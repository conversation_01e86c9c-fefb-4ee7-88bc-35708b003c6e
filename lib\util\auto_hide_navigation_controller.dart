import 'dart:async';
import 'package:flutter/material.dart';

/// Controller for managing auto-hiding behavior of app bar and bottom navigation
class AutoHideNavigationController extends ChangeNotifier {
  // Animation controllers
  late AnimationController _appBarAnimationController;
  late AnimationController _bottomNavAnimationController;
  
  // Animations
  late Animation<Offset> _appBarSlideAnimation;
  late Animation<Offset> _bottomNavSlideAnimation;
  
  // Scroll detection variables
  double _lastScrollPosition = 0.0;
  bool _isAppBarVisible = true;
  bool _isBottomNavVisible = true;
  bool _isScrolling = false;
  
  // Configuration
  static const double _scrollThreshold = 10.0; // Minimum scroll distance to trigger hide/show
  static const Duration _animationDuration = Duration(milliseconds: 250);
  static const Duration _scrollDebounceDelay = Duration(milliseconds: 100);
  
  Timer? _scrollDebounceTimer;
  
  // Getters
  bool get isAppBarVisible => _isAppBarVisible;
  bool get isBottomNavVisible => _isBottomNavVisible;
  Animation<Offset> get appBarSlideAnimation => _appBarSlideAnimation;
  Animation<Offset> get bottomNavSlideAnimation => _bottomNavSlideAnimation;
  
  /// Initialize the controller with the required TickerProvider
  void initialize(TickerProvider tickerProvider) {
    // Initialize animation controllers
    _appBarAnimationController = AnimationController(
      duration: _animationDuration,
      vsync: tickerProvider,
    );
    
    _bottomNavAnimationController = AnimationController(
      duration: _animationDuration,
      vsync: tickerProvider,
    );
    
    // Initialize slide animations
    _appBarSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, -1.0), // Slide up to hide
    ).animate(CurvedAnimation(
      parent: _appBarAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _bottomNavSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, 1.0), // Slide down to hide
    ).animate(CurvedAnimation(
      parent: _bottomNavAnimationController,
      curve: Curves.easeInOut,
    ));
    
    // Start with navigation bars visible
    _appBarAnimationController.reset();
    _bottomNavAnimationController.reset();
  }
  
  /// Handle scroll events to determine when to hide/show navigation bars
  void handleScroll(ScrollController scrollController) {
    if (!scrollController.hasClients) return;
    
    final currentScrollPosition = scrollController.position.pixels;
    final maxScrollExtent = scrollController.position.maxScrollExtent;
    final minScrollExtent = scrollController.position.minScrollExtent;
    
    // Don't hide if at the very top or bottom
    if (currentScrollPosition <= minScrollExtent + 50 || 
        currentScrollPosition >= maxScrollExtent - 50) {
      _showNavigationBars();
      return;
    }
    
    // Calculate scroll delta
    final scrollDelta = currentScrollPosition - _lastScrollPosition;
    
    // Only react to significant scroll movements
    if (scrollDelta.abs() < _scrollThreshold) {
      _lastScrollPosition = currentScrollPosition;
      return;
    }
    
    _isScrolling = true;
    
    // Debounce scroll events to avoid jittery behavior
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(_scrollDebounceDelay, () {
      _isScrolling = false;
      
      // Determine scroll direction and hide/show accordingly
      if (scrollDelta > 0) {
        // Scrolling down - hide navigation bars
        _hideNavigationBars();
      } else {
        // Scrolling up - show navigation bars
        _showNavigationBars();
      }
    });
    
    _lastScrollPosition = currentScrollPosition;
  }
  
  /// Hide both app bar and bottom navigation with animation
  void _hideNavigationBars() {
    if (!_isAppBarVisible && !_isBottomNavVisible) return;
    
    _isAppBarVisible = false;
    _isBottomNavVisible = false;
    
    _appBarAnimationController.forward();
    _bottomNavAnimationController.forward();
    
    notifyListeners();
  }
  
  /// Show both app bar and bottom navigation with animation
  void _showNavigationBars() {
    if (_isAppBarVisible && _isBottomNavVisible) return;
    
    _isAppBarVisible = true;
    _isBottomNavVisible = true;
    
    _appBarAnimationController.reverse();
    _bottomNavAnimationController.reverse();
    
    notifyListeners();
  }
  
  /// Force show navigation bars (useful for certain interactions)
  void forceShowNavigationBars() {
    _showNavigationBars();
  }
  
  /// Force hide navigation bars (useful for certain interactions)
  void forceHideNavigationBars() {
    _hideNavigationBars();
  }
  
  /// Reset to initial state (navigation bars visible)
  void reset() {
    _isAppBarVisible = true;
    _isBottomNavVisible = true;
    _lastScrollPosition = 0.0;
    
    _appBarAnimationController.reset();
    _bottomNavAnimationController.reset();
    
    notifyListeners();
  }
  
  @override
  void dispose() {
    _scrollDebounceTimer?.cancel();
    _appBarAnimationController.dispose();
    _bottomNavAnimationController.dispose();
    super.dispose();
  }
}
