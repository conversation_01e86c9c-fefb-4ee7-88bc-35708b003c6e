import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/initial_store_and_people_card.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_pagination.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/store_and_people_card/store_and_people_card.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/stores_and_people/store_and_people_pagination.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/stores_and_people/stores_and_people_bloc.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class StoresAndPeople extends StatefulWidget {
  final String reference;
  final EntityType entityType;
  final FollowEnum requiredList;
  const StoresAndPeople(
      {super.key,
      required this.reference,
      required this.entityType,
      required this.requiredList});

  @override
  State<StoresAndPeople> createState() => _StoresAndPeopleState();
}

class _StoresAndPeopleState extends State<StoresAndPeople>
    with AutomaticKeepAliveClientMixin<StoresAndPeople> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //region Bloc
  late StoreAndPeopleBloc storeAndPeopleBloc;
  //endregion

  //region Init
  @override
  void initState() {
    storeAndPeopleBloc = StoreAndPeopleBloc(
        context, widget.reference, widget.entityType, widget.requiredList);
    storeAndPeopleBloc.init();
    super.initState();
  }
  //endregion

//   color: AppColors.brandGreen,
//   onRefresh: ()async{
//   await storeAndPeopleBloc.init();
// },
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<StoreAndPeopleState>(
        stream: storeAndPeopleBloc.storeAndPeopleCtrl.stream,
        initialData: StoreAndPeopleState.Loading,
        builder: (context, snapshot) {
          //print("Data ${snapshot.data}");
          //Success
          if (snapshot.data == StoreAndPeopleState.Success) {
            return RefreshIndicator(
              color: AppColors.brandBlack,
              onRefresh: () async {
                await storeAndPeopleBloc.init();
              },
              child: ListView.builder(
                // shrinkWrap: true,
                itemCount: storeAndPeopleBloc.storesAndPeople.length + 1,
                controller: storeAndPeopleBloc.scrollController,
                // shrinkWrap: true,
                physics: const AlwaysScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  if (index < storeAndPeopleBloc.storesAndPeople.length) {
                    return StoreAndPeopleCard(
                      recommendedStoreAndUser:
                          storeAndPeopleBloc.storesAndPeople[index],
                      reference:
                          storeAndPeopleBloc.storesAndPeople[index].reference!,
                    );
                  } else {
                    return paginationProgress();
                  }
                },
              ),
            );
          }
          //Empty
          if (snapshot.data == StoreAndPeopleState.Empty) {
            return RefreshIndicator(
              color: AppColors.brandBlack,
              onRefresh: () async {
                await storeAndPeopleBloc.init();
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.width,
                  child: NoResult(message: AppStrings.noOneIsHere),
                ),
              ),
            );
            // return  NoResult(message: 'No one is here',);
          }
          //Loading
          if (snapshot.data == StoreAndPeopleState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          //Failed
          if (snapshot.data == StoreAndPeopleState.Failed) {
            return AppCommonWidgets.errorWidget(
                onTap: () {
                  storeAndPeopleBloc.init();
                },
                errorMessage: AppStrings.commonErrorMessage);
          }
          return const SizedBox();
        });
  }
//endregion

//region Pagination progress
  Widget paginationProgress() {
    return ValueListenableBuilder<StoreAndPeoplePaginationState>(
      valueListenable: storeAndPeopleBloc.paginationIsLoadingValueNotifier,
      builder: (context, data, _) {
        //Success
        if (data == StoreAndPeoplePaginationState.Success) {
          return Text("");
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                child: Text(
                  AppStrings.suggestions,
                  style: AppTextStyle.sectionHeading(
                      textColor: AppColors.appBlack),
                ),
              ),
              RecommendedStoreAndUserScreen(
                  isRecommendedStore:
                      widget.entityType == EntityType.STORE ? true : false,
                  scrollController: storeAndPeopleBloc.scrollController),
            ],
          );
          return const SizedBox(
            child: Text("Success"),
          );
        }
        //End
        if (data == StoreAndPeoplePaginationState.End) {
          return Text("");

          return const SizedBox(
            child: Text("End"),
          );
        }
        //Loading
        if (data == StoreAndPeoplePaginationState.Loading) {
          return AppCommonWidgets.appCircularProgress(
              isPaginationProgress: true);
        }
        return const SizedBox();
      },
    );
  }
//endregion
}
