import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class InfectedDevice extends StatefulWidget {
  final  String message;
  const InfectedDevice({super.key, required this.message});

  @override
  State<InfectedDevice> createState() => _InfectedDeviceState();
}

class _InfectedDeviceState extends State<InfectedDevice> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: Center(child: body()),
      ),
    );
  }


  //region Body

Widget body(){
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        Icon(Icons.warning_amber,color: AppColors.yellow,size: 200,),
        Text(widget.message,
          textAlign: TextAlign.center,
          style: AppTextStyle.introSlideDetail(textColor: AppColors.writingBlack0),)
      ],
    );
}
//endregion

}
