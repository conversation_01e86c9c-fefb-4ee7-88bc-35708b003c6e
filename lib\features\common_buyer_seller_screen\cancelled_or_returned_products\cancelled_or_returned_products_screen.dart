import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/cancelled_or_returned_products_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class CancelledOrReturnedProductsScreen extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final Order order;
  final bool isSellerView;

  const CancelledOrReturnedProductsScreen({Key? key, required this.subOrderList, required this.order, required this.isSellerView}) : super(key: key);

  @override
  State<CancelledOrReturnedProductsScreen> createState() => _CancelledOrReturnedProductsScreenState();
}

class _CancelledOrReturnedProductsScreenState extends State<CancelledOrReturnedProductsScreen> {
  //region Bloc
  late CancelledOrReturnedProductsBloc cancelledOrReturnedProductsBloc;

  //endregion
  //region Init
  @override
  void initState() {
    cancelledOrReturnedProductsBloc = CancelledOrReturnedProductsBloc(context, widget.subOrderList, widget.order, widget.isSellerView);
    super.initState();
  }

  //endregion
  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        //CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        appBar: appBar(),
        body: SafeArea(child: body()),
      ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isTitleVisible: true,
        isCustomTitle: false,
        title: AppStrings.cancelledOrReturnedProducts,
        isDefaultMenuVisible: true,
        isCartVisible: false,
        isMembershipVisible: true,
        onTapDrawer: () {
          // buyerViewStoreBloc.goToSellerAccountScreen();
        });
  }

//endregion

//region Body
  Widget body() {
    return subOrderList();
  }

//endregion

//region Sub order list
  Widget subOrderList() {
    return ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 5),
        shrinkWrap: true,
        itemCount: widget.subOrderList.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 7),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.textFieldFill1, // Replace with your desired color
                ),
              ),
            ),
            child: AppCommonWidgets.subOrderInfo(subOrder:widget.subOrderList[index],
                onTap: (){
                  cancelledOrReturnedProductsBloc.onTapProduct(subOrder: widget.subOrderList[index]);
                }, context: context,
                isCheckBoxVisible: false,
                isPriceDetailVisible: false,
                isStatusVisible: false,
                isArrowVisible: true

            ),
          );
          // return Text(widget.subOrderList[index].productName!);
        });
  }
//endregion
}
