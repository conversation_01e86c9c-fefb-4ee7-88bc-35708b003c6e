import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_add_image/selected_image_preview/buyer_selected_image_preview_bloc.dart';
import 'package:swadesic/model/product_images.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:reorderables/reorderables.dart';
import 'package:swadesic/util/common_widgets.dart';

import '../../../../util/app_constants.dart';



// region Buyer Selected Image PreviewScreen
class BuyerSelectedImagePreviewScreen extends StatefulWidget {
  //final ProductImageResponse? productImageResponse;
  // final int? productId;

  const BuyerSelectedImagePreviewScreen({Key? key}) : super(key: key);

  @override
  _BuyerSelectedImagePreviewScreenState createState() => _BuyerSelectedImagePreviewScreenState();
}
// endregion

class _BuyerSelectedImagePreviewScreenState extends State<BuyerSelectedImagePreviewScreen> {
  // region Bloc
  late BuyerSelectedImagePreviewBloc buyerSelectedImagePreviewBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerSelectedImagePreviewBloc = BuyerSelectedImagePreviewBloc(context);
    buyerSelectedImagePreviewBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,

      body: SafeArea(child: body()),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar(){
    return AppBar(
      backgroundColor: AppColors.appWhite,
      leading: CupertinoButton(
          onPressed: (){
            Navigator.pop(context);
          },
          padding: EdgeInsets.zero,
          child: SvgPicture.asset(AppImages.backButton,color: AppColors.appBlack,fit: BoxFit.fill)),
      elevation: 0,
      centerTitle: false,
      titleSpacing: 0,
      title: Text(
        AppStrings.productImage,
        style: TextStyle(
          fontSize: 19,
          fontWeight: FontWeight.w700,
          color: AppColors.appBlack,
        ),
      ),
      automaticallyImplyLeading: false,
      //region Done Button
      actions: [
        CupertinoButton(
            child: SvgPicture.asset(AppImages.done,color: AppColors.appBlack,fit: BoxFit.fill,),
            onPressed: (){
              // widget.addProductToSelectedImage!.isEmpty?
              // selectedImagePreviewBloc.goBackToAddProduct():
              Navigator.pop(context);
              // selectedImagePreviewBloc.goBackToAddProduct();

            }),
        CupertinoButton(
            onPressed: (){},
            child: SvgPicture.asset(AppImages.drawerIcon,color: AppColors.appBlack,height: 24,)),


      ],

      //endregion
    );
  }

  //endregion






  // region Body
  Widget body() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        verticalSizedBox(10),
        SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.width,

            child: bigImage()),
        //previouslyAddedImagesText(),
        verticalSizedBox(10),
        //previousImageList(),
        dragReorderText(),
        verticalSizedBox(10),
         Expanded(child: imageList()),
        verticalSizedBox(10),
      ],
    );
  }

  // endregion



  //region Big image
    Widget bigImage() {
      return StreamBuilder<ProductImages>(
          stream: buyerSelectedImagePreviewBloc.selectedImageCtrl.stream,
          builder: (context, snapshot) {
            if(!snapshot.hasData) return Container();

            if(snapshot.data!.imageUrl.isNotEmpty) {
              return SizedBox(
              width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.width,
                child: Center(
                child: Image.network(
                 snapshot.data!.imageUrl,
                  fit: BoxFit.cover,
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.width,

                ),
              ),
            );
            }

             return SizedBox(
               width: MediaQuery.of(context).size.width,
               height: MediaQuery.of(context).size.width,

               child: Center(
                 child: Image.file(
                   File(snapshot.data!.image.path),
                   fit: BoxFit.cover,
                   width: MediaQuery.of(context).size.width,
                   height: MediaQuery.of(context).size.width,


                 ),
               ),
             );
          });
    }

  //endregion


  // region Drag and reorder
  Widget dragReorderText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        AppStrings.dragAndReorder,
        style: const TextStyle(
          fontFamily: "LatoRegular",
          fontSize: 16,
          fontWeight: FontWeight.w400,
         // color: AppColors.appBlack.withOpacity(70),
        ),
      ),
    );
  }

  //endregion

  Widget imageList() {
    return StreamBuilder<bool>(
      stream: buyerSelectedImagePreviewBloc.gridViewRefreshCtrl.stream,
      builder: (context, snapshot) {
        return Center(
          child: ReorderableWrap(
            minMainAxisCount: 4,
            needsLongPressDraggable: true,

            // maxMainAxisCount: 5,
            spacing: 4,
            runSpacing: 4,
            padding: EdgeInsets.zero,
           crossAxisAlignment: WrapCrossAlignment.start,
           // crossAxisAlignment: WrapCrossAlignment,
            alignment:WrapAlignment.start,
            runAlignment: WrapAlignment.start,
              ///Reorder
            onReorder: (oldIndex, newIndex) {
              setState(() {
                final element = buyerSelectedImagePreviewBloc.productImages.removeAt(oldIndex);
                buyerSelectedImagePreviewBloc.productImages.insert(newIndex, element);
                //AppConstants.multipleSelectedImage.addAll(selectedImagePreviewBloc.) as XFile;
              });
            },
            header: [
              InkWell(
                onTap: () {
                  buyerSelectedImagePreviewBloc.goBackToAddAndEditImage();
                },
                child: SizedBox(
                  height: 85,
                  width: 85,
                  child: Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColors.lightGray2,width: 2),
                        borderRadius: const BorderRadius.all(Radius.circular(10))
                    ),
                    child: const Center(
                      child: Icon(Icons.add),
                    ),
                  ),
                ),
              ),
            ],
            children: buyerSelectedImagePreviewBloc.productImages.map((ProductImages file) {
              ///Network Image
              if(file.imageUrl.isNotEmpty) {
                return Container(
                  height: 85,
                  width: 85,
                  decoration: const BoxDecoration(
                  ),
                  key: ValueKey(file),
                  child: Stack(
                    children: [
                      InkWell(onTap: () {
                        buyerSelectedImagePreviewBloc.selectedImageCtrl.add(file);
                        // int pos = AppConstants.selectedImage.indexOf(file.image);
                        // selectedImagePreviewBloc.selectedImageCtrl.add(pos);
                      },
                          child: Center(
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(Radius.circular(10)),
                                  child: CachedNetworkImage(
                                    imageUrl: file.imageUrl,
                                    fit: BoxFit.fill,
                                      height: double.infinity,
                                      width: double.infinity,
                                    filterQuality: FilterQuality.low,
                                    placeholder: (context, url) => const Center(child: CircularProgressIndicator()),
                                    errorWidget: (context, url, error) => const Icon(Icons.error),

                                  )

                                // child: Image.network(
                                //   file.imageUrl,
                                //   fit: BoxFit.fill,
                                //   height: double.infinity,
                                //   width: double.infinity,
                                // ),
                              ))),
                      Align(
                        alignment: Alignment.topRight,
                        child: InkWell(
                            child: const Icon(Icons.close),
                            onTap: () {
                              /// Api call to remove
                              setState(() {
                                buyerSelectedImagePreviewBloc.deleteImage(buyerSelectedImagePreviewBloc.productImages.firstWhere((element) => element.imageUrl ==file.imageUrl).id);

                              });

                            }),
                      )
                    ],
                  ));
              }
              ///Local Image
              return Container(
                  height: 85,
                  width: 85,
                decoration: const BoxDecoration(
                ),
                  key: ValueKey(file),
                  child: Stack(
                    children: [
                      InkWell(onTap: () {
                        buyerSelectedImagePreviewBloc.selectedImageCtrl.add(file);
                        // int pos = AppConstants.selectedImage.indexOf(file.image);
                        // selectedImagePreviewBloc.selectedImageCtrl.add(pos);
                      },
                          child: Center(
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(Radius.circular(10)),
                                child: Image.file(
                                  File(file.image.path),
                                  fit: BoxFit.fill,
                                  height: double.infinity,
                                  width: double.infinity,
                                ),
                              ))),
                      Align(
                        alignment: Alignment.topRight,
                        child: InkWell(
                            child: const Icon(Icons.close),
                            onTap: () {
                              setState(() {
                                buyerSelectedImagePreviewBloc.productImages.remove(file);
                                AppConstants.multipleSelectedImage.remove(file.image);
                                buyerSelectedImagePreviewBloc.selectedImageCtrl.sink.add(buyerSelectedImagePreviewBloc.productImages.first);
                              });
                            }),
                      )
                    ],
                  ));
            }).toList(),
          ),
        );
      }
    );
  }

//endregion









}
