import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Invalid screen
class InvalidScreen extends StatefulWidget {
  final bool? isAppBarVisible ;
  const InvalidScreen({super.key, this.isAppBarVisible = false});

  @override
  State<InvalidScreen> createState() => _InvalidScreenState();
}
//endregion

class _InvalidScreenState extends State<InvalidScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: widget.isAppBarVisible! ? appBar() : null,
      body: body(),
    );
  }


  //
  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,

      isMembershipVisible: false,
      isCartVisible: false,
      isDefaultMenuVisible: true,
      title: "",

    );
  }

  //endregion


  //region Body
Widget body(){
    return Container(
      alignment: Alignment.center,
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,

            children: [
              SvgPicture.asset(AppImages.storeIcon,height: 75,width: 75,),
              horizontalSizedBox(12),
              SvgPicture.asset(AppImages.invalidProfile,height: 75,width: 75,),

            ],
          ),
          verticalSizedBox(12),
          Row(
            mainAxisSize: MainAxisSize.min,

            children: [
              SvgPicture.asset(AppImages.invalidProduct,height: 75,width: 75,),
              horizontalSizedBox(12),
              SvgPicture.asset(AppImages.invalidQuestion,height: 75,width: 75,),

            ],
          ),
          verticalSizedBox(30),
          Text(AppStrings.weCouldNotFound,
            textAlign: TextAlign.center,
            style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack0),),
          verticalSizedBox(10),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(AppStrings.pleaseRecheck,
                textAlign: TextAlign.center,
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),),
              horizontalSizedBox(3),
              SvgPicture.asset(AppImages.smileEmoji),

            ],
          )


        ],
      ),
    );
}
//endregion

}
