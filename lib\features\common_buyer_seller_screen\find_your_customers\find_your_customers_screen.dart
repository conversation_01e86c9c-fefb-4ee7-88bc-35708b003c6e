import 'package:app_settings/app_settings.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customer_card/find_your_customer_card_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/follow_and_support_all_button/fllow_and_support_all_button.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/features/widgets/recommended_and_followers_card/recommanded_and_followers_card.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class FindYourCustomersScreen extends StatefulWidget {
  // final String reference;
  final bool visibleNext;
  final String? title;
  final bool isFromSearch;
  final bool iSFromOnboarding;

  const FindYourCustomersScreen(
      {super.key,
      // required this.reference,
      this.visibleNext = false,
      this.title,
      this.isFromSearch = false,
      this.iSFromOnboarding = false});

  @override
  State<FindYourCustomersScreen> createState() =>
      _FindYourCustomersScreenState();
}

class _FindYourCustomersScreenState extends State<FindYourCustomersScreen>
    with AutomaticKeepAliveClientMixin<FindYourCustomersScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //Bloc
  late FindYourCustomersBloc findYourCustomersBloc;

  //App state
  AppLifecycleState? _previousAppState;

  //region Init
  @override
  void initState() {
    super.initState();
    findYourCustomersBloc = FindYourCustomersBloc(
        context,
        // widget.reference,
        widget.iSFromOnboarding);
    findYourCustomersBloc.init();
    // WidgetsBinding.instance.addObserver(this);
  }

  //endregion

  //region Did change app lifecycle change
  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   super.didChangeAppLifecycleState(state);
  //   // if (_previousAppState == AppLifecycleState.inactive &&
  //   //     state == AppLifecycleState.resumed) {
  //   //   // This block is executed when the app is resumed from the background to the foreground
  //   //   //print('App resumed from background');
  //   //   // Perform actions or call methods specific to background-to-foreground transition
  //   //   // For example, check permission or refresh data
  //   //   // findYourCustomersBloc.requestPermission();
  //   // }
  //   // _previousAppState = state;
  // }

  //endregion

  //region Dispose
  @override
  void dispose() {
    super.dispose();
    // WidgetsBinding.instance.removeObserver(this);
    findYourCustomersBloc.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: widget.isFromSearch ? syncButton() : null,
      appBar: widget.title != null ? appBar() : null,
      body: body(),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: "${widget.title}",
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
        isTextButtonVisible: widget.visibleNext,
        onTapTextButton: () {
          findYourCustomersBloc.goToBuyerBottomNavigation();
        },
        textButtonWidget: AppCommonWidgets.appBarTextButtonText(
          text: AppStrings.next,
        ));
  }

  //endregion

  //region Body
  Widget body() {
    return StreamBuilder<FindYourCustomersState>(
        stream: findYourCustomersBloc.findYourCustomerStateCtrl.stream,
        initialData: FindYourCustomersState.Loading,
        builder: (context, snapshot) {
          //print("State is ${snapshot.data}");
          // return RefreshIndicator(
          //   color: AppColors.brandGreen,
          //   onRefresh: () async {
          //     await findYourCustomersBloc.init();
          //   },
          //   child: SingleChildScrollView(
          //     physics: const AlwaysScrollableScrollPhysics(),
          //     child: Container(
          //       alignment: Alignment.center,
          //       height: MediaQuery.of(context).size.width,
          //       child:  NoResult(message: AppStrings.noContactsInPhone),
          //     ),
          //   ),
          // );

          //Success
          if (snapshot.data == FindYourCustomersState.Success ||
              snapshot.data == FindYourCustomersState.Search_Empty) {
            return Column(
              children: [
                widget.title != null
                    ? Padding(
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          children: [
                            Flexible(
                              child: AppSearchField(
                                textEditingController:
                                    findYourCustomersBloc.searchTextCtrl,
                                isActive: true,
                                hintText: AppStrings.search,
                                onChangeText: (value) {
                                  // findYourCustomersBloc.onChangeText(data: value);
                                  // buyerSearchBloc.onChangeTextField();
                                },
                                onTapSuffix: () {
                                  findYourCustomersBloc.searchTextCtrl.clear();
                                  CommonMethods.closeKeyboard(context);
                                },
                                onSubmit: () {},
                              ),
                            ),
                            CupertinoButton(
                                padding: EdgeInsets.zero,
                                onPressed: () async {
                                  CommonMethods.appSnackBar(
                                      AppStrings.syncingContacts);

                                  findYourCustomersBloc.refreshKey.currentState
                                      ?.show();
                                  findYourCustomersBloc.syncStatus =
                                      AppBoolEnum.TRUE;

                                  await findYourCustomersBloc.init();
                                },
                                child: SvgPicture.asset(
                                  AppImages.contactSync,
                                ))
                          ],
                        ),
                      )
                    : const SizedBox(),
                //Message
                Visibility(
                  visible: widget.title != null,
                  child: Container(
                    margin:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
                    child: Text(
                      AppStrings.youWillAlsoSeeStores,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack0),
                    ),
                  ),
                ),
                Expanded(
                    child: findYourCustomersBloc.searchResult.isEmpty
                        ? Center(child: Text(AppStrings.noResults))
                        : userAndStoreInfoList()),
                //If title is null then return container or else the buttons
                widget.title != null
                    ? FollowAndSupportAllButton(
                        findYourCustomersBloc: findYourCustomersBloc,
                        reference: AppConstants.appData.isUserView!
                            ? AppConstants.appData.userReference!
                            : AppConstants.appData.storeReference!,
                      )
                    : const SizedBox()
              ],
            );
          }
          //Permission permanently denied
          if (snapshot.data ==
              FindYourCustomersState.PermissionPermanatlyDenied) {
            return AppCommonWidgets.errorWidget(
                errorMessage: AppStrings.openSettingToAllowPermission,
                onTap: () {
                  //Ask one more time if false again then open setting
                  AppSettings.openAppSettings().then((value) {
                    //Change state to permission denied
                    findYourCustomersBloc.findYourCustomerStateCtrl.sink
                        .add(FindYourCustomersState.PermissionDenied);
                  });
                });
          }
          //Permission denied
          if (snapshot.data == FindYourCustomersState.PermissionDenied) {
            return AppCommonWidgets.errorWidget(
                errorMessage: AppStrings.allowContactPermission,
                onTap: () {
                  findYourCustomersBloc.requestPermission();
                });
          }
          //Loading
          if (snapshot.data == FindYourCustomersState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          //EMPTY
          if (snapshot.data == FindYourCustomersState.Empty) {
            return RefreshIndicator(
              color: AppColors.brandBlack,
              onRefresh: () async {
                await findYourCustomersBloc.init();
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Container(
                  alignment: Alignment.center,
                  height: MediaQuery.of(context).size.width,
                  child: const NoResult(message: AppStrings.noContactsInPhone),
                ),
              ),
            );
          }

          //Failed
          if (snapshot.data == FindYourCustomersState.Failed) {
            return AppCommonWidgets.errorWidget(
                errorMessage: AppStrings.commonErrorMessage,
                onTap: () {
                  findYourCustomersBloc.init();
                });
          }
          return const SizedBox();
        });
  }

//endregion

  //Sync button
  Widget syncButton() {
    return StreamBuilder<FindYourCustomersState>(
        stream: findYourCustomersBloc.findYourCustomerStateCtrl.stream,
        initialData: FindYourCustomersState.Loading,
        builder: (context, snapshot) {
          //Success
          if (snapshot.data == FindYourCustomersState.Success ||
              snapshot.data == FindYourCustomersState.Search_Empty) {
            return FloatingActionButton(
              onPressed: () async {
                CommonMethods.appSnackBar(AppStrings.syncingContacts);
                findYourCustomersBloc.refreshKey.currentState?.show();
                findYourCustomersBloc.syncStatus = AppBoolEnum.TRUE;
                await findYourCustomersBloc.init();
              },
              backgroundColor: AppColors.brandBlack,
              child: SvgPicture.asset(
                AppImages.contactSync,
                color: AppColors.appWhite,
              ),
            );
          }
          return const SizedBox();
        });
  }
  //endregion

//region User and store info list
  Widget userAndStoreInfoList() {
    return RefreshIndicator(
      key: findYourCustomersBloc.refreshKey,
      color: AppColors.brandBlack,
      onRefresh: () async {
        findYourCustomersBloc.syncStatus = AppBoolEnum.FALSE;
        await findYourCustomersBloc.init();
      },
      child: Scrollbar(
        child: ListView.builder(
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: findYourCustomersBloc.searchResult.length,
            itemBuilder: (context, index) {
              return RecommendedAndFollowers(
                cardImage: findYourCustomersBloc.searchResult[index].icon,
                onTap: () {
                  findYourCustomersBloc.onTapFollowAndSupport(
                      contactUserAndStoreInfo:
                          findYourCustomersBloc.searchResult[index]);
                },
                customImageContainerType:
                    findYourCustomersBloc.searchResult[index].entityType ==
                            EntityType.STORE.name
                        ? CustomImageContainerType.store
                        : CustomImageContainerType.user,
                heading: findYourCustomersBloc.searchResult[index].name!,
                title: findYourCustomersBloc.searchResult[index].handle!,
                followStatus:
                    findYourCustomersBloc.searchResult[index].followStatus!,
              );

              // return FindYourCustomerUserScreen(
              //   findYourCustomersBloc: findYourCustomersBloc,
              //   contactUserAndStoreInfo: findYourCustomersBloc.searchResult[index],
              //   reference: findYourCustomersBloc.reference,
              // );
              //If register/un-register
              /*
              // 1. reference_type == user || reference_type == contact
              //  */
              // if (findYourCustomersBloc.searchResult[index].referenceType == 'user' ||
              //     findYourCustomersBloc.searchResult[index].referenceType == 'contact') {
              //
              // }
              // return Text("hello");
            }),
      ),
    );
  }
//endregion
}
