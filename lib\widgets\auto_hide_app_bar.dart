import 'package:flutter/material.dart';
import 'package:swadesic/util/auto_hide_navigation_controller.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_colors.dart';

/// A wrapper widget that provides auto-hiding functionality for the app bar
class AutoHideAppBar extends StatefulWidget implements PreferredSizeWidget {
  // All the parameters from the original mainAppBar
  final BuildContext context;
  Color? backgroundColor;
  Color? leadingIconColor;
  final dynamic onTapLeading;
  final bool isLeadingVisible;
  final String leadingIcon;
  final bool isCustomLeadingIcon;
  final bool isTitleVisible;
  final bool isCustomTitle;
  final String title;
  final Widget customTitleWidget;
  final bool isCenterTitle;
  final bool isMembershipVisible;
  final bool isCartVisible;
  final bool isTextButtonVisible;
  final Widget textButtonWidget;
  final dynamic onTapTextButton;
  final bool isDefaultMenuVisible;
  final dynamic onTapReport;
  final dynamic onTapDrawer;
  final bool isCustomMenuVisible;
  final Widget customMenuButton;
  final bool isDropdownVisible;
  final bool isRotateDropdownIcon;
  final dynamic onTapDropdown;
  final bool isFilterVisible;
  final dynamic onTapFilter;
  final dynamic onTapCart;

  // Auto-hide specific parameters
  final AutoHideNavigationController? autoHideController;
  final bool enableAutoHide;

  AutoHideAppBar({
    Key? key,
    required this.context,
    this.backgroundColor,
    this.leadingIconColor,
    this.onTapLeading,
    this.isLeadingVisible = true,
    this.leadingIcon = '',
    this.isCustomLeadingIcon = false,
    this.isTitleVisible = true,
    this.isCustomTitle = false,
    this.title = "",
    this.customTitleWidget = const SizedBox(),
    this.isCenterTitle = false,
    this.isMembershipVisible = true,
    this.isCartVisible = true,
    this.isTextButtonVisible = false,
    this.textButtonWidget = const SizedBox(),
    this.onTapTextButton,
    this.isDefaultMenuVisible = true,
    this.onTapReport,
    this.onTapDrawer,
    this.isCustomMenuVisible = false,
    this.customMenuButton = const SizedBox(),
    this.isDropdownVisible = false,
    this.isRotateDropdownIcon = false,
    this.onTapDropdown,
    this.isFilterVisible = false,
    this.onTapFilter,
    this.onTapCart,
    this.autoHideController,
    this.enableAutoHide = true,
  }) : super(key: key);

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  State<AutoHideAppBar> createState() => _AutoHideAppBarState();
}

class _AutoHideAppBarState extends State<AutoHideAppBar> {
  @override
  Widget build(BuildContext context) {
    // Create the standard app bar using the existing mainAppBar method
    final appBar = AppCommonWidgets.mainAppBar(
      context: widget.context,
      backgroundColor: widget.backgroundColor ?? AppColors.appWhite,
      leadingIconColor: widget.leadingIconColor ?? AppColors.appBlack,
      onTapLeading: widget.onTapLeading,
      isLeadingVisible: widget.isLeadingVisible,
      leadingIcon: widget.leadingIcon,
      isCustomLeadingIcon: widget.isCustomLeadingIcon,
      isTitleVisible: widget.isTitleVisible,
      isCustomTitle: widget.isCustomTitle,
      title: widget.title,
      customTitleWidget: widget.customTitleWidget,
      isCenterTitle: widget.isCenterTitle,
      isMembershipVisible: widget.isMembershipVisible,
      isCartVisible: widget.isCartVisible,
      isTextButtonVisible: widget.isTextButtonVisible,
      textButtonWidget: widget.textButtonWidget,
      onTapTextButton: widget.onTapTextButton,
      isDefaultMenuVisible: widget.isDefaultMenuVisible,
      onTapReport: widget.onTapReport,
      onTapDrawer: widget.onTapDrawer,
      isCustomMenuVisible: widget.isCustomMenuVisible,
      customMenuButton: widget.customMenuButton,
      isDropdownVisible: widget.isDropdownVisible,
      isRotateDropdownIcon: widget.isRotateDropdownIcon,
      onTapDropdown: widget.onTapDropdown,
      isFilterVisible: widget.isFilterVisible,
      onTapFilter: widget.onTapFilter,
      onTapCart: widget.onTapCart,
    );

    // If auto-hide is disabled or no controller provided, return standard app bar
    if (!widget.enableAutoHide || widget.autoHideController == null) {
      return appBar;
    }

    // Wrap the app bar with slide animation
    return AnimatedBuilder(
      animation: widget.autoHideController!,
      builder: (context, child) {
        return SlideTransition(
          position: widget.autoHideController!.appBarSlideAnimation,
          child: appBar,
        );
      },
    );
  }
}

/// Extension to easily create auto-hide app bars
extension AppCommonWidgetsAutoHide on AppCommonWidgets {
  /// Create an auto-hiding app bar with the same parameters as mainAppBar
  static AutoHideAppBar autoHideAppBar({
    required BuildContext context,
    Color? backgroundColor,
    Color? leadingIconColor,
    dynamic onTapLeading,
    bool isLeadingVisible = true,
    String leadingIcon = '',
    bool isCustomLeadingIcon = false,
    bool isTitleVisible = true,
    bool isCustomTitle = false,
    String title = "",
    Widget customTitleWidget = const SizedBox(),
    bool isCenterTitle = false,
    bool isMembershipVisible = true,
    bool isCartVisible = true,
    bool isTextButtonVisible = false,
    Widget textButtonWidget = const SizedBox(),
    dynamic onTapTextButton,
    bool isDefaultMenuVisible = true,
    dynamic onTapReport,
    dynamic onTapDrawer,
    bool isCustomMenuVisible = false,
    Widget customMenuButton = const SizedBox(),
    bool isDropdownVisible = false,
    bool isRotateDropdownIcon = false,
    dynamic onTapDropdown,
    bool isFilterVisible = false,
    dynamic onTapFilter,
    dynamic onTapCart,
    AutoHideNavigationController? autoHideController,
    bool enableAutoHide = true,
  }) {
    return AutoHideAppBar(
      context: context,
      backgroundColor: backgroundColor ?? AppColors.appWhite,
      leadingIconColor: leadingIconColor ?? AppColors.appBlack,
      onTapLeading: onTapLeading,
      isLeadingVisible: isLeadingVisible,
      leadingIcon: leadingIcon,
      isCustomLeadingIcon: isCustomLeadingIcon,
      isTitleVisible: isTitleVisible,
      isCustomTitle: isCustomTitle,
      title: title,
      customTitleWidget: customTitleWidget,
      isCenterTitle: isCenterTitle,
      isMembershipVisible: isMembershipVisible,
      isCartVisible: isCartVisible,
      isTextButtonVisible: isTextButtonVisible,
      textButtonWidget: textButtonWidget,
      onTapTextButton: onTapTextButton,
      isDefaultMenuVisible: isDefaultMenuVisible,
      onTapReport: onTapReport,
      onTapDrawer: onTapDrawer,
      isCustomMenuVisible: isCustomMenuVisible,
      customMenuButton: customMenuButton,
      isDropdownVisible: isDropdownVisible,
      isRotateDropdownIcon: isRotateDropdownIcon,
      onTapDropdown: onTapDropdown,
      isFilterVisible: isFilterVisible,
      onTapFilter: onTapFilter,
      onTapCart: onTapCart,
      autoHideController: autoHideController,
      enableAutoHide: enableAutoHide,
    );
  }
}
