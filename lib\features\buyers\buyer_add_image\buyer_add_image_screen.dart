import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_add_image/buyer_add_image_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';



class BuyerAddImageScreen extends StatefulWidget {
  // final bool? selectSingleImage;
  const BuyerAddImageScreen({Key? key}) : super(key: key);

  @override
  _BrandNameScreenState createState() => _BrandNameScreenState();
}

class _BrandNameScreenState extends State<BuyerAddImageScreen> {
  //region Bloc
  late BuyerAddImageBloc buyerAddImageBloc;

  //endregion
  //region Init
  @override
  void initState() {

    buyerAddImageBloc = BuyerAddImageBloc(context);
    buyerAddImageBloc.init();
    super.initState();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: body(),
    );
  }


  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title:AppStrings.productImage,
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
    );
  }

//endregion

  //region AppBar
  // AppBar appBar(){
  //   return AppBar(
  //
  //     backgroundColor: AppColors.white,
  //     leading: CupertinoButton(
  //         onPressed: (){
  //           Navigator.pop(context);
  //         },
  //
  //         padding: EdgeInsets.zero,
  //         child: SvgPicture.asset(AppImages.backButton,color: AppColors.appBlack,fit: BoxFit.fill)),
  //     elevation: 0,
  //     centerTitle: false,
  //     titleSpacing: 0,
  //
  //     title: Text(
  //       AppStrings.productImage,
  //       style: TextStyle(
  //         fontSize: 19,
  //         fontWeight: FontWeight.w700,
  //         color: AppColors.appBlack,
  //       ),
  //     ),
  //     automaticallyImplyLeading: false,
  //
  //     actions: [
  //
  //       CupertinoButton(
  //           onPressed: (){},
  //           child: SvgPicture.asset(AppImages.drawerIcon,color: AppColors.appBlack,height: 24,)),
  //
  //
  //     ],
  //
  //     //endregion
  //   );
  // }

  //endregion

  //region body
  Widget body(){
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        takePhoto(),
        verticalSizedBox(40),
        uploadImageFromPhone(),
        verticalSizedBox(40),

        //addFromPrevious(),

      ],
    );
  }
//endregion



  //region Take A Photo
  Widget takePhoto(){
    return TextButton(
      onPressed: (){
        buyerAddImageBloc.openCamera();
        // widget.selectSingleImage!=null?addImageBloc.openCamera():addImageBloc.openGallerySingleImage();
      },
      child: Padding(
        padding:  const EdgeInsets.symmetric(horizontal: 40),
        child: Container(
          height: 55,
          decoration: BoxDecoration(
              color: AppColors.primaryGreen,
              borderRadius: BorderRadius.all(Radius.circular(40))
          ),
          child: Center(child: Text(AppStrings.takePhoto,style: TextStyle(color: AppColors.appBlack),)),
        ),
      ),
    );
  }
  //endregion




  //region Upload Image From Phone
  Widget uploadImageFromPhone(){
    return TextButton(
      onPressed: (){
        buyerAddImageBloc.openGallery();
       // widget.selectSingleImage!=null?addImageBloc.openGallerySingleImage(): addImageBloc.openGallery();
      },
      child: Padding(
        padding:  const EdgeInsets.symmetric(horizontal: 40),
        child: Container(
          height: 55,
          decoration: BoxDecoration(
              color: AppColors.primaryGreen,
              borderRadius: BorderRadius.all(Radius.circular(40))
          ),
          child: Center(child: Text(AppStrings.uploadImageFromPhone,style: TextStyle(color: AppColors.appBlack),)),
        ),
      ),
    );
  }
  //endregion




  //region Add from Previous product
  Widget addFromPrevious(){
    return Padding(
      padding:  const EdgeInsets.symmetric(horizontal: 40),
      child: Container(
        height: 55,
        decoration: BoxDecoration(
            color: AppColors.primaryGreen,
            borderRadius: BorderRadius.all(Radius.circular(40))
        ),
        child: Center(child: Text(AppStrings.addFromPreviousProduct)),
      ),
    );
  }
  //endregion







}
