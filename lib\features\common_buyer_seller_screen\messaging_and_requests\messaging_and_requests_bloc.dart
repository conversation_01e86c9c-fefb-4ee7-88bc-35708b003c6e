// import 'dart:async';
//
// import 'package:flutter/material.dart';
// import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
// import 'package:swadesic/features/common_buyer_seller_screen/share_store_bottom_sheet_screen/share_store_bottom_sheet_screen.dart';
// import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
// import 'package:swadesic/model/store_info/store_info.dart';
// import 'package:swadesic/util/app_colors.dart';
// import 'package:swadesic/util/app_constants.dart';
// import 'package:swadesic/util/app_strings.dart';
//
// class MessagingAndRequestBloc{
//   //region Common variable
//   late BuildContext context;
//   final selectedOption= 0;
//
//   //endregion
// //region Controller
//   final selectedOptionCtrl = StreamController<int>.broadcast();
//
//
// //endregion
//   //region Constructor
//   MessagingAndRequestBloc(this.context);
//   //endregion
// //region Init
//   void init(){
//
//   }
// //endregion
//
//
//
//   //region On tap
//
// //region Dispose
//   void dispose(){
//
//   }
// //endregion
//
//
//
//
// //region On tap messageAndRequest
// void onTapMessaegAdnRequest({required int data}){
//   selectedOptionCtrl.sink.add(data);
// }
// //endregion
// }