import 'dart:async';
import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/seller_home_service/seller_home_service.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/store_user_product_navigation/store_user_product_navigation.dart';
import 'package:encrypt/encrypt.dart' as enc;
enum UserOrStoreNotificationState { Loading, Success, Failed, Empty }
enum UserOrStoreNotificationPaginationState { Loading, Success, Failed, Empty, End }

class UserOrStoreNotificationBloc {
  // region Common Variables
  BuildContext context;
  bool isPaginationEnded = false;
  int offset = 0;
  int limit = 1000;
  late ScrollController scrollController = ScrollController();
  late GetNotificationResponse userOrStoreNotificationResponse = GetNotificationResponse();
  static late StoreListResponse storeListResponse;

  //User or store notification data model
  late UserOrStoreNotificationDataModel userOrStoreNotificationDataModel;
  final Function(int) unSeenCount;

  // endregion

  //region Controller
  final userOrStoreNotificationCtrl = StreamController<UserOrStoreNotificationState>.broadcast();
  final tabRefreshCtrl = StreamController<bool>.broadcast();
  ValueNotifier<UserOrStoreNotificationPaginationState> userOrStoreNotificationPaginationStateCtrl = ValueNotifier(UserOrStoreNotificationPaginationState.Success);


  // region | Constructor |
  UserOrStoreNotificationBloc(this.context, this.unSeenCount);

  // endregion

  // region Init
  init() {
    //User or store notification data model initialize
    userOrStoreNotificationDataModel = Provider.of<UserOrStoreNotificationDataModel>(context, listen: false);
    //Get  store list create dby user
    getStoreListCreatedByUser();
    //For ground
    forGroundNotification();
    //Get all store notification
    getUserOrStoreNotification();
  }

// endregion

  //region Get StoreList created by user
  void getStoreListCreatedByUser() async {
    try {
      storeListResponse = await SellerHomeService().getSellerStore();
    } on ApiErrorResponseMessage {
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion


  ///1
  //region For ground notification
  void forGroundNotification() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      //If not mounted
      if (context.mounted) {
        //Get User or store notification Notification
        getUserOrStoreNotification();
      }
    });
  }

  //endregion

  ///2
  //region Get User or store notification Notification
  Future<void> getUserOrStoreNotification() async {
    //region Try
    try {
      //Api call
      userOrStoreNotificationResponse = await NotificationService().getUserOrStoreNotification(limit: limit,offset: offset);
      //Add notification data to data model
      userOrStoreNotificationDataModel.addAllStoreNotification(notificationData: userOrStoreNotificationResponse);
      //Update un seen count in call back
      unSeenCount(userOrStoreNotificationResponse.notSeenCount!);
      //Empty
      if (userOrStoreNotificationResponse.notifications!.isEmpty) {
        userOrStoreNotificationCtrl.sink.add(UserOrStoreNotificationState.Empty);
        return;
      }
      //Success
      context.mounted ? userOrStoreNotificationCtrl.sink.add(UserOrStoreNotificationState.Success) : null;
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //Failed
      userOrStoreNotificationCtrl.sink.add(UserOrStoreNotificationState.Failed);
      CommonMethods.toastMessage(error.message!, context);
      //CommonMethods.snackBar(error.message!, context);
    } catch (error) {
      //print(error);
      //Failed
      userOrStoreNotificationCtrl.sink.add(UserOrStoreNotificationState.Failed);
    }
  }

  //endregion

  //region Update Notification
  updateNotification({required NotificationDetail notificationDetail}) async {

    //region Try
    try {
      //If product is seen then return
      if (notificationDetail.notificationStatus == "NOT_SEEN") {
        //Update ui is seen
        userOrStoreNotificationDataModel.markNotificationAsSeen(notificationDetail: notificationDetail);
        //Api call
        // await NotificationService().updateNotification(notificationReference: [notificationDetail.notificationReference!], notifiedUserOrStore:notificationDetail.notifiedUser!);
         NotificationService().updateNotification(
            body: { "notification_list":[notificationDetail.notificationReference!], "notified_user":[notificationDetail.notifiedUser!],"is_all_notifications": false }
        );
      }
      //If notification contains a url
      navigateToScreen(notificationDetail: notificationDetail);

      // if(notificationDetail.externalUrl != null){
      //   context.mounted?CommonMethods.opeAppWebView(webUrl: notificationDetail.externalUrl!, context: context):null;
      //   return;
      // }else{
      //   //Navigate
      //   navigateToScreen(notificationDetail: notificationDetail);
      // }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted?CommonMethods.toastMessage(error.message!, context):null;
      //CommonMethods.snackBar(error.message!, context);
    } catch (error) {
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }

  //endregion

  //region Navigation
  navigateToScreen({required NotificationDetail notificationDetail}) async{
    ///If notification contains "PAYOUT_OTP"
    if(notificationDetail.notificationType == "PAYOUT_OTP"){
      return;
    }
    ///1
    //Check if user view then directly push to the appropriate screen
    if(AppConstants.appData.isUserView!){
      //If actionPage is not null
      if (notificationDetail.actionPage != null) {
        //Navigate
        StoreUserProductNavigation().goToScreen(actionPage:notificationDetail.actionPage! );
        return;
      } else {
        //Navigation
        StoreUserProductNavigation().navigateToStoreProductAndStore(references: notificationDetail.notificationAbout!);
        return;
      }
    }
    //Else switch to buyer and then switch
    else{
      //Switch account with notification tab navigation
      CommonMethods.switchToBuyer(context: context, switchToNotificationTab: true);
      //Show message that switching to the another store
      context.mounted?CommonMethods.toastMessage("Switching to user account", AppConstants.userStoreCommonBottomNavigationContext,toastShowTimer: 3):null;

      //Wait for account switch to complete before navigation
      await Future.delayed(const Duration(milliseconds: 800));

      //Navigate to the appropriate screen immediately after account switch
      //If actionPage is not null
      if (notificationDetail.actionPage != null) {
        //Navigate
        StoreUserProductNavigation().goToScreen(actionPage:notificationDetail.actionPage! );
        return;
      } else {
        //Navigation
        StoreUserProductNavigation().navigateToStoreProductAndStore(references: notificationDetail.notificationAbout!);
        return;
      }

    }
  }

  //endregion



  //region Go to comment
  void goToComment({required String commentOrPostReference, required String notificationEnumns}) {
    var screen = SinglePostViewScreen(postReference:commentOrPostReference ,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
  //endregion

//region Dispose
  void dispose() {}
//endregion




  // demoMeth() {
  //   // Define the plaintext message
  //   final plainText = 'Hello';
  //
  //   // Generate a key for AES encryption from a UTF-8 encoded string
  //   final key = enc.Key.fromUtf8('R4tY7sWqP2zE5xV8cF3nM6bT9gU1iO0l');
  //
  //   // Generate an Initialization Vector (IV) with a length of 16 bytes
  //   final iv = enc.IV.fromLength(16);
  //
  //   // Create an Encrypter object using AES encryption algorithm and the generated key
  //   final encrypter = enc.Encrypter(enc.AES(key));
  //
  //   // Encrypt the plaintext message using the Encrypter object and the IV
  //   final encrypted = encrypter.encrypt(plainText, iv: iv);
  //
  //   // Decrypt the encrypted message using the Encrypter object and the IV
  //   final decrypted = encrypter.decrypt(encrypted, iv: iv);
  //
  //   // Print the decrypted plaintext message
  //   //print(decrypted); // Lorem ipsum dolor sit amet, consectetur adipiscing elit
  //
  //   // Print the encrypted ciphertext encoded in Base64
  //   //print(encrypted.base64); // R4PxiU3h8YoIRqVowBXm36ZcCeNeZ4s1OvVBTfFlZRdmohQqOpPQqD1YecJeZMAop/hZ4OxqgC1WtwvX/hP9mw==
  // }


  // String encryptMessage(String message, String key) {
  //   final plainText = encrypt.PaddedBlockCipherParameters<encrypt.Encrypter, encrypt.Encrypter>(
  //     encrypt.Encrypter(encrypt.AES(encrypt.Key.fromUtf8(key))),
  //     null,
  //   );
  //   final encrypter = encrypt.Encrypter(plainText);
  //   final encrypted = encrypter.encrypt(message, iv: encrypt.IV.fromLength(16));
  //   return base64.encode(encrypted.bytes);
  // }
  //
  // String decryptMessage(String encryptedMessage, String key) {
  //   final encrypted = encrypt.Encrypted.fromBase64(encryptedMessage);
  //   final plainText = encrypt.PaddedBlockCipherParameters<encrypt.Encrypter, encrypt.Encrypter>(
  //     encrypt.Encrypter(encrypt.AES(encrypt.Key.fromUtf8(key))),
  //     null,
  //   );
  //   final decrypter = encrypt.Encrypter(plainText);
  //   final decrypted = decrypter.decrypt(encrypted, iv: encrypt.IV.fromLength(16));
  //   return decrypted;
  // }
  //



}
