import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/buyer_cancelling_order/buyer_cancelling_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/refund_amount_calculation/refund_amount_calculation.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class BuyerCancellingOrderScreen extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order store;
  const BuyerCancellingOrderScreen(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.store})
      : super(key: key);

  @override
  State<BuyerCancellingOrderScreen> createState() =>
      _BuyerCancellingOrderScreenState();
}

class _BuyerCancellingOrderScreenState
    extends State<BuyerCancellingOrderScreen> {
  // region Bloc
  late BuyerCancellingOrderBloc buyerCancellingOrderBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerCancellingOrderBloc = BuyerCancellingOrderBloc(
        context, widget.buyerSubOrderBloc, widget.store, widget.subOrderList);
    buyerCancellingOrderBloc.init();
    super.initState();
  }
  // endregion

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: buyerCancellingOrderBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return SingleChildScrollView(
              child: SingleChildScrollView(
            child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  cancellingOrder(),
                  productDropDown(),

                  ///Proceed to cancel
                  proceedToCancel(),

                  AppCommonWidgets.bottomListSpace(context: context)
                ]),
          ));
        });
  }

  //region Cancelling order
  Widget cancellingOrder() {
    return Container(
      padding: const EdgeInsets.all(10),
      child: BuyerMyOrderCommonWidgets.buyerBottomSheetSubTitle(
          title: AppStrings.cancellingOrder),
    );
  }
//endregion

  //region Product dropdown
  Widget productDropDown() {
    return StreamBuilder<bool>(
        stream: buyerCancellingOrderBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              ///Drop down
              InkWell(
                onTap: () {
                  buyerCancellingOrderBloc.onTapProductList();
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 6.5),
                  decoration: BoxDecoration(
                      color: AppColors.lightestGrey2,
                      borderRadius: BorderRadius.circular(7)),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ///Drop down
                      Container(
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          children: [
                            //Grand total
                            Text(
                              AppStrings.productList,
                              style: AppTextStyle.settingHeading1(
                                  textColor: AppColors.appBlack),
                            ),
                            Expanded(child: horizontalSizedBox(10)),
                            buyerCancellingOrderBloc
                                    .isProductListDropDownVisible
                                ? RotatedBox(
                                    quarterTurns: 3,
                                    child: RotatedBox(
                                        quarterTurns: 4,
                                        child: SvgPicture.asset(
                                          AppImages.arrow3,
                                          color: AppColors.appBlack,
                                        )),
                                  )
                                : RotatedBox(
                                    quarterTurns: 1,
                                    child: SvgPicture.asset(
                                      AppImages.arrow3,
                                      color: AppColors.appBlack,
                                    ))
                          ],
                        ),
                      ),

                      ///List of product
                      Visibility(
                          visible: buyerCancellingOrderBloc
                              .isProductListDropDownVisible,
                          child: subOrderList()),

                      ///Selected product count
                      Visibility(
                        visible: !buyerCancellingOrderBloc
                            .isProductListDropDownVisible,
                        child: Container(
                            alignment: Alignment.centerLeft,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 10),
                            child: Text(
                              "${CommonMethods.returnHowManySubOrdersSelected(subOrderList: buyerCancellingOrderBloc.subOrderList) == 0 ? "No" : CommonMethods.returnHowManySubOrdersSelected(subOrderList: buyerCancellingOrderBloc.subOrderList)} ${CommonMethods.returnHowManySubOrdersSelected(subOrderList: buyerCancellingOrderBloc.subOrderList) == 1 ? "suborder" : "suborders"} selected",
                              style: AppTextStyle.settingText(
                                  textColor: AppColors.appBlack),
                            )),
                      )
                    ],
                  ),
                ),
              ),
              verticalSizedBox(10),

              cancel(),

              //If cancel is clicked then visible this
              ///Refund amount calculation
              Visibility(
                  visible: buyerCancellingOrderBloc.isCancelClicked,
                  child: RefundAmountCalculation(
                    subOrderList: buyerCancellingOrderBloc.subOrderList
                        .where((element) => element.isSelected)
                        .toList(),
                    order: buyerCancellingOrderBloc.store,
                    backgroundColor: AppColors.textFieldFill1,
                  )),
            ],
          );
        });
  }
//endregion

  //region Sub orders list
  Widget subOrderList() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: buyerCancellingOrderBloc.subOrderList.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppCommonWidgets.subOrderInfo(
                  subOrder: buyerCancellingOrderBloc.subOrderList[index],
                  onTap: () {
                    buyerCancellingOrderBloc
                        .onSelectSubOrder(widget.subOrderList[index]);
                    buyerCancellingOrderBloc.hideRefund();
                  },
                  context: context),
              index == buyerCancellingOrderBloc.subOrderList.length - 1
                  ? const SizedBox()
                  : Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                      child: Divider(
                        color: AppColors.lightGray,
                        height: 1,
                        thickness: 1,
                      ),
                    )
            ],
          );
        });
  }

//endregion

//region Sub orders list
//   Widget subOrderList(){
//     return Container(
//       margin: const EdgeInsets.all(5),
//       decoration: BoxDecoration(
//           border: Border.all(  color: AppColors.lightGray2,
//             width: 1,
//           ),
//           borderRadius: const BorderRadius.all(Radius.circular(10))
//       ),
//       child: ListView.builder(
//           physics: const NeverScrollableScrollPhysics(),
//           itemCount: widget.subOrderList.length,
//           shrinkWrap: true,
//           itemBuilder:(buildContext,index){
//             return Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 AppCommonWidgets.subOrderInfo(subOrder:widget.subOrderList[index],
//                     onTap: (){
//                         buyerCancellingOrderBloc.onSelectSubOrder(widget.subOrderList[index]);
//                         buyerCancellingOrderBloc.hideRefund();
//
//
//                     }, context: context,
//                     isCheckBoxVisible: true,
//                     isPriceDetailVisible: true,
//                     isStatusVisible: true,
//                     isArrowVisible: false
//
//                 ),
//             //     InkWell(
//             //     onTap: (){
//             //   buyerCancellingOrderBloc.onSelectSubOrder(widget.subOrderList[index]);
//             //   buyerCancellingOrderBloc.hideRefund();
//             // },
//             // child:buyerBottomSheetSubOrderDetails(context: context,subOrder: widget.subOrderList[index],subOrderStatus:buyerCancellingOrderBloc.subOrderList[index].suborderStatus!.toLowerCase(),isPriceQuantityVisible: true,showCheckBox: true),
//             // ),
//                 index==widget.subOrderList.length-1?SizedBox():divider()
//               ],
//             );
//           }),
//     );
//   }
//endregion

//region Cancel
  Widget cancel() {
    return Visibility(
      visible: !buyerCancellingOrderBloc.isCancelClicked,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Row(
          children: [
            Expanded(
              child: AppCommonWidgets.activeButton(
                  buttonName: CommonMethods.sellerSelectedSubOrderNumberList(
                                  buyerCancellingOrderBloc.subOrderList)
                              .length >
                          1
                      ? AppStrings.cancelAll
                      : AppStrings.cancel,
                  onTap: () {
                    buyerCancellingOrderBloc.onTapCancel();
                  }),
            )
          ],
        ),
      ),
    );
  }
//endregion

//region Proceed to cancel
  Widget proceedToCancel() {
    return Visibility(
      visible: buyerCancellingOrderBloc.isCancelClicked,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSizedBox(10),
            Text(
              "Refund amount gets credited to the same channel from which you paid for the order",
              style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(20),
            AppTitleAndOptions(
              title: AppStrings.reasonToCancel,
              titleOption: SvgPicture.asset(AppImages.exclamation),
              option: AppTextFields.allTextField(
                context: context,
                maxEntry: 200,
                maxLines: 5,
                minLines: 5,
                textEditingController:
                    buyerCancellingOrderBloc.cancelReasonTextCtrl,
                hintText: "Cancel reason",
              ),
            ),
            verticalSizedBox(20),
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: AppCommonWidgets.activeButton(
                      buttonName: "Proceed to cancel",
                      onTap: () {
                        buyerCancellingOrderBloc.proceedToCancel(
                            subOrderList: widget.subOrderList);
                      }),
                )
                //
                // buyerMyOrderCancelButton(buttonName: "Proceed to cancel",
                //     textColor: AppColors.appWhite,
                //     buttonColor: AppColors.brandGreen,
                //     onPress: (){
                //   buyerCancellingOrderBloc.proceedToCancel(subOrderList: widget.subOrderList);
                //   //buyerCancellingOrderBloc.cancelOrder(subOrderList:widget.subOrderList );
                // }),
              ],
            ),
          ],
        ),
      ),
    );
  }
//endregion
}
