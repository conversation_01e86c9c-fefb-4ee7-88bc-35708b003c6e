import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/post/single_post_view/commentr_field_bloc.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/parent_reveal_widget.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddCommentScreen extends StatefulWidget {
  final SinglePostViewBloc singlePostViewBloc;
  final VoidCallback? onCommentAdded;

  const AddCommentScreen({
    super.key,
    required this.singlePostViewBloc,
    this.onCommentAdded,
  });

  @override
  State<AddCommentScreen> createState() => _AddCommentScreenState();
}

class _AddCommentScreenState extends State<AddCommentScreen> {
  CommentEnums selectedCommentType = CommentEnums.COMMENT;
  double selectedRating = 5.0;

  @override
  void initState() {
    super.initState();
    // Initialize comment field if needed
    if (widget.singlePostViewBloc.isFromProduct) {
      widget.singlePostViewBloc.commentFieldsBloc.getCommentAccessDetail();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: StreamBuilder<CommentFieldState>(
        stream: widget
            .singlePostViewBloc.commentFieldsBloc.commentFieldStatCtrl.stream,
        builder: (context, snapshot) {
          return Stack(
            children: [
              Scaffold(
                appBar: appBar(),
                body: SafeArea(
                  child: ParentRevealWidget(
                    parentCommentId: widget.singlePostViewBloc
                        .replyCommentOrPostDetail['parentCommentId'],
                    mainParentId: widget.singlePostViewBloc
                        .replyCommentOrPostDetail['mainParentId'],
                    currentReference: widget.singlePostViewBloc
                            .replyCommentOrPostDetail['reference'] ??
                        '',
                    child: body(),
                  ),
                ),
                floatingActionButton: sendButton(),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.endFloat,
              ),
              Visibility(
                visible: snapshot.data == CommentFieldState.Loading,
                child: Positioned.fill(
                  child: Container(
                    color: AppColors.textFieldFill1.withOpacity(0.8),
                    alignment: Alignment.center,
                    child: AppCommonWidgets.appCircularProgress(),
                  ),
                ),
              )
            ],
          );
        },
      ),
    );
  }

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      onTapLeading: () {
        Navigator.pop(context);
      },
      context: context,
      isCustomTitle: false,
      title: _getAppBarTitle(),
      isMembershipVisible: false,
      isCustomMenuVisible: true,
      isDefaultMenuVisible: false,
      isCartVisible: false,
    );
  }

  String _getAppBarTitle() {
    if (widget.singlePostViewBloc.replyCommentOrPostDetail['handle']
            ?.toString()
            .isNotEmpty ==
        true) {
      return "Add Reply";
    }
    if (widget.singlePostViewBloc.isFromProduct) {
      return "Add Comment/Question/Review";
    }
    return "Add Comment";
  }
  //endregion

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          replyingToSection(),
          userInfo(),
          selectedImages(),
          writeCommentSection(),
          if (widget.singlePostViewBloc.isFromProduct) commentTypeSelector(),
          if (selectedCommentType == CommentEnums.REVIEW) ratingSection(),
          accessOptions(),
        ],
      ),
    );
  }
  //endregion

  //region User Info
  Widget userInfo() {
    final loggedInUser = Provider.of<LoggedInUserInfoDataModel>(context);
    final loggedInStore = Provider.of<SellerOwnStoreInfoDataModel>(context);

    return Container(
      alignment: Alignment.centerLeft,
      margin: const EdgeInsets.only(bottom: 5),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(CommonMethods.byReferenceIsUSer(
                    reference: AppConstants.appData.isUserView!
                        ? AppConstants.appData.userReference!
                        : AppConstants.appData.storeReference!)
                ? 100
                : (0.4130 * 27)),
            child: SizedBox(
              height: 27,
              width: 27,
              child: extendedImage(
                  AppConstants.appData.isUserView!
                      ? loggedInUser.userDetail!.icon
                      : loggedInStore.storeInfo!.icon,
                  context,
                  100,
                  100,
                  customPlaceHolder: AppImages.userPlaceHolder),
            ),
          ),
          const SizedBox(width: 5),
          Text(
            AppConstants.appData.isUserView!
                ? loggedInUser.userDetail!.userName ?? "user_name"
                : loggedInStore.storeInfo!.storehandle ?? "Store",
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
                .copyWith(height: 0),
          ),
          VerifiedBadge(
            width: 15,
            height: 15,
            subscriptionType: AppConstants.appData.isUserView!
                ? loggedInUser.userDetail!.subscriptionType
                : loggedInStore.storeInfo!.subscriptionType,
          ),
        ],
      ),
    );
  }
  //endregion

  //region Replying To Section
  Widget replyingToSection() {
    final replyHandle = widget
        .singlePostViewBloc.replyCommentOrPostDetail['handle']
        ?.toString();

    if (replyHandle?.isEmpty != false) {
      return const SizedBox();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill2,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: RichText(
              text: TextSpan(
                style: AppTextStyle.smallTextRegular(
                    textColor: AppColors.writingBlack1),
                children: [
                  const TextSpan(text: 'Replying to '),
                  TextSpan(
                    text: '@$replyHandle',
                    style: AppTextStyle.smallTextRegular(
                        textColor: AppColors.brandBlack),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 16,
            width: 16,
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                Navigator.pop(context);
              },
              child: SvgPicture.asset(
                AppImages.close,
                height: 16,
                width: 16,
                color: AppColors.appBlack,
              ),
            ),
          ),
        ],
      ),
    );
  }
  //endregion

  //region Selected Images
  Widget selectedImages() {
    // For web platform
    if (kIsWeb) {
      if (widget
          .singlePostViewBloc.commentFieldsBloc.webSelectedImage.isNotEmpty) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: widget
                .singlePostViewBloc.commentFieldsBloc.webSelectedImage.length,
            itemBuilder: (context, index) {
              final webImage = widget
                  .singlePostViewBloc.commentFieldsBloc.webSelectedImage[index];
              return Stack(
                children: [
                  Container(
                    margin: const EdgeInsets.only(right: 10),
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      image: DecorationImage(
                        image: MemoryImage(webImage['bytes']),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Positioned(
                    right: 10,
                    top: 0,
                    child: SizedBox(
                      height: 30,
                      width: 30,
                      child: CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          widget.singlePostViewBloc.commentFieldsBloc
                              .onTapRemoveImage(webFilePath: webImage);
                        },
                        child: Opacity(
                          opacity: 0.5,
                          child: SvgPicture.asset(AppImages.removeCircle1),
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      }
    }
    // For mobile platform
    else if (widget
        .singlePostViewBloc.commentFieldsBloc.selectedImage.isNotEmpty) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        height: 100,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount:
              widget.singlePostViewBloc.commentFieldsBloc.selectedImage.length,
          itemBuilder: (context, index) {
            return Stack(
              children: [
                PostAndProductImageWidgets(
                  localOrNetworkImage: widget.singlePostViewBloc
                      .commentFieldsBloc.selectedImage[index].path,
                  imageSize: 100,
                ),
                Positioned(
                  right: 10,
                  top: 0,
                  child: SizedBox(
                    height: 30,
                    width: 30,
                    child: CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        widget.singlePostViewBloc.commentFieldsBloc
                            .onTapRemoveImage(
                                filePath: widget.singlePostViewBloc
                                    .commentFieldsBloc.selectedImage[index]);
                      },
                      child: Opacity(
                        opacity: 0.5,
                        child: SvgPicture.asset(AppImages.removeCircle1),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      );
    }
    return const SizedBox();
  }
  //endregion

  //region Write Comment Section
  Widget writeCommentSection() {
    return Container(
      // margin: const EdgeInsets.symmetric(horizontal: 15),
      child: TextFormField(
        autofocus: true,
        maxLines: 10,
        minLines: 3,
        textCapitalization: TextCapitalization.sentences,
        inputFormatters: [
          LengthLimitingTextInputFormatter(500),
        ],
        textAlign: TextAlign.start,
        textInputAction: TextInputAction.none,
        keyboardType: TextInputType.multiline,
        controller: widget.singlePostViewBloc.commentFieldsBloc.commentTextCtrl,
        scrollPadding:
            EdgeInsets.only(bottom: MediaQuery.of(context).size.height * 0.2),
        style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        // decoration: InputDecoration(
        //   isDense: true,
        //   hintStyle: AppTextStyle.hintText(textColor: AppColors.writingBlack1),
        //   fillColor: AppColors.textFieldFill1,
        //   filled: true,
        //   hintText: _getHintText(),
        //   contentPadding:
        //       const EdgeInsets.symmetric(vertical: 10, horizontal: 14.0),
        //   border: InputBorder.none,
        //   // focusedBorder: InputBorder.none, // Remove border when focused
        //   // enabledBorder: InputBorder.none, // Remove border when not focused
        //   // errorBorder: InputBorder.none, // Remove border on error
        //   // disabledBorder: InputBorder.none, // Remove border when disabled
        //   // focusedErrorBorder: InputBorder.none,
        // ),
        decoration: InputDecoration(
          isDense: true,
          hintStyle: AppTextStyle.hintText(textColor: AppColors.writingBlack1),
          fillColor: AppColors.appWhite,
          // Specify the desired internal color
          filled: true,
          hintText: "write your thoughts..",
          contentPadding:
              const EdgeInsets.symmetric(vertical: 10, horizontal: 14.0),
          border: InputBorder.none, // Remove all borders
          // focusedBorder: InputBorder.none, // Remove border when focused
          // enabledBorder: InputBorder.none, // Remove border when not focused
          // errorBorder: InputBorder.none, // Remove border on error
          // disabledBorder: InputBorder.none, // Remove border when disabled
          // focusedErrorBorder:
          //     InputBorder.none, // Remove border on error when focused
        ),
      ),
    );
  }

// //region Write your though
//   Widget writeYourThough() {
//     return Column(
//       children: [
//         //Text field
//         TextFormField(
//             autofocus: true,
//             maxLines: 10,
//             minLines: 3,
//             textCapitalization: TextCapitalization.sentences,
//             inputFormatters: [
//               LengthLimitingTextInputFormatter(500),
//             ],
//             textAlign: TextAlign.start,
//             textInputAction: TextInputAction.none,
//             keyboardType: TextInputType.multiline,
//             controller: editPostBloc.addPostTextCtrl,
//             scrollPadding: EdgeInsets.only(
//                 bottom: MediaQuery.of(context).size.height * 0.2),
//             style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
//             decoration: InputDecoration(
//               isDense: true,
//               hintStyle:
//                   AppTextStyle.hintText(textColor: AppColors.writingBlack1),
//               fillColor: AppColors.appWhite,
//               // Specify the desired internal color
//               filled: true,
//               hintText: "write your thoughts..",
//               contentPadding:
//                   const EdgeInsets.symmetric(vertical: 10, horizontal: 14.0),
//               border: InputBorder.none, // Remove all borders
//               // focusedBorder: InputBorder.none, // Remove border when focused
//               // enabledBorder: InputBorder.none, // Remove border when not focused
//               // errorBorder: InputBorder.none, // Remove border on error
//               // disabledBorder: InputBorder.none, // Remove border when disabled
//               // focusedErrorBorder:
//               //     InputBorder.none, // Remove border on error when focused
//             )),
//         //Rating
//         StreamBuilder<bool>(
//             stream: editPostBloc.refreshCtrl.stream,
//             builder: (context, snapshot) {
//               return Visibility(
//                 visible:
//                     widget.postDetail.commentType == CommentEnums.REVIEW.name,
//                 child: RatingBar.builder(
//                   initialRating:
//                       widget.postDetail.commentType == CommentEnums.REVIEW.name
//                           ? double.parse(editPostBloc.reviewCount)
//                           : 5.0,
//                   minRating: 1,
//                   direction: Axis.horizontal,
//                   glowColor: AppColors.yellow,
//                   unratedColor: AppColors.lightGray,
//                   allowHalfRating: false,
//                   itemCount: 5,
//                   itemPadding: const EdgeInsets.symmetric(horizontal: 5.0),
//                   itemBuilder: (context, _) => const Icon(
//                     Icons.star,
//                     color: Colors.amber,
//                   ),
//                   onRatingUpdate: (rating) {
//                     editPostBloc.reviewCount = rating.toString();
//                     // widget.singlePostViewBloc.commentFieldsBloc.ratingCount = rating.round().toString();
//                     //buyerProductCommentBloc.productRating = rating.round();
//                     // //print(buyerProductCommentBloc.productRating);
//                   },
//                 ),
//               );
//             }),
//         // Typing suggestions overlay
//         StreamBuilder<bool>(
//           stream: editPostBloc.showSuggestionsCtrl.stream,
//           initialData: false,
//           builder: (context, showSnapshot) {
//             if (!showSnapshot.data!) return const SizedBox.shrink();

//             return StreamBuilder<List<SuggestionItem>>(
//               stream: editPostBloc.suggestionsCtrl.stream,
//               initialData: const [],
//               builder: (context, suggestionsSnapshot) {
//                 return StreamBuilder<bool>(
//                   stream: editPostBloc.suggestionsLoadingCtrl.stream,
//                   initialData: false,
//                   builder: (context, loadingSnapshot) {
//                     return TypingSuggestionsOverlay(
//                       suggestions: suggestionsSnapshot.data ?? [],
//                       onSuggestionTap: editPostBloc.onSuggestionTap,
//                       isLoading: loadingSnapshot.data ?? false,
//                       onLoadMore: editPostBloc.loadMoreSuggestions,
//                       hasMore: editPostBloc.hasMoreSuggestions,
//                     );
//                   },
//                 );
//               },
//             );
//           },
//         ),
//       ],
//     );
//   }

// //endregion

  String _getHintText() {
    if (widget.singlePostViewBloc.replyCommentOrPostDetail['handle']
            ?.toString()
            .isNotEmpty ==
        true) {
      return "Write your reply...";
    }
    if (widget.singlePostViewBloc.isFromProduct) {
      switch (selectedCommentType) {
        case CommentEnums.QUESTION:
          return "Ask a question...";
        case CommentEnums.REVIEW:
          return "Write your review...";
        default:
          return "Write a comment...";
      }
    }
    return "Write your comment...";
  }
  //endregion

  //region Comment Type Selector (for products)
  Widget commentTypeSelector() {
    return StreamBuilder<CommentFieldState>(
      stream: widget
          .singlePostViewBloc.commentFieldsBloc.commentFieldStatCtrl.stream,
      builder: (context, snapshot) {
        if (snapshot.data != CommentFieldState.Success) {
          return const SizedBox();
        }

        final commentAccessDetails =
            widget.singlePostViewBloc.commentFieldsBloc.commentAccessDetails;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          child: Row(
            children: [
              Text(
                "Post as:",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              horizontalSizedBox(10),
              Container(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: DropdownButton<CommentEnums>(
                    value: selectedCommentType,
                    icon: Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.appBlack,
                    ),
                    iconSize: 24,
                    // elevation: 16,
                    style: AppTextStyle.contentText0(
                      textColor: AppColors.appBlack,
                    ),
                    onChanged: (CommentEnums? newValue) {
                      setState(() {
                        selectedCommentType = newValue!;
                      });
                    },
                    items: [
                      if (commentAccessDetails.commentAccess == true)
                        DropdownMenuItem<CommentEnums>(
                          value: CommentEnums.COMMENT,
                          child: Text("Comment"),
                        ),
                      if (commentAccessDetails.questionAccess == true)
                        DropdownMenuItem<CommentEnums>(
                          value: CommentEnums.QUESTION,
                          child: Text("Question"),
                        ),
                      if (commentAccessDetails.reviewAccess == true)
                        DropdownMenuItem<CommentEnums>(
                          value: CommentEnums.REVIEW,
                          child: Text("Review"),
                        ),
                    ],
                    underline: SizedBox(),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTypeButton(String title, CommentEnums type, bool isSelected) {
    return InkWell(
      onTap: () {
        setState(() {
          selectedCommentType = type;
        });
      },
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.brandBlack : AppColors.appWhite,
          border: Border.all(color: AppColors.appBlack, width: 1),
          borderRadius: const BorderRadius.all(Radius.circular(50)),
        ),
        child: Text(
          title,
          style: AppTextStyle.contentText0(
            textColor:
                isSelected ? AppColors.appWhite : AppColors.writingBlack0,
          ),
        ),
      ),
    );
  }
  //endregion

  //region Rating Section (for reviews)
  Widget ratingSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Rating:",
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          const SizedBox(height: 10),
          RatingBar.builder(
            initialRating: selectedRating,
            minRating: 1,
            direction: Axis.horizontal,
            glowColor: AppColors.yellow,
            unratedColor: AppColors.lightGray,
            allowHalfRating: false,
            itemCount: 5,
            itemPadding: const EdgeInsets.symmetric(horizontal: 5.0),
            itemBuilder: (context, _) => const Icon(
              Icons.star,
              color: Colors.amber,
            ),
            onRatingUpdate: (rating) {
              setState(() {
                selectedRating = rating;
              });
              widget.singlePostViewBloc.commentFieldsBloc.ratingCount =
                  rating.round().toString();
            },
          ),
        ],
      ),
    );
  }
  //endregion

  //region Access Options
  Widget accessOptions() {
    return Column(
      children: [
        Consumer<AppConfigDataModel>(
          builder:
              (BuildContext context, AppConfigDataModel value, Widget? child) {
            return AppCommonWidgets.settingOption(
              horizontalPadding: 15,
              optionText:
                  "${AppStrings.addPhotos} (up to ${value.appConfig!.postImageLimit})",
              onTap: () {
                widget.singlePostViewBloc.commentFieldsBloc.onTapAddImage();
              },
            );
          },
        ),
      ],
    );
  }
  //endregion

  //region Send Button
  Widget sendButton() {
    return FloatingActionButton.extended(
      backgroundColor: AppColors.appWhite,
      extendedPadding: EdgeInsets.zero,
      isExtended: true,
      elevation: 0,
      tooltip: "Send",
      label: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          CupertinoButton(
            color: AppColors.brandBlack,
            borderRadius: BorderRadius.circular(100),
            onPressed: () {
              if (CommonMethods().isStaticUser()) {
                CommonMethods().goToSignUpFlow();
                return;
              }
              _sendComment();
            },
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
            child: Text(
              "Send",
              style: AppTextStyle.access0(textColor: AppColors.appWhite),
            ),
          ),
        ],
      ),
      onPressed: null,
    );
  }

  void _sendComment() {
    if (selectedCommentType == CommentEnums.REVIEW) {
      widget.singlePostViewBloc.commentFieldsBloc.sendComment(
        replyCommentOrPostDetail:
            widget.singlePostViewBloc.replyCommentOrPostDetail,
        commentEnums: selectedCommentType,
        reviewCount: selectedRating.round().toString(),
      );
    } else {
      widget.singlePostViewBloc.commentFieldsBloc.sendComment(
        replyCommentOrPostDetail:
            widget.singlePostViewBloc.replyCommentOrPostDetail,
        commentEnums: selectedCommentType,
      );
    }

    // Close the screen after sending
    Navigator.pop(context);
    widget.onCommentAdded?.call();
  }
  //endregion
}
