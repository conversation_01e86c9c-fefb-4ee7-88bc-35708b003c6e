import 'package:flutter/material.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/add_comment_screen.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';

class CommentBottomSheetService {
  /// Show comment screen for commenting on a post
  static void showPostCommentBottomSheet({
    required BuildContext context,
    required String postReference,
    VoidCallback? onCommentAdded,
  }) {
    // Create a SinglePostViewBloc for the post
    final singlePostViewBloc = SinglePostViewBloc(context, postReference, false);
    singlePostViewBloc.init();

    // Set up for commenting on the post (not replying)
    singlePostViewBloc.replyCommentOrPostDetail = {
      "reference": postReference,
      "handle": ""
    };

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddCommentScreen(
          singlePostViewBloc: singlePostViewBloc,
          onCommentAdded: () {
            singlePostViewBloc.dispose();
            onCommentAdded?.call();
          },
        ),
      ),
    );
  }

  /// Show comment screen for commenting on a product
  static void showProductCommentBottomSheet({
    required BuildContext context,
    required String productReference,
    VoidCallback? onCommentAdded,
  }) {
    // Create a SinglePostViewBloc for the product
    final singlePostViewBloc = SinglePostViewBloc(context, productReference, true);
    singlePostViewBloc.init();

    // Set up for commenting on the product (not replying)
    singlePostViewBloc.replyCommentOrPostDetail = {
      "reference": productReference,
      "handle": ""
    };

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddCommentScreen(
          singlePostViewBloc: singlePostViewBloc,
          onCommentAdded: () {
            singlePostViewBloc.dispose();
            onCommentAdded?.call();
          },
        ),
      ),
    );
  }

  /// Show comment screen for replying to a specific comment
  static void showReplyBottomSheet({
    required BuildContext context,
    required PostDetail commentDetail,
    VoidCallback? onReplyAdded,
  }) {
    // Create a SinglePostViewBloc for the comment
    final singlePostViewBloc = SinglePostViewBloc(context, commentDetail.postOrCommentReference!, false);
    singlePostViewBloc.init();

    // Set up for replying to the comment
    singlePostViewBloc.replyCommentOrPostDetail = {
      "reference": commentDetail.postOrCommentReference!,
      "handle": commentDetail.createdBy?.handle ?? ""
    };

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddCommentScreen(
          singlePostViewBloc: singlePostViewBloc,
          onCommentAdded: () {
            singlePostViewBloc.dispose();
            onReplyAdded?.call();
          },
        ),
      ),
    );
  }
}
