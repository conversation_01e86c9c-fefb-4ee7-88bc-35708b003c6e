import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';

class AppThemes {
  // Light Theme Configuration
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: false,
      brightness: Brightness.light,

      // Primary colors
      primarySwatch: Colors.green,
      primaryColor: AppColors.brandGreen,

      // Background colors
      scaffoldBackgroundColor: AppColors.appWhite,
      backgroundColor: AppColors.appWhite,
      canvasColor: AppColors.appWhite,

      // Card and surface colors
      cardColor: AppColors.appWhite,
      dialogBackgroundColor: AppColors.appWhite,

      // Text theme
      textTheme: TextTheme(
        displayLarge: TextStyle(color: AppColors.appBlack),
        displayMedium: TextStyle(color: AppColors.appBlack),
        displaySmall: TextStyle(color: AppColors.appBlack),
        headlineLarge: TextStyle(color: AppColors.appBlack),
        headlineMedium: TextStyle(color: AppColors.appBlack),
        headlineSmall: TextStyle(color: AppColors.appBlack),
        titleLarge: TextStyle(color: AppColors.title),
        titleMedium: TextStyle(color: AppColors.title),
        titleSmall: TextStyle(color: AppColors.title),
        bodyLarge: TextStyle(color: AppColors.appBlack),
        bodyMedium: TextStyle(color: AppColors.appBlack),
        bodySmall: TextStyle(color: AppColors.writingColor2),
        labelLarge: TextStyle(color: AppColors.appBlack),
        labelMedium: TextStyle(color: AppColors.appBlack),
        labelSmall: TextStyle(color: AppColors.writingColor2),
      ),

      // AppBar theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.appWhite,
        foregroundColor: AppColors.appBlack,
        elevation: 0,
        iconTheme: IconThemeData(color: AppColors.appBlack),
        titleTextStyle: TextStyle(
          color: AppColors.appBlack,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),

      // Icon theme
      iconTheme: IconThemeData(
        color: AppColors.appBlack,
      ),

      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandGreen,
          foregroundColor: AppColors.appWhite,
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.brandGreen,
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.brandGreen,
          side: BorderSide(color: AppColors.brandGreen),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        fillColor: AppColors.textFieldFill1,
        filled: true,
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        //border: OutlineInputBorder(
        //   borderSide: BorderSide(color: AppColors.borderColor1),
        // ),
        // enabledBorder: OutlineInputBorder(
        //   borderSide: BorderSide(color: AppColors.borderColor1),
        // ),
        // focusedBorder: OutlineInputBorder(
        //   borderSide: BorderSide(color: AppColors.brandGreen),
        // ),
        // errorBorder: OutlineInputBorder(
        //   borderSide: BorderSide(color: AppColors.red),
        // ),

        hintStyle: TextStyle(color: AppColors.writingColor2),
      ),

      // Text selection theme
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: AppColors.brandGreen,
        selectionColor: AppColors.brandGreen,
        selectionHandleColor: AppColors.brandGreen,
      ),

      // Scrollbar theme
      scrollbarTheme: ScrollbarThemeData(
        thumbColor: MaterialStateProperty.all<Color>(AppColors.brandBlack),
        radius: const Radius.circular(50),
        thickness: MaterialStateProperty.all<double>(5.0),
        interactive: true,
      ),

      // Divider theme
      dividerTheme: DividerThemeData(
        color: AppColors.lightGray2,
        thickness: 1,
      ),

      // Other properties
      splashColor: Colors.transparent,
      highlightColor: AppColors.brandGreen.withOpacity(0.1),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColors.appWhite,
        selectedItemColor: AppColors.brandGreen,
        unselectedItemColor: AppColors.writingColor2,
        type: BottomNavigationBarType.fixed,
      ),

      // Tab bar theme
      tabBarTheme: TabBarTheme(
        labelColor: AppColors.brandGreen,
        unselectedLabelColor: AppColors.writingColor2,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: AppColors.brandGreen, width: 2),
        ),
      ),
    );
  }

  // Dark Theme Configuration
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: false,
      brightness: Brightness.dark,

      // Primary colors
      primarySwatch: Colors.green,
      primaryColor: AppColors.brandGreen,

      // Background colors
      scaffoldBackgroundColor: AppColors.appWhite,
      backgroundColor: AppColors.appWhite,
      canvasColor: AppColors.appWhite,

      // Card and surface colors
      cardColor: AppColors.textFieldFill2,
      dialogBackgroundColor: AppColors.textFieldFill2,

      // Text theme
      textTheme: TextTheme(
        displayLarge: TextStyle(color: AppColors.appBlack),
        displayMedium: TextStyle(color: AppColors.appBlack),
        displaySmall: TextStyle(color: AppColors.appBlack),
        headlineLarge: TextStyle(color: AppColors.appBlack),
        headlineMedium: TextStyle(color: AppColors.appBlack),
        headlineSmall: TextStyle(color: AppColors.appBlack),
        titleLarge: TextStyle(color: AppColors.title),
        titleMedium: TextStyle(color: AppColors.title),
        titleSmall: TextStyle(color: AppColors.title),
        bodyLarge: TextStyle(color: AppColors.appBlack),
        bodyMedium: TextStyle(color: AppColors.appBlack),
        bodySmall: TextStyle(color: AppColors.writingColor2),
        labelLarge: TextStyle(color: AppColors.appBlack),
        labelMedium: TextStyle(color: AppColors.appBlack),
        labelSmall: TextStyle(color: AppColors.writingColor2),
      ),

      // AppBar theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.appWhite,
        foregroundColor: AppColors.appBlack,
        elevation: 0,
        iconTheme: IconThemeData(color: AppColors.appBlack),
        titleTextStyle: TextStyle(
          color: AppColors.appBlack,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),

      // Icon theme
      iconTheme: IconThemeData(
        color: AppColors.appBlack,
      ),

      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandGreen,
          foregroundColor: AppColors.appWhite,
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.brandGreen,
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.brandGreen,
          side: BorderSide(color: AppColors.brandGreen),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        fillColor: AppColors.textFieldFill1,
        filled: true,
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        hintStyle: TextStyle(color: AppColors.writingColor2),
      ),

      // Text selection theme
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: AppColors.brandGreen,
        selectionColor: AppColors.brandGreen,
        selectionHandleColor: AppColors.brandGreen,
      ),

      // Scrollbar theme
      scrollbarTheme: ScrollbarThemeData(
        thumbColor: MaterialStateProperty.all<Color>(AppColors.brandBlack),
        radius: const Radius.circular(50),
        thickness: MaterialStateProperty.all<double>(5.0),
        interactive: true,
      ),

      // Divider theme
      dividerTheme: DividerThemeData(
        color: AppColors.borderColor2,
        thickness: 1,
      ),

      // Other properties
      splashColor: Colors.transparent,
      highlightColor: AppColors.brandGreen.withOpacity(0.1),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColors.appWhite,
        selectedItemColor: AppColors.brandGreen,
        unselectedItemColor: AppColors.writingColor2,
        type: BottomNavigationBarType.fixed,
      ),

      // Tab bar theme
      tabBarTheme: TabBarTheme(
        labelColor: AppColors.brandGreen,
        unselectedLabelColor: AppColors.writingColor2,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: AppColors.brandGreen, width: 2),
        ),
      ),
    );
  }
}
