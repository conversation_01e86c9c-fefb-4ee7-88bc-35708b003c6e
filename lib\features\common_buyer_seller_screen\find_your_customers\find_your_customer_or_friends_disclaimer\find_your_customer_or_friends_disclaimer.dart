import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class FindYourCustomerOrFriendDisclaimer extends StatefulWidget {
  const FindYourCustomerOrFriendDisclaimer({super.key});

  @override
  State<FindYourCustomerOrFriendDisclaimer> createState() => _FindYourCustomerOrFriendDisclaimerState();
}

class _FindYourCustomerOrFriendDisclaimerState extends State<FindYourCustomerOrFriendDisclaimer> {
  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region Body

  Widget body() {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text("Disclaimer",style: AppTextStyle.sectionHeading(textColor: AppColors.writingBlack0),),
          Container(
            margin: EdgeInsets.all(20),
            child: Text(
                "• When you hit support, you will become one of supporters for stores created by one of your contacts, encouraging them.\n"
            "• When you hit follow, your contacts who are already on Swadesic will receive an immediate follow from you.]n"
            "• For contacts not on Swadesic, they'll receive a follow request when they join Swadesic, helping you reconnect and a warm welcome to them from you 😊\n"
            "• We respect your privacy and strive to make your experience enjoyable and convenient. All contact numbers are encrypted and saved.",
              style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0),

            ),
          ),
        ],
      ),
    );
  }

//endregion

}
