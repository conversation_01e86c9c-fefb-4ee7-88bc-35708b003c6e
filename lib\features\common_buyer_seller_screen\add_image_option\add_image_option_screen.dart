import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/add_image_option/add_image_option_bloc.dart';
import 'package:swadesic/features/seller/add_and_edit_image/add_and_edit_image_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddImageOptionScreen extends StatefulWidget {
  const AddImageOptionScreen({Key? key}) : super(key: key);

  @override
  _BrandNameScreenState createState() => _BrandNameScreenState();
}

class _BrandNameScreenState extends State<AddImageOptionScreen> {
  //region Bloc
  late AddImageOptionBloc addImageOptionBloc;

  //endregion
  //region Init
  @override
  void initState() {
    addImageOptionBloc = AddImageOptionBloc(context);
    addImageOptionBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: body(),
    );
  }

  //region App bar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      title: AppStrings.addImages,
      context: context,
      isCenterTitle: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isDefaultMenuVisible: true,
      isCustomMenuVisible: false,
    );
  }

  //endregion

  //region body
  Widget body() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          takePhoto(),
          verticalSizedBox(40),
          uploadImageFromPhone(),
          verticalSizedBox(40),

          //addFromPrevious(),
        ],
      ),
    );
  }
//endregion

  //region Take A Photo
  Widget takePhoto() {
    return button(
        onTap: () {
          addImageOptionBloc.openCamera();
        },
        buttonName: AppStrings.takePhoto);
  }
  //endregion

  //region Upload Image From Phone
  Widget uploadImageFromPhone() {
    return button(
        onTap: () {
          addImageOptionBloc.openGallery();
        },
        buttonName: AppStrings.uploadImageFromPhone);
  }
  //endregion

  //region Button
  Widget button({required String buttonName, required dynamic onTap}) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        alignment: Alignment.center,
        width: double.infinity,
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
            color: AppColors.brandBlack,
            borderRadius: BorderRadius.all(Radius.circular(100))),
        child: Text(buttonName,
            style: AppTextStyle.access0(textColor: AppColors.appWhite)),
      ),
    );
  }
  //endregion
}
