import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:swadesic/features/widgets/post_widgets/parent_content_service.dart';
import 'package:swadesic/features/widgets/post_widgets/parent_content_widget.dart';
import 'package:swadesic/util/app_colors.dart';
import 'dart:developer' as developer;

/// Widget that displays parent content hierarchy above current content (Twitter-style)
class ParentRevealWidget extends StatefulWidget {
  final String? parentCommentId;
  final String? mainParentId;
  final String currentReference;
  final Widget child;
  final ScrollController? scrollController;

  const ParentRevealWidget({
    Key? key,
    this.parentCommentId,
    this.mainParentId,
    required this.currentReference,
    required this.child,
    this.scrollController,
  }) : super(key: key);

  @override
  State<ParentRevealWidget> createState() => _ParentRevealWidgetState();
}

class _ParentRevealWidgetState extends State<ParentRevealWidget> {
  static const String _tag = 'ParentRevealWidget';

  List<dynamic> _parentChain = [];
  List<dynamic> _revealedParents = [];
  ScrollController? _scrollController;
  bool _isLoadingParents = false;
  bool _hasLoadedParentChain = false;
  double _lastScrollPosition = 0;
  int _nextParentIndex = 0; // Track which parent to load next
  double _revealOffset = 0.0; // Track reveal offset for animation
  bool _isRevealing = false; // Track if we're in reveal mode

  @override
  void initState() {
    super.initState();
    // Don't load parents by default - wait for scroll interaction
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _attachScrollListener();
    });
  }

  @override
  void dispose() {
    _scrollController?.removeListener(_onScroll);
    super.dispose();
  }

  /// Attach scroll listener to detect upward scrolling
  void _attachScrollListener() {
    try {
      // Use the passed scroll controller or try to find one from the widget tree
      _scrollController = widget.scrollController;

      if (_scrollController == null) {
        final scrollable = Scrollable.of(context);
        if (scrollable != null) {
          _scrollController = scrollable.widget.controller;
        }
      }

      if (_scrollController != null) {
        _scrollController!.addListener(_onScroll);
        developer.log('[INFO] Scroll listener attached successfully', name: _tag);
      } else {
        developer.log('[WARNING] No ScrollController available', name: _tag);
      }
    } catch (e) {
      developer.log('[ERROR] Failed to attach scroll listener: $e', name: _tag);
    }
  }

  /// Handle scroll events to reveal parents progressively (Twitter-style)
  void _onScroll() {
    if (_scrollController == null) return;

    try {
      final position = _scrollController!.position;
      final currentPixels = position.pixels;
      final isAtTop = currentPixels <= 0;
      final isNearTop = currentPixels <= 100;
      final isScrollingUp = position.userScrollDirection == ScrollDirection.forward;
      final isOverscrolling = currentPixels < 0;

      // Track scroll direction by comparing positions
      final scrollDelta = currentPixels - _lastScrollPosition;
      final isMovingUp = scrollDelta < 0;
      _lastScrollPosition = currentPixels;

      // Calculate reveal offset based on scroll position
      if (isOverscrolling) {
        _revealOffset = -currentPixels.clamp(0.0, 100.0) / 100.0; // Normalize to 0-1
        setState(() {}); // Trigger rebuild for smooth animation
      } else if (_revealOffset > 0) {
        _revealOffset = 0.0;
        setState(() {});
      }

      developer.log(
        '[SCROLL] pixels: ${currentPixels.toStringAsFixed(1)}, '
        'delta: ${scrollDelta.toStringAsFixed(1)}, isAtTop: $isAtTop, '
        'isNearTop: $isNearTop, isScrollingUp: $isScrollingUp, '
        'isMovingUp: $isMovingUp, isOverscrolling: $isOverscrolling, '
        'revealOffset: ${_revealOffset.toStringAsFixed(2)}, isLoading: $_isLoadingParents, '
        'revealed: ${_revealedParents.length}/${_parentChain.length}',
        name: _tag
      );

      // Twitter-style: Reveal parents when overscrolling
      if (isOverscrolling && !_isLoadingParents) {
        developer.log('[ACTION] Triggering parent load - Twitter style', name: _tag);
        _loadNextParent();
      }
    } catch (e) {
      developer.log('[ERROR] Error in scroll handler: $e', name: _tag);
    }
  }

  /// Load the complete parent chain (lazy-loaded)
  Future<void> _loadParentChain() async {
    if (widget.parentCommentId == null && widget.mainParentId == null) {
      return;
    }

    if (_hasLoadedParentChain) return;

    setState(() {
      _isLoadingParents = true;
    });

    try {
      final parentChain = await ParentContentService.getParentChain(
        widget.currentReference,
        parentCommentId: widget.parentCommentId,
        mainParentId: widget.mainParentId,
      );

      setState(() {
        _parentChain = parentChain;
        _hasLoadedParentChain = true;
        _isLoadingParents = false;
      });

      developer.log(
        '[EXIT] _loadParentChain(): Loaded ${parentChain.length} parents',
        name: _tag,
      );
    } catch (e) {
      developer.log(
        '[ERROR] _loadParentChain(): Failed to load parent chain: $e',
        name: _tag,
      );
      setState(() {
        _isLoadingParents = false;
      });
    }
  }

  /// Load and reveal the next parent in the chain (Twitter-style: immediate parent first)
  Future<void> _loadNextParent() async {
    // First, ensure we have the parent chain loaded
    if (!_hasLoadedParentChain) {
      await _loadParentChain();
      if (_parentChain.isEmpty) return;
    }

    // If we have more parents to reveal
    if (_nextParentIndex < _parentChain.length) {
      setState(() {
        // Add the next parent in correct order: immediate parent first
        // _parentChain is ordered from immediate parent to highest parent
        final nextParent = _parentChain[_nextParentIndex];
        _revealedParents.add(nextParent);
        _nextParentIndex++;
      });

      developer.log(
        '[INFO] _loadNextParent(): Revealed parent ${_nextParentIndex}/${_parentChain.length} '
        '(${_revealedParents.length} total revealed)',
        name: _tag,
      );
    } else {
      developer.log(
        '[INFO] _loadNextParent(): All parents already revealed (${_revealedParents.length}/${_parentChain.length})',
        name: _tag,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (notification is ScrollUpdateNotification) {
          _handleScrollNotification(notification);
        }
        return false; // Allow the notification to continue
      },
      child: Column(
        children: [
          // Parent content hierarchy (only revealed parents)
          if (_revealedParents.isNotEmpty) ..._buildParentHierarchy(),

          // Loading indicator when loading parents
          if (_isLoadingParents) _buildLoadingIndicator(),

          // Add connection line between last parent and main post if there are revealed parents
          if (_revealedParents.isNotEmpty) _buildConnectionLine(),

          // Current content
          widget.child,
        ],
      ),
    );
  }

  /// Handle scroll notifications for more reliable scroll detection (Twitter-style)
  void _handleScrollNotification(ScrollUpdateNotification notification) {
    try {
      final metrics = notification.metrics;
      final isAtTop = metrics.pixels <= 0;
      final isNearTop = metrics.pixels <= 100;
      final isOverscrolling = metrics.pixels < 0;
      final scrollDelta = notification.scrollDelta ?? 0;
      final isScrollingUp = scrollDelta < 0; // Negative delta means scrolling up (towards top)

      developer.log(
        '[NOTIFICATION] pixels: ${metrics.pixels.toStringAsFixed(1)}, '
        'delta: ${scrollDelta.toStringAsFixed(1)}, isAtTop: $isAtTop, '
        'isNearTop: $isNearTop, isScrollingUp: $isScrollingUp, '
        'isOverscrolling: $isOverscrolling, isLoading: $_isLoadingParents, '
        'revealed: ${_revealedParents.length}/${_parentChain.length}',
        name: _tag
      );

      // Twitter-style: Trigger parent loading when user scrolls UP near the top
      if ((isNearTop && isScrollingUp || isOverscrolling) && !_isLoadingParents) {
        developer.log('[ACTION] Triggering parent load via notification - Twitter style', name: _tag);
        _loadNextParent();
      }
    } catch (e) {
      developer.log('[ERROR] Error in scroll notification handler: $e', name: _tag);
    }
  }

  /// Build the parent hierarchy in Twitter-style (only revealed parents)
  List<Widget> _buildParentHierarchy() {
    List<Widget> parentWidgets = [];

    // Display parents in reverse order: highest parent first, immediate parent last
    List<dynamic> displayOrder = _revealedParents.reversed.toList();

    for (int i = 0; i < displayOrder.length; i++) {
      final parent = displayOrder[i];
      final isLastParent = i == displayOrder.length - 1;
      
      parentWidgets.add(
        Column(
          children: [
            ParentContentWidget(content: parent),
            if (!isLastParent) _buildConnectionLine(),
          ],
        ),
      );
    }

    return parentWidgets;
  }

  /// Build loading indicator for parent loading
  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.brandBlack),
        ),
      ),
    );
  }

  /// Build connection line between parent and child content with different visual styles
  Widget _buildConnectionLine() {
    // Choose from different styles (0-4)
    const int styleIndex = 4; // 4 = Simple vertical line
    
    switch (styleIndex) {
      // Style 0: Simple dashed line
      case 0:
        return Container(
          height: 1,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: CustomPaint(
            painter: DashedLinePainter(
              color: AppColors.appBlack,
              strokeWidth: 5,
              dashLength: 4,
              dashSpace: 2,
            ),
          ),
        );
      
      // Style 1: Gradient line with dots
      case 1:
        return Container(
          height: 12,
          margin: const EdgeInsets.symmetric(horizontal: 24),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 1,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.borderColor1.withOpacity(0),
                        AppColors.borderColor1,
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                width: 4,
                height: 4,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  color: AppColors.borderColor1,
                  shape: BoxShape.circle,
                ),
              ),
              Expanded(
                child: Container(
                  height: 1,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.borderColor1,
                        AppColors.borderColor1.withOpacity(0),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      
      // Style 2: Vertical dotted line with arrow
      case 2:
        return Container(
          height: 16,
          margin: const EdgeInsets.symmetric(horizontal: 32),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Dotted line
              Positioned(
                top: 0,
                bottom: 0,
                child: CustomPaint(
                  size: Size(1, double.infinity),
                  painter: DottedLinePainter(
                    color: AppColors.borderColor1,
                    strokeWidth: 1,
                    dotRadius: 0.5, // Half of strokeWidth for round dots
                    dotSpacing: 2.0,
                    isVertical: true,
                  ),
                ),
              ),
              // Arrow down
              Positioned(
                bottom: 0,
                child: Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                  color: AppColors.borderColor1,
                ),
              ),
            ],
          ),
        );
      
      // Style 3: Curved connector
      case 3:
        return SizedBox(
          height: 20,
          child: CustomPaint(
            size: Size(MediaQuery.of(context).size.width, 20),
            painter: CurvedConnectorPainter(
              color: AppColors.borderColor1,
            ),
          ),
        );
      
      // Style 4: Simple vertical line (left-aligned)
      case 4:
        return Align(
          alignment: Alignment.centerLeft,
          child: Container(
            width: 1,
            height: 28,
            margin: const EdgeInsets.only(left: 32),
            color: AppColors.borderColor1,
          ),
        );
      
      // Default style (fallback - left-aligned)
      default:
        return Align(
          alignment: Alignment.centerLeft,
          child: Container(
            width: 1,
            height: 28,
            margin: const EdgeInsets.only(left: 32),
            color: AppColors.borderColor1,
          ),
        );
    }
  }
}

// Custom painter for curved connector
// Custom painter for dotted line
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dotRadius;
  final double dotSpacing;
  final bool isVertical;

  const DottedLinePainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dotRadius = 1.0,
    this.dotSpacing = 2.0,
    this.isVertical = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.fill;

    final double totalLength = isVertical ? size.height : size.width;
    final double dotDiameter = dotRadius * 2;
    final double spacing = dotDiameter + dotSpacing;
    
    double position = dotRadius;
    
    while (position < totalLength) {
      final center = isVertical 
          ? Offset(size.width / 2, position)
          : Offset(position, size.height / 2);
      
      canvas.drawCircle(center, dotRadius, paint);
      position += spacing;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Custom painter for dashed line
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double dashSpace;

  const DashedLinePainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashLength = 4.0,
    this.dashSpace = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashLength, 0),
        paint,
      );
      startX += dashLength + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class CurvedConnectorPainter extends CustomPainter {
  final Color color;
  
  CurvedConnectorPainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
      
    final path = Path();
    final startX = size.width * 0.4;
    final endX = size.width * 0.6;
    
    path.moveTo(startX, 0);
    path.quadraticBezierTo(
      size.width / 2, 
      size.height * 0.7, 
      endX, 
      size.height
    );
    
    canvas.drawPath(path, paint);
    
    // Draw arrow head
    final arrowPath = Path();
    arrowPath.moveTo(endX - 4, size.height - 8);
    arrowPath.lineTo(endX, size.height);
    arrowPath.lineTo(endX + 4, size.height - 8);
    canvas.drawPath(arrowPath, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;

}
