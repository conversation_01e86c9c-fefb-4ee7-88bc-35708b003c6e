import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/get_orders_card_bloc.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class GetOrdersCard extends StatefulWidget {
  final StoreDashBoard storeDashboard;

  const GetOrdersCard({Key? key, required this.storeDashboard})
      : super(key: key);

  static Widget buildTitle() {
    return Text(
      "Get orders on Swadesic",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }

  @override
  State<GetOrdersCard> createState() => _GetOrdersCardState();
}

class _GetOrdersCardState extends State<GetOrdersCard> {
  late final GetOrdersCardBloc _bloc;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _bloc = GetOrdersCardBloc(context, widget.storeDashboard.storeReference!);
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  Future<void> _handleToggle(bool value) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _bloc.toggleStoreOpenStatus();
    } catch (e) {
      // Error is already handled in the bloc
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: _bloc.stateStream,
      initialData: widget.storeDashboard.openForOrder,
      builder: (context, snapshot) {
        final isOpenForOrder =
            snapshot.data ?? widget.storeDashboard.openForOrder!;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                "Open your store for orders. You can toggle this as many times you want in settings.",
                style: AppTextStyle.smallTextRegular(
                    textColor: AppColors.appBlack),
              ),
            ),
            SizedBox(width: 10),
            // Toggle switch
            SizedBox(
              height: 21,
              width: 43.24,
              child: _isLoading
                  ? Center(
                      child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.brandBlack),
                      ),
                    ))
                  : FlutterSwitch(
                      width: 43.24,
                      height: 21.0,
                      toggleSize: 21,
                      borderRadius: 21.0,
                      padding: 0.0,
                      activeColor: AppColors.lightGray,
                      inactiveColor: AppColors.lightGray,
                      toggleColor: isOpenForOrder
                          ? AppColors.brandBlack
                          : AppColors.darkStroke,
                      value: isOpenForOrder,
                      onToggle: _handleToggle,
                    ),
            ),
          ],
        );
      },
    );
  }
}
