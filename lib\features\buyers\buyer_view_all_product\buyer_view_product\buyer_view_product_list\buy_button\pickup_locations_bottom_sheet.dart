import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/model/seller_return_warranty_response/address_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:url_launcher/url_launcher.dart';

class PickupLocationsBottomSheet extends StatelessWidget {
  final List<Data> locations;

  const PickupLocationsBottomSheet({super.key, required this.locations});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.only(top: 30, bottom: 50),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.center,
            child: Text(
              "Available at below locations",
              style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
            ),
          ),
          const SizedBox(height: 40),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: locations.length,
            separatorBuilder: (context, index) => const Divider(
              height: 24,
              thickness: 1,
              color: Color(0xFFE5E5E5),
            ),
            itemBuilder: (context, index) =>
                _buildLocationCard(locations[index], context),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationCard(Data location, BuildContext context) {
    return GestureDetector(
      onLongPress: () {
        if (location.locationLink != null && location.locationLink!.isNotEmpty) {
          Clipboard.setData(ClipboardData(text: location.locationLink!));
          if (context.mounted) {
            CommonMethods.toastMessage("Link copied to clipboard", context);
          }
        }
      },
      child: InkWell(
        onTap: () async {
          if (location.locationLink != null &&
              location.locationLink!.isNotEmpty) {
            final Uri url = Uri.parse(location.locationLink!);
            if (await canLaunchUrl(url)) {
              await launchUrl(url);
            } else {
              if (context.mounted) {
                CommonMethods.toastMessage(
                    "Could not open location link", context);
              }
            }
          }
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset(
              AppImages.pickUpLocationIcon,
              height: 30,
              width: 30,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    location.name ?? "",
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack),
                  ),
                  // const SizedBox(height: 4),
                  Text(
                    "${location.address}, ${location.city}, ${location.state}, ${location.pincode}",
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack0),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
