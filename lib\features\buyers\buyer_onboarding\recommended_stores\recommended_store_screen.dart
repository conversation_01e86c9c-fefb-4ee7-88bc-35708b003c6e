import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/recommended_stores/recommended_store_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class RecommendedStoreScreen extends StatefulWidget {
  const RecommendedStoreScreen({Key? key}) : super(key: key);

  @override
  _RecommendedStoreScreenState createState() => _RecommendedStoreScreenState();
}

class _RecommendedStoreScreenState extends State<RecommendedStoreScreen> {
  //region Bloc
  late RecommendedStoreBloc recommendedStoreBloc;
  //endregion

  //region Init
  @override
  void initState() {
    recommendedStoreBloc = RecommendedStoreBloc(context);
    recommendedStoreBloc.init();
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
          backgroundColor: AppColors.appWhite,
          appBar: appBar(),
          body: SafeArea(child: body())),
    );
  }

  //region Body
  Widget body() {
    return Column(
      children: [Expanded(child: storeList()), sendGetIn()],
    );
  }
//endregion

  //region AppBar
  AppBar appBar() {
    return AppBar(
      backgroundColor: AppColors.appWhite,
      leading: CupertinoButton(
          onPressed: () {
            Navigator.pop(context);
          },
          padding: EdgeInsets.zero,
          child: SvgPicture.asset(
            AppImages.backButton,
            fit: BoxFit.cover,
          )),
      titleSpacing: 0,

      centerTitle: false,
      title: Text(
        "recommended stores to follow",
        style: TextStyle(
          fontFamily: "LatoBold",
          fontSize: 19,
          fontWeight: FontWeight.w700,
          color: AppColors.appBlack,
        ),
      ),
      elevation: 0,
      automaticallyImplyLeading: false,
      //region Next Button

      actions: [
        CupertinoButton(
            onPressed: () {
              recommendedStoreBloc.goToBottomNavigation();
            },
            //padding: EdgeInsets.zero,
            child: Text(
              "skip",
              style: TextStyle(
                  fontSize: 19,
                  fontFamily: "LatoBold",
                  fontWeight: FontWeight.w700,
                  color: AppColors.primaryGreen),
            )),
      ],

      //endregion
    );
  }
  //endregion

  //region Stores List
  Widget storeList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
      child: GridView.builder(
          itemCount: 10,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              childAspectRatio: MediaQuery.of(context).size.width /
                  (MediaQuery.of(context).size.height / 2.3),
              crossAxisCount: 2,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10),
          itemBuilder: (BuildContext, index) {
            return Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: AppColors.appWhite,
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                border: Border.all(color: AppColors.lightGray),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, 1),
                    blurRadius: 5,
                    color: AppColors.appBlack.withOpacity(0.2),
                  ),
                ],
              ),
              child: InkWell(
                onTap: () {
                  //print(editProductBloc.storeProductResponse.data![index].productid);
                },
                child: StreamBuilder<int>(
                    stream: recommendedStoreBloc.selectStoreCtrl.stream,
                    builder: (context, snapshot) {
                      return Stack(
                        alignment: Alignment.topLeft,
                        children: [
                          Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              storeLogoDetail(),
                              verticalSizedBox(10),
                              divider(),
                              verticalSizedBox(10),
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [follow(), products(), sales()],
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsets.all(10),
                            child: Container(
                              height: 15,
                              width: 15,
                              decoration: BoxDecoration(
                                  //color:AppConstants.selectedProducts.contains(editProductBloc.storeProductResponse.data![index].productid!) ?AppColors.brandBlue:AppColors.white,
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(10)),
                                  border:
                                      Border.all(color: AppColors.lightStroke)),
                            ),
                          ),
                        ],
                      );
                    }),
              ),
            );
          }),
    );
  }

  //endregion

  //region Store Logo and Detail
  Widget storeLogoDetail() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(20)),
          child: Container(
            height: 65,
            width: 65,
            color: Colors.red,
          ),
        ),
        horizontalSizedBox(10),
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Soft house",
                overflow: TextOverflow.fade,
                maxLines: 1,
                softWrap: false,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontFamily: "LatoSemiBold",
                  fontSize: 12,
                  color: AppColors.appBlack,
                ),
              ),
              verticalSizedBox(2),
              Text(
                "Food & beverages",
                overflow: TextOverflow.fade,
                maxLines: 1,
                softWrap: false,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: "LatoRegular",
                  fontSize: 12,
                  color: AppColors.darkGray,
                ),
              ),
              verticalSizedBox(2),
              Text(
                "Food & beverages",
                overflow: TextOverflow.fade,
                maxLines: 1,
                softWrap: false,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: "LatoBold",
                  fontSize: 14,
                  color: AppColors.darkGray,
                ),
              )
            ],
          ),
        )
      ],
    );
  }
  //endregion

  //region Follow
  Widget follow() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "550",
          style: TextStyle(
              fontSize: 14,
              fontFamily: "LatoSemibold",
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack),
        ),
        Text(
          "followers",
          style: TextStyle(
              fontSize: 11,
              fontFamily: "LatoSemibold",
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack),
        )
      ],
    );
  }
  //endregion

  //region Products
  Widget products() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "550",
          style: TextStyle(
              fontSize: 14,
              fontFamily: "LatoSemibold",
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack),
        ),
        Text(
          "followers",
          style: TextStyle(
              fontSize: 11,
              fontFamily: "LatoSemibold",
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack),
        )
      ],
    );
  }
  //endregion

  //region Sales
  Widget sales() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "550",
          style: TextStyle(
              fontSize: 14,
              fontFamily: "LatoSemibold",
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack),
        ),
        Text(
          "followers",
          style: TextStyle(
              fontSize: 11,
              fontFamily: "LatoSemibold",
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack),
        )
      ],
    );
  }
  //endregion

  //region Send OTP and Get In
  Widget sendGetIn() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 80),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        width: double.infinity,
        decoration: BoxDecoration(
            color: AppColors.brandBlack,
            borderRadius: BorderRadius.all(Radius.circular(10))),
        child: Center(
          child: Text(
            AppStrings.followNext,
            style: TextStyle(
                fontFamily: "LatoBold",
                fontSize: 15,
                color: AppColors.appWhite,
                fontWeight: FontWeight.w700),
          ),
        ),
      ),
    );
  }
//endregion
}
