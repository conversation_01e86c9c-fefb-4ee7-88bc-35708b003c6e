import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'package:swadesic/util/common_widgets.dart';

/// Test screen to demonstrate auto-hiding navigation functionality
class AutoHideTestScreen extends StatefulWidget {
  const AutoHideTestScreen({Key? key}) : super(key: key);

  @override
  State<AutoHideTestScreen> createState() => _AutoHideTestScreenState();
}

class _AutoHideTestScreenState extends State<AutoHideTestScreen>
    with AutoHideNavigationMixin<AutoHideTestScreen> {
  
  late ScrollController _scrollController;
  
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    
    // Initialize auto-hide navigation
    enableAutoHideNavigation();
    attachScrollControllerToAutoHide(_scrollController);
  }
  
  @override
  void dispose() {
    detachScrollControllerFromAutoHide(_scrollController);
    disableAutoHideNavigation();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        title: "Auto-Hide Test",
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
      ),
      body: Column(
        children: [
          // Control buttons
          Container(
            padding: const EdgeInsets.all(16),
            color: AppColors.appWhite,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: forceShowNavigationBars,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.brandBlack,
                    foregroundColor: AppColors.appWhite,
                  ),
                  child: const Text("Show Nav"),
                ),
                ElevatedButton(
                  onPressed: forceHideNavigationBars,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.brandBlack,
                    foregroundColor: AppColors.appWhite,
                  ),
                  child: const Text("Hide Nav"),
                ),
                ElevatedButton(
                  onPressed: resetAutoHideNavigation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.brandBlack,
                    foregroundColor: AppColors.appWhite,
                  ),
                  child: const Text("Reset"),
                ),
              ],
            ),
          ),
          
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Auto-Hide Navigation Test",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  "• Scroll down to hide navigation bars\n"
                  "• Scroll up to show navigation bars\n"
                  "• Navigation bars stay visible at top/bottom\n"
                  "• Use buttons above to manually control",
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
          
          // Scrollable content
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: 100,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: index % 2 == 0 ? Colors.grey.shade100 : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: AppColors.brandBlack,
                        child: Text(
                          "${index + 1}",
                          style: TextStyle(
                            color: AppColors.appWhite,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "List Item ${index + 1}",
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              "This is a sample list item to demonstrate the auto-hiding navigation functionality. Scroll up and down to see the navigation bars hide and show automatically.",
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
