import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/waiting_for_confirmation/waiting_for_confirmation_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class WaitingForConfirmationScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final Order order;
  final SellerSubOrderBloc sellerSubOrderBloc;

  const WaitingForConfirmationScreen({
    Key? key,
    required this.suborderList,
    required this.order,
    required this.sellerSubOrderBloc,
  }) : super(key: key);

  @override
  State<WaitingForConfirmationScreen> createState() => _WaitingForConfirmationScreenState();
}

class _WaitingForConfirmationScreenState extends State<WaitingForConfirmationScreen> {
  // region Bloc
  late WaitingForConfirmationBloc waitingForConfirmationBloc;

  // endregion

  // region Init
  @override
  void initState() {
    waitingForConfirmationBloc = WaitingForConfirmationBloc(context, widget.suborderList, widget.order, widget.sellerSubOrderBloc);
    waitingForConfirmationBloc.init();
    super.initState();
  }
  // endregion


  //region Dis update
  @override
  void didUpdateWidget(covariant WaitingForConfirmationScreen oldWidget) {
    waitingForConfirmationBloc = WaitingForConfirmationBloc(context, widget.suborderList, widget.order, widget.sellerSubOrderBloc);
    waitingForConfirmationBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion
  //region Did change
  @override
  void didChangeDependencies() {
    waitingForConfirmationBloc = WaitingForConfirmationBloc(context, widget.suborderList, widget.order, widget.sellerSubOrderBloc);
    waitingForConfirmationBloc.init();
    super.didChangeDependencies();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    waitingForConfirmationBloc.dispose();
    super.dispose();
  }
  //endregion






  @override
  Widget build(BuildContext context) {
    // late ExpandableController expandableController = ExpandableController();
    // expandableController.notifyListeners()
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(
          bottom: BorderSide(
            color: AppColors.lightStroke
          )
        )
      ),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed: confirmAndCancelAll(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            confirmAndCancelAll(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: waitingForConfirmationBloc.suborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Column(
                          children: [
                            productInfoCard(context: context, subOrder: waitingForConfirmationBloc.suborderList[index]),
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                AppCommonWidgets.subOrderButton(
                                    buttonName: AppStrings.confirmThisAlone,
                                    onTap: () {
                                      //Mark only selected
                                      waitingForConfirmationBloc.suborderList[index].isSelected = true;
                                      //Open bottom sheet
                                      waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                                    },
                                    horizontalPadding: 10),
                                horizontalSizedBox(10),
                                AppCommonWidgets.subOrderButton(
                                    buttonName: AppStrings.cancel,
                                    onTap: () {
                                      //Mark only selected
                                      waitingForConfirmationBloc.suborderList[index].isSelected = true;
                                      //Open bottom sheet
                                      waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                                    },
                                    horizontalPadding: 10),
                              ],
                            ),
                            verticalSizedBox(10),

                          ],
                        ),
                        //Divider
                        Visibility(
                          visible: waitingForConfirmationBloc.suborderList.length-1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }




  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.loading,
      componentName: AppStrings.waitingForConfirmation,
      suborderList: waitingForConfirmationBloc.suborderList,
      additionalWidgets:
          Container(
            alignment: Alignment.centerLeft,
            child: StreamBuilder<bool>(
                stream: waitingForConfirmationBloc.confirmCancelRefreshCtrl.stream,
              builder: (context, snapshot) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: RichText(
                        textScaleFactor: MediaQuery.textScaleFactorOf(AppConstants.globalNavigator.currentContext!),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "${waitingForConfirmationBloc.countDownValue} secs  ",
                              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                            ),
                            TextSpan(
                              text: waitingForConfirmationBloc.timer.isActive?AppStrings.autoCancelledIfNotConfirmed:AppStrings.timesUpOrderAutoCancelShortly,
                              style: AppTextStyle.contentHeading0(textColor: AppColors.orange),
                            ),
                            // WidgetSpan(child: Text(productName,
                            // maxLines: 1,
                            // ))
                          ],
                        ),
                      ),
                    ),

                    //   Text("${waitingForConfirmationBloc.countDownValue} secs",
                    //     // Text("${CommonMethods.autoCancelTime(orderedDate: "30:05:2023 13:00:00")} mins",
                    //       style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                    //     ),
                    //
                    // horizontalSizedBox(10),
                    // Expanded(
                    //   child: Text(waitingForConfirmationBloc.timer.isActive?AppStrings.autoCancelledIfNotConfirmed:AppStrings.timesUpOrderAutoCancelShortly,
                    //     maxLines: 2,
                    //     style: AppTextStyle.contentHeading0(textColor: AppColors.orange),
                    //   ),
                    // ),
                  ],
                );
              }
            ),
          ),
    );

    // return Column(
    //   mainAxisSize: MainAxisSize.min,
    //   mainAxisAlignment: MainAxisAlignment.center,
    //   crossAxisAlignment: CrossAxisAlignment.center,
    //   children: [
    //     //Icon and status
    //     Container(
    //       alignment: Alignment.centerLeft,
    //       height: 25,
    //       child: Row(
    //         mainAxisSize: MainAxisSize.min,
    //         mainAxisAlignment: MainAxisAlignment.start,
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         children: [
    //           SvgPicture.asset(AppImages.loading),
    //           horizontalSizedBox(10),
    //           Text(AppStrings.waitingForConfirmation,style: AppTextStyle.normalTextBold(textColor: AppColors.appBlack),),
    //         ],
    //       ),
    //     ),
    //     verticalSizedBox(15),
    //     //Sub order,items
    //     Container(
    //       height: 20,
    //       alignment: Alignment.centerLeft,
    //       child: Row(
    //         mainAxisSize: MainAxisSize.min,
    //         mainAxisAlignment: MainAxisAlignment.start,
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         children: [
    //           Text(CommonMethods.calculateItemsInSuborderList(subOrderList: waitingForConfirmationBloc.suborderList),
    //           style: AppTextStyle.normalTextSemiBold(textColor: AppColors.appBlack),
    //           ),
    //           horizontalSizedBox(10),
    //           Text(CommonMethods.calculateSubOrdersInSuborderList(subOrderList: widget.suborderList),
    //             style: AppTextStyle.normalTextSemiBold(textColor: AppColors.appBlack),
    //           ),
    //         ],
    //       ),
    //     ),
    //     verticalSizedBox(5),
    //     //Estimate delivery
    //     Container(
    //       height: 20,
    //       alignment: Alignment.centerLeft,
    //       child: Text("${AppStrings.deliveryEstimate} ${waitingForConfirmationBloc.suborderList.first.estimatedDeliveryDate}",
    //       style: AppTextStyle.heading2SemiBold(textColor: AppColors.writingColor2,fontSize: 16),
    //       ),
    //     ),
    //     verticalSizedBox(20),
    //     //Timer and Message
    //     Container(
    //       height: 20,
    //       alignment: Alignment.centerLeft,
    //       child: Row(
    //         mainAxisSize: MainAxisSize.min,
    //         mainAxisAlignment: MainAxisAlignment.start,
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         children: [
    //           Text("35:54 mins",
    //             style: AppTextStyle.heading1Bold(textColor: AppColors.writingColor2),
    //           ),
    //           horizontalSizedBox(10),
    //           Text(AppStrings.autoCancelledIfNotConfirmed,
    //             style: AppTextStyle.normalTextBold(textColor: AppColors.orange),
    //           ),
    //         ],
    //       ),
    //     ),
    //
    //     verticalSizedBox(10),
    //
    //
    //
    //   ],
    // );
  }

  //endregion

  //region Confirm and cancel all
  Widget confirmAndCancelAll() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: AppCommonWidgets.activeButton(
                buttonName: AppStrings.confirmAllSubOrder,
                onTap: () {
                  CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.suborderList);
                  waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                }),
        ),
        horizontalSizedBox(10),
        Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: AppStrings.cancelAll,
                onTap: () {
                  CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.suborderList);
                  waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                })),
      ],
    );
  }

  //endregion

}
