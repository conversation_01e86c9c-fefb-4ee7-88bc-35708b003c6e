import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/providers/theme_provider.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/theme_mode.dart';
import 'package:swadesic/util/common_widgets.dart';

class ThemeSettingsScreen extends StatefulWidget {
  const ThemeSettingsScreen({Key? key}) : super(key: key);

  @override
  State<ThemeSettingsScreen> createState() => _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends State<ThemeSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite, // Now automatically theme-aware
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        title: "Theme Settings",
        backgroundColor: AppColors.appWhite,
        leadingIconColor: AppColors.appBlack,
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
      ),
      body: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Theme Settings",
                  style: AppTextStyle.access0(
                    textColor: AppColors.title,
                  ),
                ),
                const SizedBox(height: 20),
                // Light Mode (enabled)
                _buildThemeOption(
                  context: context,
                  themeProvider: themeProvider,
                  themeMode: AppThemeMode.light,
                  title: "Light Mode",
                  subtitle: "Currently active theme",
                  icon: Icons.light_mode,
                  isEnabled: true,
                ),
                const SizedBox(height: 12),
                // Dark Mode (disabled)
                _buildThemeOption(
                  context: context,
                  themeProvider: themeProvider,
                  themeMode: AppThemeMode.dark,
                  title: "Dark Mode",
                  subtitle: "Coming soon",
                  icon: Icons.dark_mode,
                  isEnabled: false,
                ),
                const SizedBox(height: 12),
                // System Default (disabled)
                _buildThemeOption(
                  context: context,
                  themeProvider: themeProvider,
                  themeMode: AppThemeMode.system,
                  title: "System Default",
                  subtitle: "Coming soon",
                  icon: Icons.settings_system_daydream,
                  isEnabled: false,
                ),
                const SizedBox(height: 30),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.shadeColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.borderColor1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppColors.brandGreen,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            "Theme Information",
                            style: AppTextStyle.contentHeading0(
                              textColor: AppColors.title,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        "Light theme is currently the only available option. Dark mode and system theme will be available in a future update.",
                        style: AppTextStyle.smallText(
                          textColor: AppColors.writingColor2,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildThemeOption({
    required BuildContext context,
    required ThemeProvider themeProvider,
    required AppThemeMode themeMode,
    required String title,
    required String subtitle,
    required IconData icon,
    bool isEnabled = true,
  }) {
    final bool isSelected = themeProvider.themeMode == themeMode;

    return Opacity(
      opacity: isEnabled ? 1.0 : 0.6,
      child: AbsorbPointer(
        absorbing: !isEnabled,
        child: GestureDetector(
          onTap: isEnabled 
              ? () => themeProvider.setThemeMode(themeMode)
              : null,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.brandGreen.withOpacity(0.1)
                  : AppColors.appWhite,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? AppColors.brandGreen : AppColors.borderColor1,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.brandGreen : AppColors.shadeColor,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected ? Colors.white : AppColors.appBlack,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyle.contentHeading0(
                          textColor: AppColors.title,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: AppTextStyle.smallText(
                          textColor: AppColors.writingColor2,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: AppColors.brandGreen,
                    size: 24,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
