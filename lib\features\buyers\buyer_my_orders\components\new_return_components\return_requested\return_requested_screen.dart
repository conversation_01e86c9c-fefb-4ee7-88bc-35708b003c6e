import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_requested/return_requested_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ReturnRequestedScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final Order order;
  final BuyerSubOrderBloc buyerSuborderBloc;

  const ReturnRequestedScreen({
    Key? key,
    required this.suborderList,
    required this.order,
    required this.buyerSuborderBloc,
  }) : super(key: key);

  @override
  State<ReturnRequestedScreen> createState() => _ReturnRequestedScreenState();
}

class _ReturnRequestedScreenState extends State<ReturnRequestedScreen> {
  // region Bloc
  late ReturnRequestedBloc returnRequestedBloc;

  // endregion

  // region Init
  @override
  void initState() {
    returnRequestedBloc = ReturnRequestedBloc(context, widget.suborderList, widget.order, widget.buyerSuborderBloc);
    returnRequestedBloc.init();
    super.initState();
  }
  // endregion


  //region Dis update
  @override
  void didUpdateWidget(covariant ReturnRequestedScreen oldWidget) {
    returnRequestedBloc = ReturnRequestedBloc(context, widget.suborderList, widget.order, widget.buyerSuborderBloc);
    returnRequestedBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion
  //region Did change
  @override
  void didChangeDependencies() {
    returnRequestedBloc = ReturnRequestedBloc(context, widget.suborderList, widget.order, widget.buyerSuborderBloc);
    returnRequestedBloc.init();
    super.didChangeDependencies();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    returnRequestedBloc.dispose();
    super.dispose();
  }
  //endregion






  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(
          bottom: BorderSide(
            color: AppColors.lightStroke
          )
        )
      ),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          headerAlignment: ExpandablePanelHeaderAlignment.center,
          iconSize: 40,
          iconColor: AppColors.appBlack,
        ),
        //endregion

        //Return requested
        //region Header
        header: header(),
        //endregion
        collapsed: Container(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: returnRequestedBloc.suborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Column(
                          children: [
                            productInfoCard(context: context, subOrder: returnRequestedBloc.suborderList[index]),
                            verticalSizedBox(10),
                            // Return reason message
                            Text(
                              AppStrings.speakWithStoreAndUnderstand,
                              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                            ),
                            verticalSizedBox(10),
                            // Cancel return request button
                            Row(
                              children: [
                                AppCommonWidgets.subOrderButton(
                                  buttonName: "Cancel return request",
                                  onTap: () {
                                    returnRequestedBloc.cancelReturnRequest(subOrder: returnRequestedBloc.suborderList[index]);
                                  },
                                  horizontalPadding: 25
                                ),
                                const Expanded(child: SizedBox())
                              ],
                            ),
                            verticalSizedBox(10),
                          ],
                        ),
                        //Divider
                        Visibility(
                          visible: returnRequestedBloc.suborderList.length-1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }

  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.loading,
      componentName: "Return Requested",
      suborderList: returnRequestedBloc.suborderList,
      additionalWidgets: Container(
        alignment: Alignment.centerLeft,
        child: Text(
          "You have requested to return these products. The store will review and accept your return requests.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
      ),
      isEstimateDeliveryShow: false
    );
  }
  //endregion


  //endregion

  //region Product Info Card
  Widget productInfoCard({required BuildContext context, required SubOrder subOrder}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Product name
                  ///Brand and product name
                  RichText(
                    textScaler: MediaQuery.textScalerOf(AppConstants.globalNavigator.currentContext!),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: subOrder.productBrand,
                          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack,),
                        ),
                        TextSpan(
                          text: " ${subOrder.productName}",
                          style: AppTextStyle.contentText0(textColor: AppColors.appBlack,)
                        ),
                      ],
                    ),
                  ),
                  verticalSizedBox(5),
                  //Price
                  Text("₹${subOrder.sellingPrice} X ${subOrder.productQuantity} = ₹${subOrder.sellingPrice! * subOrder.productQuantity!}"
                      "${subOrder.suborderFeeDetails!.productLevelDeliveryFee! == 0?"":"    Delivery: ₹${subOrder.suborderFeeDetails!.productLevelDeliveryFee!}"}",
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack,),
                  ),
                  // Return reason if available
                  if (subOrder.returnReason != null && subOrder.returnReason!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(
                        "Return reason: ${subOrder.returnReason}",
                        style: AppTextStyle.heading3Regular(
                          textColor: AppColors.orange,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
            horizontalSizedBox(20),
            //Product image
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              child: Container(
                color: AppColors.textFieldFill1,
                height: 60,
                width: 60,
                child: extendedImage(
                  "${subOrder.productImage}",
                  customPlaceHolder: AppImages.productPlaceHolder,
                  context,
                  100,
                  100,
                  cache: true,
                )
              )
            )
          ],
        ),
      ),
    );
  }
  //endregion

  //region Divider
  Widget divider() {
    return Divider(
      color: AppColors.lightGray,
      height: 1,
      thickness: 1,
    );
  }
  //endregion
}
