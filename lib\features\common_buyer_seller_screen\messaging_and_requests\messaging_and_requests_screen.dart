// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/all_messages_screen.dart';
// import 'package:swadesic/features/common_buyer_seller_screen/your_invitees/your_invitees.dart';
// import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/account_balance/account_balance_screen.dart';
// import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/seller_rewards/seller_rewards.dart';
// import 'package:swadesic/features/widgets/app_left_align_tab_bar/app_left_align_tab_bar.dart';
// import 'package:swadesic/util/app_colors.dart';
// import 'package:swadesic/util/app_images.dart';
// import 'package:swadesic/util/app_strings.dart';
// import 'package:swadesic/util/app_text_style.dart';
// import 'package:swadesic/util/common_widgets.dart';
//
// class MessagingAndRequestScreen extends StatefulWidget {
//
//   const MessagingAndRequestScreen({Key? key,}) : super(key: key);
//
//   @override
//   _MessagingAndRequestScreenState createState() => _MessagingAndRequestScreenState();
// }
//
// class _MessagingAndRequestScreenState extends State<MessagingAndRequestScreen> with SingleTickerProviderStateMixin {
//   //Tab controller
//   late TabController tabController;
//
//   //region Init
//   @override
//   void initState() {
//     tabController = TabController(length: 2, vsync: this);
//     super.initState();
//   }
//
//   //endregion
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppColors.appWhite,
//       appBar: appBar(),
//       body: TabBarView(
//           controller: tabController,
//           children: [
//             AllMessagesScreen(),
//             requests()
//           ]
//       ),
//     );
//   }
//
//   //region Appbar
//   AppLeftAlignTabBar appBar() {
//     return AppLeftAlignTabBar(
//       title: "",
//       tabController: tabController,
//       tabs: [
//         AppStrings.messaging,
//         AppStrings.requests,
//
//       ],
//     );
//   }
//
// //endregion
//
//
//   //region Requests
//   Widget requests() {
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 16),
//       child: Column(
//         children: [
//           // Removed searchField() as it's not defined
//           Expanded(
//             child: Container(
//               alignment: Alignment.center,
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   SvgPicture.asset(
//                     AppImages.noMessageIcon,
//                     height: MediaQuery
//                         .of(context)
//                         .size
//                         .width / 2,
//                     width: MediaQuery
//                         .of(context)
//                         .size
//                         .width / 2,
//                   ),
//                   Text(
//                     "Messaging will soon be enabled. You can chat with other users & stores in Swadesic",
//                     textAlign: TextAlign.center,
//                     style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//
// }