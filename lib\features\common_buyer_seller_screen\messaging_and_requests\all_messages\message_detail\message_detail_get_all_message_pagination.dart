import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_bloc.dart';
import 'package:swadesic/model/messaging_response/messaging_detail.dart';
import 'package:swadesic/util/app_enums.dart';

class MessageDetailGetAllMessagePagination {
  final MessageDetailBloc messageDetailBloc;
  final BuildContext context;

  MessageDetailGetAllMessagePagination({
    required this.messageDetailBloc,
    required this.context,
  });

  //region Get initial message list
  Future<void> getInitialMessageList() async {
    messageDetailBloc.messageDetailResponse = await messageDetailBloc.messageChatDataModel.getInitialMessageDetail(
            toEntityReference: messageDetailBloc.toEntityReference,
            streamController: messageDetailBloc.messageDetailStateCtrl);

    //Add message list in list
    messageDetailBloc.messagesList = messageDetailBloc.messageDetailResponse.data!.messages!;


    //If message count is less than 10 then set current pagination empty state
    if (messageDetailBloc.messagesList.length < 10) {
      messageDetailBloc.paginationCurrentStateCtrl = AppStateEnum.Empty;
    }

    //Empty
    if(messageDetailBloc.messagesList.isEmpty){
      messageDetailBloc.paginationCurrentStateCtrl = AppStateEnum.Empty;
    }


    // Sort messages by timestamp (oldest to newest)
    messageDetailBloc.messagesList.sort((a, b) {
      var aIST = DateTime.parse(a.timestamp!).toUtc().add(Duration(hours: 5, minutes: 30));
      var bIST = DateTime.parse(b.timestamp!).toUtc().add(Duration(hours: 5, minutes: 30));
      return aIST.compareTo(bIST);
    });
  }
  //endregion

  //region Get pagination message list
  Future<void> getPaginationMessageList() async {
    if (messageDetailBloc.messagesList.isEmpty) return;

    // Get current scroll position
    final currentOffset = messageDetailBloc.scrollController.offset;

    // Get oldest message timestamp for anchor
    String oldestMessageTime = messageDetailBloc.messagesList.first.timestamp!;

    // Get more messages
    List<ChatDetail> newMessages = await messageDetailBloc.messageChatDataModel
        .getPaginationMessageDetail(
            streamController: messageDetailBloc.paginationStateCtrl,
            anchorTime: oldestMessageTime,
            toEntityReference: messageDetailBloc.toEntityReference,
            limit: 10);

    if (newMessages.isEmpty) {
      messageDetailBloc.paginationCurrentStateCtrl = AppStateEnum.Empty;
      messageDetailBloc.paginationStateCtrl.sink.add(AppStateEnum.Empty);
      return;
    }

    // Insert new messages at the beginning
    messageDetailBloc.messagesList.insertAll(0, newMessages);

    // Sort new messages
    messageDetailBloc.messagesList.sort((a, b) {
      var aIST = DateTime.parse(a.timestamp!).toUtc().add(Duration(hours: 5, minutes: 30));
      var bIST = DateTime.parse(b.timestamp!).toUtc().add(Duration(hours: 5, minutes: 30));
      return aIST.compareTo(bIST);
    });

    // Scroll to the same position
    //Send the index as per the message
    _scrollToIndex();

    // Notify UI of success
    messageDetailBloc.paginationStateCtrl.sink.add(AppStateEnum.Success);
  }
  //endregion



  void _scrollToIndex() {
    // Calculate the offset for the target index
    final double targetOffset = MediaQuery.of(context).size.height * 0.08;
    messageDetailBloc.scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }


}