import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';


class ContactInfo extends StatefulWidget {
  final List<String> phoneNumbers;
  final List<String> email;
  // final StoreInfo storeInfo;
  const ContactInfo({Key? key, required this.phoneNumbers, required this.email}) : super(key: key);

  @override
  State<ContactInfo> createState() => _ContactInfoState();
}

class _ContactInfoState extends State<ContactInfo> {
  @override
  Widget build(BuildContext context) {
    return body();
  }




  //region Body
  Widget body(){
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      // height: 300,
      width: double.maxFinite,
      child:   Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          contactList(phoneNumberList:widget.phoneNumbers),
          email(emailList:widget.email),
          verticalSizedBox(10),
          // Align(
          //     alignment: Alignment.centerLeft,
          //     child: appText("click to call/mail and hold to copy",color: AppColors.writingColor3,fontSize: 14,fontWeight: FontWeight.w400,fontFamily: AppConstants.rRegular)),
          // verticalSizedBox(20),


        ],
      ),
    );
  }
//endregion



//region List of Contact number
  Widget contactList({required List<String> phoneNumberList}){
    return ListView.builder(
        shrinkWrap: true,
        itemCount:phoneNumberList.length ,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder:(context,index){
          //If contact number is smalled then 10 then return
          if(phoneNumberList[index].length < 10){
            return const SizedBox();
          }
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: (){
                  CommonMethods.openDialPad(phoneNumber:phoneNumberList[index]);

                },
                onLongPress: (){
                  Navigator.of(context).pop();
                  CommonMethods.copyText(context, phoneNumberList[index]);
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child:Row(
                    children: [
                      Expanded(child: Text(phoneNumberList[index],style: AppTextStyle.access0(textColor: AppColors.appBlack),)),
                   SizedBox(
                     height: 25,
                     width: 25,
                     child: CupertinoButton(
                         padding: EdgeInsets.zero,
                         onPressed: (){

                           CommonMethods.messageOnWhatsApp(phoneNumber:phoneNumberList[index]);
                         },
                         child: SvgPicture.asset(AppImages.whatsappIcon,height: 25,width: 25,)),
                   ),
                    ],
                  )

                  // child: appText(phoneNumberList[index],color: AppColors.appBlack,fontSize: 16,fontWeight: FontWeight.w700,fontFamily: AppConstants.rRegular),
                ),
              ),
              divider(),
              verticalSizedBox(10),

            ],
          );
        });
  }
//endregion


//region List of email
  Widget email({required List<String> emailList}){
    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount:emailList.length ,
        itemBuilder:(context,index){
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: (){
                  CommonMethods.openEmail(emailId:emailList[index]);
                },
                onLongPress: (){
                  Navigator.of(context).pop();
                  CommonMethods.copyText(context, emailList[index]);
                },
                child: Container(
                  width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 10),
          child:Text(emailList[index],style: AppTextStyle.access0(textColor: AppColors.appBlack),)
                ),
              ),
              divider(),
              verticalSizedBox(10),

            ],
          );
        });
  }
//endregion

}
