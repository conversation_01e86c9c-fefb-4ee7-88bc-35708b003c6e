import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/rate_review/edit_review/edit_review_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Edit review
class EditReview extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;

  const EditReview(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<EditReview> createState() => _EditReviewState();
}
//endregion

class _EditReviewState extends State<EditReview> {
  //region Bloc
  late EditReviewBloc editReviewBloc;

  //endregion
  //region Init
  @override
  void initState() {
    editReviewBloc = EditReviewBloc(
        context, widget.subOrderList, widget.buyerSubOrderBloc, widget.order);
    editReviewBloc.init();
    super.initState();
  }
  //endregion

  //region Dis update
  @override
  void didUpdateWidget(covariant EditReview oldWidget) {
    editReviewBloc = EditReviewBloc(
        context, widget.subOrderList, widget.buyerSubOrderBloc, widget.order);
    editReviewBloc.init();
    super.didUpdateWidget(oldWidget);
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<EditReviewState>(
        stream: editReviewBloc.editReviewStateCtrl.stream,
        initialData: EditReviewState.Success,
        builder: (context, snapshot) {
          if (snapshot.data == EditReviewState.Success) {
            return body();
          }
          if (snapshot.data == EditReviewState.Loading) {
            return Center(child: AppCommonWidgets.appCircularProgress());
          }
          return const SizedBox();
        });
  }

  //region body

  Widget body() {
    return productDetailView(subOrder: editReviewBloc.subOrderList.first);
  }

//endregion

//region Product detail view
  Widget productDetailView({required SubOrder subOrder}) {
    // final TextEditingController comment = TextEditingController(text: subOrder.reviewComment ?? "");
    return Container(
      padding: const EdgeInsets.only(top: 25, left: 10, right: 10, bottom: 25),
      child: Column(
        children: [
          ///Image and name
          Row(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(6)),
                child: Container(
                  height: 50,
                  width: 50,
                  decoration: const BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(6))),
                  child: extendedImage(
                    subOrder.productImage!,
                    context,
                    100,
                    100,
                    cache: true,
                  ),
                ),
              ),
              horizontalSizedBox(20),
              Expanded(
                  child: Padding(
                padding: const EdgeInsets.only(right: 20),
                child: appText(subOrder.productName!,
                    color: AppColors.writingBlack,
                    fontWeight: FontWeight.w600,
                    fontFamily: AppConstants.rRegular,
                    fontSize: 14,
                    maxLine: 2),
              )),
            ],
          ),
          verticalSizedBox(10),

          ///Comment,rating and image
          Container(
            decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.circular(10)),
            padding: const EdgeInsets.all(10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///Comment,Update and done button
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      /*
                      If isEditEnable is true then change normal text to text field
                       */
                      child: editReviewBloc.isEditEnable
                          ? colorFilledTextField(
                              // onTapTextField: (){
                              //   //If rating has not added then show user to add rating first
                              //   if(subOrder.ratingValue == 0.0){
                              //     //Close keyboard
                              //     CommonMethods.closeKeyboard(context);
                              //     return CommonMethods.snackBar(AppStrings.pleaseRateTheProductFirst, context);
                              //   }
                              // },
                              context: context,
                              textFieldCtrl: editReviewBloc.commentTextCtrl,
                              hintText: "write your review..",
                              hintFontSize: 14,
                              textFieldMaxLine: 5,
                              // onChangeText: (){
                              //   //If rating has not added then show user to add rating first
                              //   if(subOrder.ratingValue == 0.0){
                              //     //Close keyboard
                              //     CommonMethods.closeKeyboard(context);
                              //     return CommonMethods.snackBar(AppStrings.pleaseRateTheProductFirst, context);
                              //   }
                              //
                              //   subOrder.reviewComment = comment.text;
                              // },
                              keyboardType: TextInputType.multiline,
                              textInputAction: TextInputAction.done,
                              // onChangeText: sellerOnBoardingBloc.onTextChange,
                              regExp: AppConstants.acceptAll,
                              fieldTextCapitalization:
                                  TextCapitalization.sentences,
                              maxCharacter: 200,
                            )
                          : Text(
                              editReviewBloc.subOrderList.first
                                  .replyAndComments!.comments!,
                              style: AppTextStyle.heading3Medium(
                                  textColor: AppColors.appBlack),
                            ),
                    ),
                    horizontalSizedBox(20),

                    ///Update and save button
                    editReviewBloc.isEditEnable
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              updateAndSave(
                                  textColor: AppColors.appWhite,
                                  borderColor: AppColors.brandBlack,
                                  buttonColor: AppColors.brandBlack,
                                  buttonName: AppStrings.save,
                                  onTap: () {
                                    editReviewBloc.onTapSave(
                                        subOrder: subOrder);
                                  }),
                              // verticalSizedBox(5),
                              updateAndSave(
                                  buttonColor: AppColors.appWhite,
                                  buttonName: AppStrings.cancel,
                                  onTap: () {
                                    editReviewBloc.onTapCancel();
                                  })
                            ],
                          )
                        : updateAndSave(
                            buttonColor: AppColors.appWhite,
                            buttonName: AppStrings.update,
                            onTap: () {
                              editReviewBloc.onTapUpdate();
                            }),
                  ],
                ),

                ///Ratting and add image
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 10),
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      RatingBar.builder(
                        ignoreGestures: !editReviewBloc.isEditEnable,
                        initialRating: subOrder.replyAndComments!.review!,
                        minRating: 1,
                        direction: Axis.horizontal,
                        glowColor: AppColors.yellow,
                        unratedColor: AppColors.lightGray,
                        allowHalfRating: false,
                        itemCount: 5,
                        //itemPadding: const EdgeInsets.symmetric(horizontal:2),
                        itemBuilder: (context, _) => const Icon(
                          Icons.star,
                          color: Colors.amber,
                        ),
                        onRatingUpdate: (rating) {
                          // subOrder.ratingValue = rating;
                          //If rating has not added then show user to add rating first
                          subOrder.replyAndComments!.review = rating;
                          // if (rating == 0.0) {
                          //   subOrder.ratingValue = 0.0;
                          //   subOrder.reviewComment = "";
                          //   subOrder.reviewImages.clear();
                          //   //refresh
                          //   //rateReviewBloc.rateReviewStateCtrl.sink.add(RateReviewState.Success);
                          //   //Close keyboard
                          //   CommonMethods.closeKeyboard(context);
                          // }
                          //buyerProductCommentBloc.productRating = rating.round();
                          // //print(buyerProductCommentBloc.productRating);
                        },
                      ),
                      Expanded(child: horizontalSizedBox(10)),

                      ///Add image button
                      Visibility(
                        visible: editReviewBloc.isEditEnable,
                        child: InkWell(
                          onTap: () {
                            //If rating has not added then show user to add rating first
                            // if (subOrder.ratingValue == 0.0) {
                            //   return CommonMethods.snackBar(AppStrings.pleaseRateTheProductFirst, context);
                            // }
                            editReviewBloc.goToBuyerAddImageScreen(
                                subOrder: subOrder);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 5),
                            decoration: BoxDecoration(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(10)),
                                border: Border.all(color: AppColors.lightGray)),
                            child: Text(
                              "add images",
                              style: TextStyle(
                                  fontFamily: AppConstants.rRegular,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 15,
                                  color: AppColors.writingColor3),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                ///Review images
                reviewImages(subOrder: subOrder),
              ],
            ),
          )

          //verticalSizedBox(20),
          // colorFilledTextField(
          //   onTapTextField: (){
          //     //If rating has not added then show user to add rating first
          //     if(subOrder.ratingValue == 0.0){
          //       //Close keyboard
          //       CommonMethods.closeKeyboard(context);
          //       return CommonMethods.snackBar(AppStrings.pleaseRateTheProductFirst, context);
          //     }
          //   },
          //   context: context,
          //   textFieldCtrl:comment,
          //   hintText: "write your review..",
          //   hintFontSize: 14,
          //   textFieldMaxLine: 1,
          //   onChangeText: (){
          //     //If rating has not added then show user to add rating first
          //     if(subOrder.ratingValue == 0.0){
          //       //Close keyboard
          //       CommonMethods.closeKeyboard(context);
          //       return CommonMethods.snackBar(AppStrings.pleaseRateTheProductFirst, context);
          //     }
          //
          //     subOrder.reviewComment = comment.text;
          //   },
          //   keyboardType: TextInputType.streetAddress,
          //   textInputAction: TextInputAction.done,
          //   // onChangeText: sellerOnBoardingBloc.onTextChange,
          //   regExp: AppConstants.onlyStringWithSpace,
          //   fieldTextCapitalization: TextCapitalization.sentences,
          //   maxCharacter: 50,
          // ),
        ],
      ),
    );
  }

//endregion

  //region Review images
  Widget reviewImages({required SubOrder subOrder}) {
    ///Network and local images
    return Visibility(
      visible: editReviewBloc.subOrderList.first.replyAndComments!
              .commentReviewImage!.isNotEmpty ||
          subOrder.reviewImages.isNotEmpty,
      child: SizedBox(
        height: 100,
        width: double.infinity,
        child: ListView(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          children: <Widget>[
            ///Network image
            editReviewBloc.subOrderList.first.replyAndComments!
                    .commentReviewImage!.isNotEmpty
                ? ListView.builder(
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    physics: const NeverScrollableScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    itemCount: editReviewBloc.subOrderList.first
                        .replyAndComments!.commentReviewImage!.length,
                    itemBuilder: (context, index) {
                      return Container(
                        height: 100,
                        width: 100,
                        margin: const EdgeInsets.only(right: 10),
                        child: Stack(
                          children: [
                            InkWell(
                              onTap: () {
                                //If edit mode is active then return or else open
                                if (!editReviewBloc.isEditEnable) {
                                  editReviewBloc.goToBuyerProductImageScreen(
                                      productImage: subOrder.replyAndComments!
                                          .commentReviewImage!,
                                      index: index);
                                } else {
                                  return;
                                }
                              },
                              child: extendedImage(
                                  editReviewBloc
                                          .subOrderList
                                          .first
                                          .replyAndComments!
                                          .commentReviewImage![index]
                                          .reviewImage ??
                                      "",
                                  context,
                                  100,
                                  100,
                                  imageWidth: 100,
                                  imageHeight: 100,
                                  fit: BoxFit.cover),
                            ),
                            Visibility(
                              visible: editReviewBloc.isEditEnable,
                              child: Positioned(
                                  right: 5,
                                  top: 5,
                                  child: InkWell(
                                      onTap: () {
                                        editReviewBloc.deleteCommentImage(
                                            commentReviewImage: editReviewBloc
                                                .subOrderList
                                                .first
                                                .replyAndComments!
                                                .commentReviewImage![index]);
                                        // buyerProductCommentBloc.deleteCommentImage(buyerProductCommentBloc.commentReviewImage![index].reviewImageId!);
                                      },
                                      child: Icon(
                                        Icons.close,
                                        color: AppColors.appBlack,
                                      ))),
                            )
                          ],
                        ),
                      );
                    })
                : const SizedBox(),

            ///Local Image
            editReviewBloc.subOrderList.first.reviewImages.isNotEmpty
                ? ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: subOrder.reviewImages.length,
                    scrollDirection: Axis.horizontal,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      return Stack(
                        children: [
                          Container(
                            margin: const EdgeInsets.only(right: 10),
                            width: 100,
                            height: 100,
                            // child: Text(subOrder.reviewImages[index].path),
                            child: Image.file(
                              fit: BoxFit.fill,
                              subOrder.reviewImages[index],
                              cacheHeight: 100,
                              cacheWidth: 100,
                              height: 100,
                              width: 100,
                            ),
                          ),
                          Positioned(
                              top: 0,
                              right: 10,
                              child: SizedBox(
                                height: 30,
                                width: 30,
                                child: CupertinoButton(
                                  padding: EdgeInsets.zero,
                                  onPressed: () {
                                    editReviewBloc.removeReviewImage(
                                        subOrder: subOrder, imageIndex: index);
                                  },
                                  child: Icon(
                                    Icons.close,
                                    color: AppColors.appBlack,
                                  ),
                                ),
                              ))
                        ],
                      );
                    })
                : Container(),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Add your review
  Widget addYourReview() {
    return InkWell(
      onTap: () {
        //rateReviewBloc.onTapAddReview();
      },
      child: Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.symmetric(horizontal: 30),
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(80)),
              color: AppColors.brandBlack),
          child: appText(
            AppStrings.addYourReview,
            color: AppColors.appWhite,
            fontWeight: FontWeight.w700,
            fontFamily: AppConstants.rRegular,
            fontSize: 15,
          )),
    );
  }

//endregion

//region Update and save
  Widget updateAndSave(
      {required String buttonName,
      required dynamic onTap,
      required buttonColor,
      Color? textColor,
      Color? borderColor}) {
      textColor = textColor?? AppColors.appBlack;
      borderColor = borderColor?? AppColors.darkGray;
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        decoration: BoxDecoration(
            color: buttonColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: borderColor)),
        child: Text(
          buttonName,
          style: AppTextStyle.smallText(textColor: textColor),
        ),
      ),
    );
  }
//endregion
}
