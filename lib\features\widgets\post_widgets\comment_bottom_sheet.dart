import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/post/single_post_view/comment_field/comment_field.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_enums.dart';

class CommentBottomSheet extends StatefulWidget {
  final SinglePostViewBloc singlePostViewBloc;
  final VoidCallback? onCommentAdded;
  final bool isReply;
  final String? replyToHandle;

  const CommentBottomSheet({
    super.key,
    required this.singlePostViewBloc,
    this.onCommentAdded,
    this.isReply = false,
    this.replyToHandle,
  });

  @override
  State<CommentBottomSheet> createState() => _CommentBottomSheetState();

  static void show({
    required BuildContext context,
    required SinglePostViewBloc singlePostViewBloc,
    VoidCallback? onCommentAdded,
    bool isReply = false,
    String? replyToHandle,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CommentBottomSheet(
        singlePostViewBloc: singlePostViewBloc,
        onCommentAdded: onCommentAdded,
        isReply: isReply,
        replyToHandle: replyToHandle,
      ),
    );
  }
}

class _CommentBottomSheetState extends State<CommentBottomSheet> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.appWhite,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // App Bar
            _buildAppBar(),
            
            // User Info
            _buildUserInfo(),
            
            // Comment Input Area
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Reply to indicator if it's a reply
                  if (widget.isReply && widget.replyToHandle != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text(
                        'Replying to @${widget.replyToHandle}',
                        style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
                      ),
                    ),
                  
                  // Comment field
                  CommentField(singlePostViewBloc: widget.singlePostViewBloc),
                ],
              ),
            ),
            
            // Bottom spacing
            SizedBox(height: MediaQuery.of(context).viewInsets.bottom > 0 
                ? MediaQuery.of(context).viewInsets.bottom + 10 
                : 20),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: AppColors.borderColor1, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Close button
          IconButton(
            icon: Icon(Icons.close, color: AppColors.appBlack, size: 24),
            onPressed: () => Navigator.pop(context),
          ),
          
          // Title
          Text(
            widget.isReply ? 'Reply' : 'Add Comment',
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          
          // Post button
          TextButton(
            onPressed: () {
              // Handle post comment
              widget.singlePostViewBloc.commentFieldsBloc.sendComment(
                replyCommentOrPostDetail: widget.singlePostViewBloc.replyCommentOrPostDetail,
                commentEnums: widget.isReply ? CommentEnums.COMMENT : CommentEnums.COMMENT,
              ).then((_) {
                widget.onCommentAdded?.call();
                Navigator.pop(context);
              });
            },
            child: Text(
              'Post',
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildUserInfo() {
    final loggedInUser = Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    final loggedInStore = Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // User Avatar
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.textFieldFill1,
              image: AppConstants.appData.isUserView!
                  ? (loggedInUser.userDetail?.icon != null
                      ? DecorationImage(
                          image: NetworkImage(loggedInUser.userDetail!.icon!), 
                          fit: BoxFit.cover,
                        )
                      : null)
                  : (loggedInStore.storeInfo?.icon != null
                      ? DecorationImage(
                          image: NetworkImage(loggedInStore.storeInfo!.icon!),
                          fit: BoxFit.cover,
                        )
                      : null),
            ),
            child: AppConstants.appData.isUserView!
                ? (loggedInUser.userDetail?.icon == null
                    ? Icon(Icons.person, color: AppColors.writingColor2)
                    : null)
                : (loggedInStore.storeInfo?.icon == null
                    ? Icon(Icons.store, color: AppColors.writingColor2)
                    : null),
          ),
          
          const SizedBox(width: 12),
          
          // User Name and Verification Badge
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      AppConstants.appData.isUserView!
                          ? loggedInUser.userDetail?.userName ?? 'User'
                          : loggedInStore.storeInfo?.storeName ?? 'Store',
                      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                    ),
                    const SizedBox(width: 4),
                    VerifiedBadge(
                      width: 16,
                      height: 16,
                      subscriptionType: AppConstants.appData.isUserView!
                          ? loggedInUser.userDetail?.subscriptionType
                          : loggedInStore.storeInfo?.subscriptionType,
                    ),
                  ],
                ),
                Text(
                  AppConstants.appData.isUserView!
                      ? '@${loggedInUser.userDetail?.userName ?? ''}'
                      : '@${loggedInStore.storeInfo?.storehandle ?? ''}',
                  style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
