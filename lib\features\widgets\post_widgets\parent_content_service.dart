import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/single_product_and_image_service/single_product_and_image_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'dart:developer' as developer;

/// Service to fetch parent content for comments and replies
class ParentContentService {
  static const String _tag = 'ParentContentService';

  /// Fetches parent content based on reference ID
  /// Returns PostDetail for posts/comments or Product for products
  static Future<dynamic> getParentContent(String reference) async {
    try {
      developer.log(
        '[ENTER] getParentContent(): Fetching parent content for reference: $reference',
        name: _tag
      );

      if (reference.isEmpty) {
        developer.log(
          '[EXIT] getParentContent(): Empty reference provided',
          name: _tag
        );
        return null;
      }

      // Determine content type based on reference prefix
      if (reference.startsWith('P') && !reference.startsWith('PO')) {
        // Product reference
        return await _getProductContent(reference);
      } else if (reference.startsWith('PO')) {
        // Post reference
        return await _getPostContent(reference);
      } else if (reference.startsWith('CO')) {
        // Comment reference
        return await _getCommentContent(reference);
      }

      developer.log(
        '[EXIT] getParentContent(): Unknown reference type for: $reference',
        name: _tag
      );
      return null;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] getParentContent(): Failed to fetch parent content: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Fetches product content
  static Future<Product?> _getProductContent(String productReference) async {
    try {
      developer.log(
        '[ENTER] _getProductContent(): Fetching product for reference: $productReference',
        name: _tag
      );

      final response = await SingleProductAndImageService().getSingleProductInfo(
        productReference: productReference,
        pinCode: AppConstants.appData.pinCode ?? '110001',
      );

      if (response.message == 'success' && response.singleProduct != null) {
        developer.log(
          '[EXIT] _getProductContent(): Successfully fetched product',
          name: _tag
        );
        return response.singleProduct!;
      }

      developer.log(
        '[EXIT] _getProductContent(): No product found for reference: $productReference',
        name: _tag
      );
      return null;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] _getProductContent(): Failed to fetch product: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Fetches post content
  static Future<PostDetail?> _getPostContent(String postReference) async {
    try {
      developer.log(
        '[ENTER] _getPostContent(): Fetching post for reference: $postReference',
        name: _tag
      );

      final postDetail = await PostService().getSinglePost(
        postReference: postReference,
      );

      developer.log(
        '[EXIT] _getPostContent(): Successfully fetched post',
        name: _tag
      );
      return postDetail;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] _getPostContent(): Failed to fetch post: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Fetches comment content
  static Future<PostDetail?> _getCommentContent(String commentReference) async {
    try {
      developer.log(
        '[ENTER] _getCommentContent(): Fetching comment for reference: $commentReference',
        name: _tag
      );

      final commentDetail = await PostService().getSingleComment(
        commentReference: commentReference,
      );

      developer.log(
        '[EXIT] _getCommentContent(): Successfully fetched comment',
        name: _tag
      );
      return commentDetail;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] _getCommentContent(): Failed to fetch comment: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Gets the parent chain for a given content
  /// Returns a list of parent content from immediate parent to highest parent
  static Future<List<dynamic>> getParentChain(String reference, {String? parentCommentId, String? mainParentId}) async {
    try {
      developer.log(
        '[ENTER] getParentChain(): Building parent chain for reference: $reference',
        name: _tag
      );

      List<dynamic> parentChain = [];

      // Start with immediate parent if available
      String? currentParentId = parentCommentId;
      
      while (currentParentId != null && currentParentId.isNotEmpty) {
        final parentContent = await getParentContent(currentParentId);
        if (parentContent != null) {
          parentChain.add(parentContent);
          
          // Get next parent if this is a comment/post
          if (parentContent is PostDetail) {
            currentParentId = parentContent.parentCommentId;
            // If no more parent comments, check for main parent
            if ((currentParentId == null || currentParentId.isEmpty) && 
                parentContent.mainParentId != null && 
                parentContent.mainParentId!.isNotEmpty &&
                parentContent.mainParentId != reference) {
              currentParentId = parentContent.mainParentId;
            } else if (currentParentId == null || currentParentId.isEmpty) {
              break;
            }
          } else {
            // If it's a product, it's the highest parent
            break;
          }
        } else {
          break;
        }
      }

      // If we haven't reached the main parent yet, fetch it
      if (mainParentId != null && 
          mainParentId.isNotEmpty && 
          mainParentId != reference &&
          (parentChain.isEmpty || 
           (parentChain.last is PostDetail && (parentChain.last as PostDetail).postOrCommentReference != mainParentId) ||
           (parentChain.last is Product && (parentChain.last as Product).productReference != mainParentId))) {
        final mainParentContent = await getParentContent(mainParentId);
        if (mainParentContent != null) {
          parentChain.add(mainParentContent);
        }
      }

      developer.log(
        '[EXIT] getParentChain(): Built parent chain with ${parentChain.length} items',
        name: _tag
      );
      return parentChain;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] getParentChain(): Failed to build parent chain: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return [];
    }
  }

  /// Determines the content type based on reference prefix
  static String getContentType(String reference) {
    if (reference.startsWith('P') && !reference.startsWith('PO')) {
      return 'PRODUCT';
    } else if (reference.startsWith('PO')) {
      return 'POST';
    } else if (reference.startsWith('CO')) {
      return 'COMMENT';
    }
    return 'UNKNOWN';
  }
}
