import 'dart:async';

import 'package:contacts_service/contacts_service.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customer_or_friends_disclaimer/find_your_customer_or_friends_disclaimer.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/find_your_customers_response/get_contact_user_and_store_info_response.dart';
import 'package:swadesic/model/maintenance_response/maintenance_response.dart';
import 'package:swadesic/services/app_permission_handler/app_permission_handler.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/services/maintenance_services/maintenance_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum FollowAndSupportAllButtonState { Loading, Success, Failed, Empty}

class FollowAndSupportAllButtonBloc {
  // region Common Methods
  BuildContext context;
  final FindYourCustomersBloc findYourCustomersBloc;
  final String reference;
  bool followAndSupportVisible = false;
  bool isUndoVisible = false;
  late EntityType entityType;

  //All followed User
  List<ContactUserAndStoreInfo> allFollowedUser = [];

  //All supported store
  List<ContactUserAndStoreInfo> allSupportedStores = [];

  // endregion
  //region Controller
  final followAndSupportAllButtonStateCtrl = StreamController<FollowAndSupportAllButtonState>.broadcast();
  final followAndSupportVisibleCtrl = StreamController<bool>.broadcast();

  //endregion

  // region | Constructor |
  FollowAndSupportAllButtonBloc(this.context, this.findYourCustomersBloc, this.reference);

  // endregion

  // region Init
  init() {

  }

  // endregion



  ///On tap button 1
  //region On tap Follow contacts only
  void onTapFollowContactsOnly()async{

    //Clear all contact contact list
    allFollowedUser.clear();

    //Hide the buttons and make undo visible
    followAndSupportVisibleCtrl.sink.add(false);
    //Refresh all contact follow/unfollow/support
    findYourCustomersBloc.findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);

    //1. Take out all unregister and register contact and save in allFollowedContacts
    // for(var data in findYourCustomersBloc.searchResult){
    //   if(data.entityType == EntityType.USER.name || data.entityType == EntityType.UNREGISTERED.name && data.followStatus! != "Pending" || data.followStatus! != "Following"){
    //     //Add all register and un-register user in followed list
    //     allFollowedUser.add(data.copy());
    //     //Mark all user and un-register user as following
    //     if(data.entityType == "user"){
    //       data.followStatus = "Following";
    //     }
    //     else{
    //       data.followStatus = "Pending";
    //     }
    //
    //   }
    // }

    //2. Follow all register and un-register user
    for(var data in allFollowedUser){
      // data.followStatus = "Following";
    }

    ///Api call
    //Follow all user and user
    // await followAndUnFollowAllUserApiCall(toFollow: true, allUserList: allFollowedUser);





  }
  //endregion

  ///On tap button 2
  //region On tap Support stores only
  void onTapSupportsOnly()async{

    //Clear all store list
    allSupportedStores.clear();

    //Hide the buttons and make undo visible
    followAndSupportVisibleCtrl.sink.add(false);
    //Refresh all contact follow/unfollow/support
    findYourCustomersBloc.findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);

    //1. Take out all stores
    // for(var data in findYourCustomersBloc.searchResult){
    //   if(data.entityType == "store" && data.entityType != "Supporting"){
    //     //Add all store list
    //     allSupportedStores.add(data.copy());
    //     //Mark all store as supported
    //     data.followStatus = "Supporting";
    //   }
    // }

    ///Api call
    //Support all store
    // await supportAllStoreApiCall(toFollow: true, allStoreList: allSupportedStores);





  }
  //endregion

  
  
  //region On tap undo
  void onTapUndo()async{

    //All user
    // if(allFollowedUser.isNotEmpty){
    //   //Make all user to default state
    //   // for (var data in findYourCustomersBloc.searchResult) {
    //   //   //All followed user
    //   //   for(var user in allFollowedUser){
    //   //     //Remove from main list
    //   //     findYourCustomersBloc.searchResult.removeWhere((element) => element.phoneNumber == user.phoneNumber);
    //   //     //Add old data
    //   //     findYourCustomersBloc.searchResult.add(user);
    //   //   }
    //   // }
    //   //Api call and mark all user to unfollow
    //   followAndUnFollowAllUserApiCall(toFollow: false, allUserList: allFollowedUser);
    // }
    // //All Store
    // if(allSupportedStores.isNotEmpty){
    //   //Make all store to default state
    //   for (var data in findYourCustomersBloc.searchResult) {
    //     //All followed user
    //     for(var user in allSupportedStores){
    //       //Remove from main list
    //       findYourCustomersBloc.searchResult.removeWhere((element) => element.reference == user.reference);
    //       //Add old data
    //       findYourCustomersBloc.searchResult.add(user);
    //     }
    //   }
    //   //Api call and mark all store to unsupported
    //   supportAllStoreApiCall(toFollow: false, allStoreList: allSupportedStores);
    // }

    //Sort
    findYourCustomersBloc.searchResult.sort((a, b) => a.name!.compareTo(b.name!));
    //Clear followed all user list
    allFollowedUser.clear();
    //Clear supported store list
    allSupportedStores.clear();
    //Hide the buttons and make undo visible
    followAndSupportVisibleCtrl.sink.add(false);
    //Refresh all contact follow/unfollow/support
    findYourCustomersBloc.findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);

  }
  //endregion




///Button 1

  // region On tap follow
  Future<void> onTapButton({required FollowEnum followEnum})async{
  try{
    //Loading
    List<ContactUserAndStoreInfo> updatedData = await FindYourCustomersServices().followAndUnFollowAll(entityType: entityType, followEnum:followEnum);

    //If Entity type is User and updated data is empty
    if(entityType == EntityType.USER && updatedData.isEmpty){
      return context.mounted?CommonMethods.toastMessage(AppStrings.thereAreNoMoreContactToFollow, context):null;
    }

    //If Entity type is Store and updated data is empty
    if(entityType == EntityType.STORE && updatedData.isEmpty){
      return context.mounted?CommonMethods.toastMessage(AppStrings.thereAreNoMoreStoreToFollow, context):null;
    }

    //If Entity type is Both and updated data is empty
    if(entityType == EntityType.BOTH && updatedData.isEmpty){
      return context.mounted?CommonMethods.toastMessage(AppStrings.thereAreNoMoreContactOrStoreToFollow, context):null;
    }

    //Visible and hide Undo button
    isUndoVisible = followEnum == FollowEnum.FOLLOW?true:false;

    //Add and replace
    for (var data in updatedData) {
      //Remove the data if reference is same
      findYourCustomersBloc.contactUserAndStoreInfoList.removeWhere((element) => element.reference == data.reference);
      //Add data to the list
      findYourCustomersBloc.contactUserAndStoreInfoList.add(data);
    }
    //Clear search
    findYourCustomersBloc.searchResult.clear();
    //Add api data to search result
    findYourCustomersBloc.searchResult.addAll(findYourCustomersBloc.contactUserAndStoreInfoList);
    //Assign name
    await findYourCustomersBloc.assignTheNameToUnRegisterContact();
    //Sort
    findYourCustomersBloc.searchResult.sort((a, b) => a.name!.compareTo(b.name!));
    //Refresh list
    findYourCustomersBloc.findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
    //Refresh
    followAndSupportAllButtonStateCtrl.sink.add(FollowAndSupportAllButtonState.Success);

  }
  on ApiErrorResponseMessage {
    //Success
    context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    // findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
  }
  catch(error){
    context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
  }
  }
//endregion





  //region On tap arrow
  void onTapArrow({required bool value}){
    followAndSupportVisibleCtrl.sink.add(!value);
  }
  //endregion

//region Dispose
  void dispose() {

    followAndSupportAllButtonStateCtrl.close();
  }
//endregion

}


