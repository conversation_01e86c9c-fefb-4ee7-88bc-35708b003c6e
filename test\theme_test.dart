import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/providers/theme_provider.dart';
import 'package:swadesic/util/app_themes.dart';
import 'package:swadesic/util/theme_mode.dart';

void main() {
  group('Theme Provider Tests', () {
    testWidgets('Theme provider initializes with system theme', (WidgetTester tester) async {
      final themeProvider = ThemeProvider();
      
      expect(themeProvider.themeMode, AppThemeMode.system);
    });

    testWidgets('Theme provider can change theme mode', (WidgetTester tester) async {
      final themeProvider = ThemeProvider();
      
      // Change to light mode
      await themeProvider.setThemeMode(AppThemeMode.light);
      expect(themeProvider.themeMode, AppThemeMode.light);
      
      // Change to dark mode
      await themeProvider.setThemeMode(AppThemeMode.dark);
      expect(themeProvider.themeMode, AppThemeMode.dark);
    });

    testWidgets('Theme provider correctly identifies dark mode', (WidgetTester tester) async {
      final themeProvider = ThemeProvider();
      
      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeProvider,
          child: MaterialApp(
            theme: AppThemes.lightTheme,
            darkTheme: AppThemes.darkTheme,
            home: Builder(
              builder: (context) {
                // Test light mode
                themeProvider.setThemeMode(AppThemeMode.light);
                expect(themeProvider.isDarkMode(context), false);
                
                // Test dark mode
                themeProvider.setThemeMode(AppThemeMode.dark);
                expect(themeProvider.isDarkMode(context), true);
                
                return Container();
              },
            ),
          ),
        ),
      );
    });
  });

  group('App Themes Tests', () {
    test('Light theme has correct properties', () {
      final lightTheme = AppThemes.lightTheme;
      
      expect(lightTheme.brightness, Brightness.light);
      expect(lightTheme.useMaterial3, false);
    });

    test('Dark theme has correct properties', () {
      final darkTheme = AppThemes.darkTheme;
      
      expect(darkTheme.brightness, Brightness.dark);
      expect(darkTheme.useMaterial3, false);
    });
  });

  group('Theme Mode Extension Tests', () {
    test('Theme mode extension returns correct names', () {
      expect(AppThemeMode.light.name, 'Light');
      expect(AppThemeMode.dark.name, 'Dark');
      expect(AppThemeMode.system.name, 'System');
    });

    test('Theme mode extension returns correct values', () {
      expect(AppThemeMode.light.value, 'light');
      expect(AppThemeMode.dark.value, 'dark');
      expect(AppThemeMode.system.value, 'system');
    });

    test('Theme mode fromString works correctly', () {
      expect(AppThemeModeExtension.fromString('light'), AppThemeMode.light);
      expect(AppThemeModeExtension.fromString('dark'), AppThemeMode.dark);
      expect(AppThemeModeExtension.fromString('system'), AppThemeMode.system);
      expect(AppThemeModeExtension.fromString('invalid'), AppThemeMode.system);
    });
  });
}
