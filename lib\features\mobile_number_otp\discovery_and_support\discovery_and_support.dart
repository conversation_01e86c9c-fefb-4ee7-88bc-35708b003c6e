import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:url_launcher/url_launcher.dart';

class DiscoveryAndSupport extends StatefulWidget {
  final MobileNumberOtpBloc mobileNumberOtpBloc;
  const DiscoveryAndSupport({super.key, required this.mobileNumberOtpBloc});

  @override
  State<DiscoveryAndSupport> createState() => _DiscoveryAndSupportState();
}

class _DiscoveryAndSupportState extends State<DiscoveryAndSupport> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Container(
      alignment: Alignment.centerLeft,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          titleAndSubTitle(
            title: Text(
              "Discover & Shop directly from India's Growing Brands",
              style: AppTextStyle.exHeading1(
                textColor: AppColors.appBlack,
              )
            ),
            subTitle: "or Start one!",
          ),
          // titleAndSubTitle(
          //   title: Text(
          //     "Shop Safely",
          //     style: AppTextStyle.exHeading1(
          //       textColor: AppColors.brandGreen,
          //     ),
          //   ),
          //   subTitle: "from Small Businesses Online",
          // ),
          // titleAndSubTitle(
          //   title: Text(
          //     "Build Together",
          //     style: AppTextStyle.exHeading1(
          //       textColor: AppColors.brandGreen,
          //     ),
          //   ),
          //   subTitle: "the Swadeshi Community & Economy",
          // ),
          // titleAndSubTitle(
          //   // title: "or Start Your Own Business ",
          //   title: RichText(
          //     textAlign: TextAlign.center,
          //     text: TextSpan(
          //       children: <TextSpan>[
          //         TextSpan(
          //           text: 'Or Start Your Store',
          //           style: AppTextStyle.exHeading1(
          //               textColor: AppColors.brandGreen),
          //         ),
          //       ],
          //     ),
          //   ),
          //   subTitle: "to own your Business & Community for Free",
          // ),
          options(),
        ],
      ),
    );
  }
//endregion

  //region Options
  Widget options() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
            onTap: () {
              widget.mobileNumberOtpBloc.openIntroSliderScreen();
            },
            child: Text(
              AppStrings.seeWhySwadesicMatters,
              style: AppTextStyle.subTitle(
                  textColor: AppColors.appBlack, isUnderline: true),
            )),
        const SizedBox(width: 10),
        InkWell(
            onTap: () {
              Navigator.push(
                context,
                CupertinoPageRoute(
                  builder: (context) => const CommonReferralPage(),
                ),
              );
            },
            child: Text(
              AppStrings.learnMore,
              style: AppTextStyle.subTitle(
                  textColor: AppColors.appBlack, isUnderline: true),
            )),
        const SizedBox(width: 10),
        // Text(AppStrings.appSupport,style: AppTextStyle.subTitle(textColor: AppColors.appBlack,isUnderline: true),),
      ],
    );
  }
  //endregion

//region Title and sub title
  Widget titleAndSubTitle({required Widget title, required String subTitle}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 13),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Text(title,style: AppTextStyle.exHeading1(textColor: AppColors.appBlack,),),
          title,
          const SizedBox(height: 13),
          Text(
            subTitle,
            style: AppTextStyle.exHeading2(textColor: AppColors.appBlack),
          ),
        ],
      ),
    );
  }
//endregion
}
