import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/delivered_successfully_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/return_windows_calculate_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class DeliveredSuccessfully extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const DeliveredSuccessfully({Key? key, required this.subOrderList, required this.buyerSubOrderBloc, required this.order,}) : super(key: key);

  @override
  State<DeliveredSuccessfully> createState() => _DeliveredSuccessfullyState();
}

class _DeliveredSuccessfullyState extends State<DeliveredSuccessfully> {

  // region Bloc
  late DeliveredSuccessfullyBloc deliveredSuccessfullyBloc;

  // endregion

  // region Init
  @override
  void initState() {
    deliveredSuccessfullyBloc = DeliveredSuccessfullyBloc(context,widget.order,widget.buyerSubOrderBloc,widget.subOrderList);
    deliveredSuccessfullyBloc.init();
    super.initState();
  }

  // endregion

  //region Dis update
  @override
  void didUpdateWidget(covariant DeliveredSuccessfully oldWidget) {
    deliveredSuccessfullyBloc = DeliveredSuccessfullyBloc(context,widget.order,widget.buyerSubOrderBloc,widget.subOrderList);
    deliveredSuccessfullyBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion


  //region Build

  //region Build
  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion


  //region Body
  Widget body(){
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount:deliveredSuccessfullyBloc.groupNameList.length,
        itemBuilder: (context,index){
          return deliveredGroup(groupName: deliveredSuccessfullyBloc.groupNameList[index]);
        });
  }
  //endregion



  //region Delivered group
  Widget deliveredGroup({required String groupName}){
    List<SubOrder> groupedSuborderList = [];
    ///Add all suborders to the suborder list as per the display package number
    groupedSuborderList = deliveredSuccessfullyBloc.subOrderList.where((element) =>element.displayPackageNumber==groupName).toList();
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(
              bottom: BorderSide(
                  color: AppColors.lightStroke
              )
          )
      ),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(headerOrderList:groupedSuborderList,groupName: groupName ),
        //endregion
        collapsed: rateSuggestAndNotDelivered(groupedSubOrders:groupedSuborderList ),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            rateSuggestAndNotDelivered(groupedSubOrders: groupedSuborderList),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: groupedSuborderList.length,
                  itemBuilder: (context, index) {
                    ///Return window detail
                    late ReturnWindowsCalculateModel returnWindowsCalculateModel = CommonMethods.calculateReturnWindows(
                        subOrder:groupedSuborderList[index]);

                    //print(returnWindowsCalculateModel);


                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              verticalSizedBox(10),

                              ///Return window open
                              Visibility(
                                visible:returnWindowsCalculateModel.isReturnAvailable && returnWindowsCalculateModel.isReturnDateComing,
                                child: Text("${returnWindowsCalculateModel.returnMessage}${returnWindowsCalculateModel.returnDate}",
                                style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack,),
                                ),
                              ),
                              ///Return window closed
                              Visibility(
                                visible:returnWindowsCalculateModel.isReturnAvailable && returnWindowsCalculateModel.isReturnDatePassed,
                                child: Text("${returnWindowsCalculateModel.returnMessage}${returnWindowsCalculateModel.returnDate}",
                                  style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack,),
                                ),
                              ),
                              ///No return accepted
                              Visibility(
                                visible:!returnWindowsCalculateModel.isReturnAvailable,
                                child: Text(returnWindowsCalculateModel.returnMessage,
                                  style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack,),
                                ),
                              ),

                              productInfoCard(context: context, subOrder: groupedSuborderList[index]),
                              //Buttons
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ///Rate and review
                                  Padding(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: AppCommonWidgets.subOrderButton(
                                        buttonName: AppStrings.rateReview,
                                        onTap: () {
                                          deliveredSuccessfullyBloc.goToProductComments(subOrder:groupedSuborderList[index] );
                                          // deliveredSuccessfullyBloc.onTapRateReview(selectedSuborderList:[ groupedSuborderList[index]]);
                                          // //Mark only selected
                                          // waitingForConfirmationBloc.suborderList[index].isSelected = true;
                                          // //Open bottom sheet
                                          // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                                        },
                                        horizontalPadding: 10),
                                  ),
                                  ///Not delivered
                                  Padding(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: AppCommonWidgets.subOrderButton(
                                        buttonName: AppStrings.notDelivered,
                                        onTap: () {
                                          deliveredSuccessfullyBloc.onTapNotDelivered(selectedSuborderList:[groupedSuborderList[index]], isFromGroupHead: false );

                                          // //Mark only selected
                                          // waitingForConfirmationBloc.suborderList[index].isSelected = true;
                                          // //Open bottom sheet
                                          // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                                        },
                                        horizontalPadding: 10),
                                  ),
                                  ///Return
                                  //Make this visible if return date has not passed and return is not accepted.
                                  Visibility(
                                    visible: returnWindowsCalculateModel.isReturnDateComing && returnWindowsCalculateModel.isReturnAvailable,
                                    child: AppCommonWidgets.subOrderButton(
                                        buttonName: AppStrings.returnButton,
                                        onTap: () {
                                          deliveredSuccessfullyBloc.onTapReturnEscalate(selectedSuborderList: groupedSuborderList);

                                          // //Mark only selected
                                         groupedSuborderList[index].isSelected = true;
                                          // //Open bottom sheet
                                          // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                                        },
                                        horizontalPadding: 10),
                                  ),

                                ],
                              ),
                              verticalSizedBox(10),

                            ],
                          ),
                        ),
                        //Divider
                        Visibility(
                          visible: groupedSuborderList.length-1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );

  }

//endregion



  //region Header
  Widget header({required  List<SubOrder> headerOrderList,required String groupName}) {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
        icon: AppImages.packageIcon,
        componentName: "${AppStrings.deliveredSuccessFully} :$groupName",
        suborderList: headerOrderList,
        isEstimateDeliveryShow: false,
        isBuyerSideDeliveredOnShow: true,
        additionalWidgets: const SizedBox()

    );}

  //endregion

  //region Rate,suggest and not delivered
  Widget rateSuggestAndNotDelivered({required List<SubOrder> groupedSubOrders}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        //Rate abd suggest
        // Expanded(
        //     child: AppCommonWidgets.activeButton(
        //         buttonName: AppStrings.rateReview,
        //         onTap: () {
        //           deliveredSuccessfullyBloc.onTapRateReview(selectedSuborderList:groupedSubOrders);
        //           // shippingInProgressBloc.onTapTrack(subOrders: groupedSubOrders);
        //           // shippingInProgressBloc.onTapDeliveryStatus(groupedSubOrders: groupedSubOrders);
        //           // CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.suborderList);
        //           // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
        //         })),
        ///Todo un-comment
        // horizontalSizedBox(10),
        //Suggest
        // Expanded(
        //     child: AppCommonWidgets.activeButton(
        //         buttonName: AppStrings.suggest,
        //         onTap: () {
        //           CommonMethods.reportAndSuggestion(context: context);
        //           // shippingInProgressBloc.onTapTrack(subOrders: groupedSubOrders);
        //           // shippingInProgressBloc.onTapDeliveryStatus(groupedSubOrders: groupedSubOrders);
        //           // CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.suborderList);
        //           // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
        //         })),
        // horizontalSizedBox(10),
        //Not delivered
        // Expanded(
        //     child: AppCommonWidgets.inActiveButton(
        //         buttonName: AppStrings.notDeliveredButton,
        //         onTap: () {
        //           deliveredSuccessfullyBloc.onTapNotDelivered(selectedSuborderList:groupedSubOrders, isFromGroupHead: true);

        //           // shippingInProgressBloc.onTapCancel(subOrders: groupedSubOrders);
        //           // shippingInProgressBloc.onTapTrackingDetail(groupedSubOrders: groupedSubOrders);
        //           // CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.suborderList);
        //           // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
        //         })),

        // horizontalSizedBox(10),
        //Return
        Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: AppStrings.returnButton,
              onTap: () {
                //Select all
                CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: groupedSubOrders);

                deliveredSuccessfullyBloc.onTapReturnEscalate(selectedSuborderList: groupedSubOrders);

                // //Mark only selected
                // waitingForConfirmationBloc.suborderList[index].isSelected = true;
                // //Open bottom sheet
                // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
              },
                )),
      ],
    );
  }

//endregion


}
