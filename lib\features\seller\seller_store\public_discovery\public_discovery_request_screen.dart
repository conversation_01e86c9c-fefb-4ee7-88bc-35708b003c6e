import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class PublicDiscoveryRequestScreen extends StatefulWidget {
  final String storeReference;

  const PublicDiscoveryRequestScreen({
    Key? key,
    required this.storeReference,
  }) : super(key: key);

  @override
  _PublicDiscoveryRequestScreenState createState() =>
      _PublicDiscoveryRequestScreenState();
}

class _PublicDiscoveryRequestScreenState
    extends State<PublicDiscoveryRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final List<TextEditingController> _socialMediaControllers = [
    TextEditingController()
  ];
  final TextEditingController _communitySizeController =
      TextEditingController();
  final List<TextEditingController> _cityControllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _socialMediaControllers.add(TextEditingController());
    _cityControllers.add(TextEditingController());
    _cityControllers.add(TextEditingController());
    _cityControllers.add(TextEditingController());
  }

  @override
  void dispose() {
    for (var controller in _socialMediaControllers) {
      controller.dispose();
    }
    _communitySizeController.dispose();
    for (var controller in _cityControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _addSocialMediaField() {
    setState(() {
      _socialMediaControllers.add(TextEditingController());
    });
  }

  void _removeSocialMediaField(int index) {
    if (_socialMediaControllers.length > 1) {
      setState(() {
        _socialMediaControllers[index].dispose();
        _socialMediaControllers.removeAt(index);
      });
    }
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSubmitting = true;
      });

      try {
        // Prepare the data to be sent
        final socialLinks = _socialMediaControllers
            .map((controller) => controller.text.trim())
            .where((text) => text.isNotEmpty)
            .toList();
        final communitySize = _communitySizeController.text.trim();
        final topCities = _cityControllers
            .map((controller) => controller.text.trim())
            .where((text) => text.isNotEmpty)
            .toList();

        // Prepare the details map
        final details = {
          'social_links': socialLinks,
          'community_size': communitySize,
          'top_cities': topCities,
        };

        // Call the API
        final dashboardService = StoreDashboardService();
        await dashboardService.requestPublicDiscoveryDetailed(
          storeReference: widget.storeReference,
          detailsList: details,
        );

        if (!mounted) return;

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Public discovery request submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back
        Navigator.pop(context, true); // Pass true to indicate success
      } catch (e) {
        if (!mounted) return;

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit request: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });
        }
      }
    }
  }

  // Common input decoration for all text fields
  InputDecoration _getInputDecoration({
    required String hintText,
    bool showBorder = false,
  }) {
    return InputDecoration(
        hintText: hintText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            // color: Colors.grey.shade300,
            width: 1.0,
          ),
        ),
        // enabledBorder: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(12.0),
        //   borderSide: BorderSide(
        //     color: AppColors.textFieldFill1,
        //     width: 1.0,
        //   ),
        // ),
        // focusedBorder: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(12.0),
        //   borderSide: BorderSide(
        //     color: Theme.of(context).primaryColor,
        //     width: 1.5,
        //   ),
        // ),
        // errorBorder: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(12.0),
        //   borderSide: const BorderSide(
        //     color: Colors.red,
        //     width: 1.0,
        //   ),
        // ),
        // focusedErrorBorder: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(12.0),
        //   borderSide: const BorderSide(
        //     color: Colors.red,
        //     width: 1.5,
        //   ),
        // ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        isDense: true,
        filled: true,
        fillColor: AppColors.textFieldFill1);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Public Discovery Request'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              // Social Media Links Section
              _buildSectionHeader(
                'Social Media Profiles',
                'Drop your Instagram / YouTube / Twitter / Facebook/ Whatsapp group URL etc...',
              ),
              const SizedBox(height: 8),
              Text(
                'Note: We\'ll verify it directly. Place your Swadesic store link in the bio/description',
                style: AppTextStyle.smallTextRegular(
                    textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              ..._buildSocialMediaFields(),
              TextButton.icon(
                onPressed: _addSocialMediaField,
                icon: const Icon(Icons.add, size: 20),
                label: const Text('Add another link'),
              ),
              const SizedBox(height: 24),

              // Community Size Section
              _buildSectionHeader(
                'Community Size',
                'How big is your community today?',
                tooltip: 'Helps us prioritize your application.',
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _communitySizeController,
                decoration: _getInputDecoration(
                  hintText: 'e.g., 10,000',
                ).copyWith(
                  constraints: const BoxConstraints(maxHeight: 55),
                ),
                style: const TextStyle(fontSize: 14),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your community size';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Top Cities Section
              _buildSectionHeader(
                'Top 3 Cities',
                'What are your 3 cities having the biggest base of buyers?',
              ),
              const SizedBox(height: 8),
              ...List.generate(3, (index) => _buildCityField(index)),
              const SizedBox(height: 32),

              // What Happens Next Section
              _buildSectionHeader('What Happens Next..', ''),
              const SizedBox(height: 8),
              _buildInfoItem('We verify your account and follower strength.'),
              _buildInfoItem(
                  'If approved, your store is unlocked for Public Discovery.'),

              const SizedBox(height: 32),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitForm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.appBlack,
                    foregroundColor: AppColors.appWhite,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    _isSubmitting ? 'Submitting...' : 'Submit Application',
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.appWhite),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, String subtitle, {String? tooltip}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            if (tooltip != null) ...[
              const SizedBox(width: 8),
              Tooltip(
                message: tooltip,
                child: const Icon(Icons.info_outline,
                    size: 18, color: Colors.grey),
              ),
            ],
          ],
        ),
        if (subtitle.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
        ],
        const SizedBox(height: 8),
      ],
    );
  }

  List<Widget> _buildSocialMediaFields() {
    return List<Widget>.generate(_socialMediaControllers.length, (index) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _socialMediaControllers[index],
                decoration: _getInputDecoration(
                  hintText: 'https://youtube.com/profile',
                ).copyWith(
                  constraints: const BoxConstraints(maxHeight: 45),
                ),
                keyboardType: TextInputType.url,
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a URL';
                  }
                  final uri = Uri.tryParse(value);
                  if (uri == null || !uri.isAbsolute) {
                    return 'Please enter a valid URL';
                  }
                  return null;
                },
              ),
            ),
            if (_socialMediaControllers.length > 1)
              IconButton(
                icon: const Icon(Icons.remove_circle_outline,
                    color: Colors.red, size: 20),
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(),
                onPressed: () => _removeSocialMediaField(index),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildCityField(int index) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: TextFormField(
        controller: _cityControllers[index],
        decoration: _getInputDecoration(
          hintText: 'City ${index + 1}',
        ).copyWith(
          constraints: const BoxConstraints(maxHeight: 45),
        ),
        style: const TextStyle(fontSize: 14),
        validator: (value) {
          if (index < 2 && (value == null || value.isEmpty)) {
            return 'Please enter at least ${index + 1} city';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(width: 4),
          const Text('• '),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14, height: 1.5),
            ),
          ),
        ],
      ),
    );
  }
}
