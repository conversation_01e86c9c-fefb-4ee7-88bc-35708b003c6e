import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_availability/product_availability_bloc.dart';
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_setting_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductAvailabilityLocations extends StatefulWidget {
  final ProductAvailabilityBloc productAvailabilityBloc;
  const ProductAvailabilityLocations(
      {super.key, required this.productAvailabilityBloc});

  @override
  State<ProductAvailabilityLocations> createState() =>
      _ProductAvailabilityLocationsState();
}

class _ProductAvailabilityLocationsState
    extends State<ProductAvailabilityLocations> {
  List<String> states = [];
  List<String> cities = [];
  List<String> pinCodes = [];

  @override
  void initState() {
    widget.productAvailabilityBloc.getProductLevelDeliverySetting();
    super.initState();
  }

  void _processDeliveryLocations(String locations) {
    final parts = locations.split('|');

    for (var part in parts) {
      part = part.trim();
      if (RegExp(r'^\d+$').hasMatch(part)) {
        pinCodes.add(part);
      } else if (AppConstants.indianStates.contains(part)) {
        states.add(part);
      } else {
        cities.add(part);
      }
    }
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: AppTextStyle.access1(textColor: AppColors.appBlack),
      ),
    );
  }

  Widget _buildLocationList(List<String> items) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: items.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        thickness: 1,
        color: Color(0xFFE5E5E5),
      ),
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Text(
          items[index],
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.appWhite,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              "Available delivery locations",
              style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
            ),
          ),
          const Divider(height: 1),
          Expanded(
            child: StreamBuilder<ProductAvailabilityState>(
              stream: widget
                  .productAvailabilityBloc.productAvailabilityStateCtrl.stream,
              initialData: ProductAvailabilityState.Loading,
              builder: (context, snapshot) {
                if (snapshot.data == ProductAvailabilityState.Success) {
                  final locations = widget
                      .productAvailabilityBloc
                      .productLevelDeliverySettings
                      .deliverySettingData!
                      .deliveryLocations!;

                  // Clear previous data
                  states.clear();
                  cities.clear();
                  pinCodes.clear();

                  // Process locations
                  _processDeliveryLocations(locations);

                  return SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (states.isNotEmpty) ...[
                          _buildSectionTitle("States"),
                          _buildLocationList(states),
                        ],
                        if (cities.isNotEmpty) ...[
                          _buildSectionTitle("Cities"),
                          _buildLocationList(cities),
                        ],
                        if (pinCodes.isNotEmpty) ...[
                          _buildSectionTitle("Pincodes"),
                          _buildLocationList(pinCodes),
                        ],
                      ],
                    ),
                  );
                }

                if (snapshot.data == ProductAvailabilityState.Loading) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AppCommonWidgets.appCircularProgress(),
                        // const SizedBox(height: 12),
                        // Text(
                        //   "Checking product availability...",
                        //   style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                        // ),
                      ],
                    ),
                  );
                }

                return AppCommonWidgets.errorWidget(
                  errorMessage: AppStrings.unableToFetchAvailableLocations,
                  height: 200,
                  onTap: () {
                    widget.productAvailabilityBloc
                        .getProductLevelDeliverySetting();
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
