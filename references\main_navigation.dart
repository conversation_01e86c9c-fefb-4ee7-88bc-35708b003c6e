import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Custom Bottom Nav Demo',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0; // 0–3 = grouped tabs, 4 = profile

  final List<GlobalKey<NavigatorState>> _navigatorKeys = List.generate(
    5,
    (_) => GlobalKey<NavigatorState>(),
  );

  void _onTabSelected(int index) {
    if (index == _currentIndex) {
      // Double tap reset handled separately
      return;
    }
    setState(() => _currentIndex = index);
  }

  void _resetTab(int index) {
    _navigatorKeys[index].currentState!.popUntil((r) => r.isFirst);
  }

  Future<bool> _onWillPop() async {
    final NavigatorState currentNavigator =
        _navigatorKeys[_currentIndex].currentState!;
    if (await currentNavigator.maybePop()) {
      return false;
    }
    if (_currentIndex != 0) {
      setState(() => _currentIndex = 0);
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        body: IndexedStack(
          index: _currentIndex,
          children: [
            _buildOffstageNavigator(0, const TabPage(title: "Home")),
            _buildOffstageNavigator(1, const TabPage(title: "Search")),
            _buildOffstageNavigator(2, const TabPage(title: "Notifications")),
            _buildOffstageNavigator(3, const TabPage(title: "Add")),
            _buildOffstageNavigator(4, const TabPage(title: "Profile")),
          ],
        ),
        bottomNavigationBar: CustomBottomNav(
          currentIndex: _currentIndex,
          onTabSelected: _onTabSelected,
          onTabReset: _resetTab,
        ),
      ),
    );
  }

  Widget _buildOffstageNavigator(int index, Widget child) {
    return Offstage(
      offstage: _currentIndex != index,
      child: Navigator(
        key: _navigatorKeys[index],
        onGenerateRoute: (settings) {
          return MaterialPageRoute(builder: (_) => child);
        },
      ),
    );
  }
}

// class CustomBottomNav extends StatelessWidget {
//   final int currentIndex;
//   final Function(int) onTabSelected;
//   final Function(int) onTabReset;

//   const CustomBottomNav({
//     super.key,
//     required this.currentIndex,
//     required this.onTabSelected,
//     required this.onTabReset,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: 70,
//       decoration: const BoxDecoration(color: Colors.white, boxShadow: [
//         BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, -2))
//       ]),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceAround,
//         children: [
//           // Group of 4 tabs
//           Expanded(
//             flex: 3,
//             child: Container(
//               padding: const EdgeInsets.symmetric(horizontal: 20),
//               decoration: BoxDecoration(
//                 color: Colors.grey[600],
//                 border: Border(
//                   left: BorderSide(
//                       color: const Color.fromARGB(255, 253, 55, 55)!),
//                   right: BorderSide(color: Colors.grey[300]!),
//                 ),
//               ),
//               child: GestureDetector(
//                 onHorizontalDragEnd: (details) {
//                   if (details.primaryVelocity == null) return;
//                   if (details.primaryVelocity! > 0 && currentIndex > 0) {
//                     onTabSelected(currentIndex - 1);
//                   } else if (details.primaryVelocity! < 0 && currentIndex < 3) {
//                     onTabSelected(currentIndex + 1);
//                   }
//                 },
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                   children: List.generate(4, (i) {
//                     return GestureDetector(
//                       onTap: () => onTabSelected(i),
//                       onDoubleTap: () {
//                         onTabReset(i);
//                         ScaffoldMessenger.of(context).showSnackBar(
//                           SnackBar(content: Text("Reset ${_tabNames[i]} tab")),
//                         );
//                       },
//                       onLongPress: () {
//                         showDialog(
//                           context: context,
//                           builder: (_) => AlertDialog(
//                             title: Text("Quick menu - ${_tabNames[i]}"),
//                             content: const Text("Mock quick action menu"),
//                           ),
//                         );
//                       },
//                       onVerticalDragEnd: (_) {
//                         ScaffoldMessenger.of(context).showSnackBar(
//                           SnackBar(
//                               content:
//                                   Text("Swipe gesture on ${_tabNames[i]}")),
//                         );
//                       },
//                       child: Icon(
//                         _tabIcons[i],
//                         color: i == currentIndex ? Colors.blue : Colors.grey,
//                       ),
//                     );
//                   }),
//                 ),
//               ),
//             ),
//           ),
//           // Profile tab
//           GestureDetector(
//             onTap: () => onTabSelected(4),
//             onDoubleTap: () => onTabReset(4),
//             child: Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 20),
//               child: CircleAvatar(
//                 radius: 20,
//                 backgroundColor: currentIndex == 4 ? Colors.blue : Colors.grey,
//                 child: const Icon(Icons.person, color: Colors.white),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

class CustomBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTabSelected;
  final Function(int) onTabReset;

  const CustomBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTabSelected,
    required this.onTabReset,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, -2))
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Group of 4 inside pill background
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(40),
              ),
              child: GestureDetector(
                onHorizontalDragEnd: (details) {
                  if (details.primaryVelocity == null) return;
                  if (details.primaryVelocity! > 0 && currentIndex > 0) {
                    onTabSelected(currentIndex - 1);
                  } else if (details.primaryVelocity! < 0 && currentIndex < 3) {
                    onTabSelected(currentIndex + 1);
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(4, (i) {
                    return GestureDetector(
                      onTap: () => onTabSelected(i),
                      onDoubleTap: () {
                        onTabReset(i);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text("Reset ${_tabNames[i]} tab")),
                        );
                      },
                      onLongPress: () {
                        showDialog(
                          context: context,
                          builder: (_) => AlertDialog(
                            title: Text("Quick menu - ${_tabNames[i]}"),
                            content: const Text("Mock quick action menu"),
                          ),
                        );
                      },
                      onVerticalDragEnd: (_) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content:
                                  Text("Swipe gesture on ${_tabNames[i]}")),
                        );
                      },
                      child: Icon(
                        _tabIcons[i],
                        size: 28,
                        color: currentIndex == i ? Colors.black : Colors.grey,
                      ),
                    );
                  }),
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Profile avatar with gestures
          GestureDetector(
            onTap: () => onTabSelected(4),
            onDoubleTap: () {
              onTabReset(4);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text("Reset Profile tab")),
              );
            },
            onLongPress: () {
              showDialog(
                context: context,
                builder: (_) => const AlertDialog(
                  title: Text("Profile Menu"),
                  content: Text("Quick actions for your profile"),
                ),
              );
            },
            onVerticalDragEnd: (_) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text("Swipe gesture on Profile")),
              );
            },
            child: CircleAvatar(
              radius: 26,
              backgroundImage: const NetworkImage(
                  "https://i.pravatar.cc/150?img=47"), // replace with real image
              backgroundColor: currentIndex == 4 ? Colors.blue : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

const _tabIcons = [
  Icons.home,
  Icons.search,
  Icons.notifications,
  Icons.add,
];

const _tabNames = [
  "Home",
  "Search",
  "Notifications",
  "Add",
];

class TabPage extends StatelessWidget {
  final String title;
  const TabPage({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: Center(
        child: ElevatedButton(
          child: Text("Push detail on $title"),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => DetailPage(title: "$title Detail"),
              ),
            );
          },
        ),
      ),
    );
  }
}

class DetailPage extends StatelessWidget {
  final String title;
  const DetailPage({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: Center(child: Text("This is $title")),
    );
  }
}
