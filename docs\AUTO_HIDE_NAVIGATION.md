# Auto-Hide Navigation Implementation

This document describes the auto-hiding navigation bar functionality implemented for the Flutter mobile app.

## Overview

The auto-hide navigation system provides a modern, Instagram/Twitter-like experience where the app bar and bottom navigation bar automatically hide when scrolling down through content and reappear when scrolling up. This maximizes screen real estate for content viewing while maintaining easy access to navigation.

## Features

- **Smooth Animations**: 250ms slide animations with easing curves
- **Smart Detection**: Scroll direction and velocity-based hiding/showing
- **Edge Case Handling**: Navigation bars remain visible at top/bottom of content
- **Threshold-based**: Prevents jittery behavior on small scroll movements
- **Configurable**: Can be enabled/disabled per screen
- **Performance Optimized**: Debounced scroll events and efficient state management

## Architecture

### Core Components

1. **AutoHideNavigationController** (`lib/util/auto_hide_navigation_controller.dart`)
   - Main controller with animation management
   - Handles scroll detection and state changes
   - Provides slide animations for both app bar and bottom navigation

2. **ScrollDetectionService** (`lib/util/scroll_detection_service.dart`)
   - Advanced scroll behavior detection
   - Velocity and direction calculation
   - Position-based edge case handling

3. **AutoHideNavigationService** (`lib/util/auto_hide_navigation_service.dart`)
   - Singleton service for app-wide auto-hide management
   - Integrates with existing bottom navigation refresh streams
   - Provides mixin for easy integration

4. **AutoHideAppBar** (`lib/widgets/auto_hide_app_bar.dart`)
   - Wrapper widget for the existing mainAppBar
   - Supports all existing app bar parameters
   - Adds auto-hide functionality with animations

## Usage

### Basic Implementation

To add auto-hide functionality to a screen:

```dart
class MyScreen extends StatefulWidget {
  @override
  State<MyScreen> createState() => _MyScreenState();
}

class _MyScreenState extends State<MyScreen>
    with AutoHideNavigationMixin<MyScreen> {

  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    // Enable auto-hide navigation
    enableAutoHideNavigation();
    attachScrollControllerToAutoHide(_scrollController);
  }

  @override
  void dispose() {
    detachScrollControllerFromAutoHide(_scrollController);
    disableAutoHideNavigation();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        title: "My Screen",
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
      ),
      body: ListView.builder(
        controller: _scrollController,
        itemCount: 100,
        itemBuilder: (context, index) {
          return ListTile(title: Text("Item $index"));
        },
      ),
    );
  }
}
```

### Manual Control

You can manually control navigation bar visibility:

```dart
// Force show navigation bars
forceShowNavigationBars();

// Force hide navigation bars
forceHideNavigationBars();

// Reset to initial state
resetAutoHideNavigation();
```

### Configuration

The auto-hide behavior can be configured:

```dart
// Disable auto-hide for specific screens
disableAutoHideNavigation();

// Enable only for certain conditions
if (shouldEnableAutoHide) {
  enableAutoHideNavigation();
}
```

## Integration Status

### Implemented Screens

- ✅ **Feed Screen** (`lib/features/post/feed/feed_screen.dart`)
  - Full auto-hide integration with existing scroll controller
  - Works with both regular feed and all feed views

- ✅ **Buyer Home Screen** (`lib/features/buyers/buyer_home/buyer_home_screen.dart`)
  - Auto-hide enabled for feed tab, disabled for home tab
  - Coordinates with embedded FeedScreen component

- ✅ **Buyer Search Screen** (`lib/features/buyers/buyer_search/buyer_search_screen.dart`)
  - Auto-hide enabled for all search tabs
  - Works with tab-based navigation

- ✅ **Search Results Screen** (`lib/features/buyers/buyer_search/search_post_store_product_and_people/search_post_store_product_and_people_screen.dart`)
  - Auto-hide with scroll controller integration
  - Supports posts, stores, products, and people search results

- ✅ **Notification Screen** (`lib/features/notification/notification_screen.dart`)
  - Auto-hide enabled for notification list views
  - Works with tab-based notification categories

- ✅ **User/Store Notification Component** (`lib/features/notification/user_or_store_notification/user_or_store_notification.dart`)
  - Auto-hide with scroll controller attached to ListView
  - Smooth scrolling with auto-hide functionality

- ✅ **User Profile Screen** (`lib/features/user_profile/user_profile_screen.dart`)
  - Auto-hide enabled for non-bottom navigation instances
  - Integrates with nested scroll view

- ✅ **Buyer View Store Screen** (`lib/features/buyers/buyer_view_store/buyer_view_store_screen.dart`)
  - Auto-hide for non-bottom navigation instances
  - Works with nested scroll view and tab content

- ✅ **Buyer My Orders Screen** (`lib/features/buyers/buyer_my_orders/buyer_my_orders_screen.dart`)
  - Auto-hide with scroll controller integration
  - Smooth scrolling through order history

- ✅ **Seller All Orders Screen** (`lib/features/seller/seller_all_orders/seller_all_orders_screen.dart`)
  - Auto-hide with scroll controller and pagination support
  - Works with order filtering and search

- ✅ **Bottom Navigation** (`lib/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart`)
  - Enhanced to work with auto-hide service
  - Maintains compatibility with existing functionality

### Test Screen

- ✅ **Auto-Hide Test Screen** (`lib/features/test/auto_hide_test_screen.dart`)
  - Demonstrates all auto-hide functionality
  - Includes manual control buttons
  - Shows proper implementation patterns

## Technical Details

### Scroll Detection

- **Threshold**: 10px minimum scroll distance to trigger hide/show
- **Debounce**: 100ms delay to prevent jittery behavior
- **Velocity**: Considers scroll speed for better UX
- **Edge Cases**: Automatically shows navigation at top/bottom (50px threshold)

### Animation Configuration

- **Duration**: 250ms for smooth transitions
- **Curve**: `Curves.easeInOut` for natural feel
- **Direction**: App bar slides up/down, bottom nav slides down/up

### Performance Optimizations

- Debounced scroll events
- Efficient state management with streams
- Minimal widget rebuilds
- Memory-efficient disposal patterns

## Best Practices

### When to Use

✅ **Recommended for:**
- Content-heavy screens (feeds, lists, articles)
- Media viewing screens
- Long scrollable content
- Main navigation screens

❌ **Avoid for:**
- Forms and input screens
- Settings and configuration screens
- Modal dialogs
- Short content that doesn't scroll

### Implementation Guidelines

1. **Always dispose properly**: Detach scroll controllers and disable auto-hide in dispose()
2. **Check scroll capability**: Only enable for screens with significant scrollable content
3. **Consider user context**: Disable for screens where navigation access is critical
4. **Test thoroughly**: Verify behavior with different content lengths and scroll patterns

## Compilation Fixes Applied

### Fixed Issues

1. **PersistentTabView onTabChanged parameter**: Removed unsupported `onTabChanged` parameter from AutoHidePersistentTabView
2. **NavBarDecoration null safety**: Added null coalescing operator to provide default NavBarDecoration when null
3. **Mixin generic interface conflict**: Removed TickerProviderStateMixin implementation from AutoHideBottomNavigationMixin to avoid conflicts
4. **ScrollController null parameters**: Added proper scroll controller tracking in AutoHideNavigationService to prevent null parameter errors

### Verification

All Dart analyzer errors have been resolved and the auto-hide functionality compiles without issues.

## Troubleshooting

### Common Issues

1. **Navigation not hiding**: Check if scroll controller is properly attached
2. **Jittery behavior**: Verify scroll threshold settings and debounce timing
3. **Performance issues**: Ensure proper disposal of controllers and listeners
4. **Conflicts with existing navigation**: Check for multiple auto-hide instances

### Debug Tools

Use the test screen (`AutoHideTestScreen`) to:
- Verify auto-hide behavior
- Test manual controls
- Debug scroll detection
- Validate animation performance

## Future Enhancements

- [ ] Gesture-based manual control (swipe to show/hide)
- [ ] Customizable animation curves and durations
- [ ] Per-screen configuration persistence
- [ ] Advanced scroll prediction algorithms
- [ ] Integration with pull-to-refresh patterns

## Accessibility

The auto-hide functionality maintains accessibility by:
- Preserving screen reader navigation
- Maintaining focus management
- Providing manual control options
- Ensuring navigation remains accessible via gestures
