import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/refund_on_hold/refund_on_hold_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/contact_info/contact_info.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class RefundOnHoldScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const RefundOnHoldScreen(
      {Key? key,
      required this.suborderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<RefundOnHoldScreen> createState() => _RefundOnHoldScreenState();
}

class _RefundOnHoldScreenState extends State<RefundOnHoldScreen> {
  // region Bloc
  late RefundOnHoldBloc refundOnHoldBloc;

  // endregion
  // region Init
  @override
  void initState() {
    refundOnHoldBloc = RefundOnHoldBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.suborderList);
    refundOnHoldBloc.init();
    super.initState();
  }

  // endregion
  //region Dis update
  @override
  void didUpdateWidget(covariant RefundOnHoldScreen oldWidget) {
    refundOnHoldBloc = RefundOnHoldBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.suborderList);
    refundOnHoldBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

  //region Body
  Widget body() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: refundOnHoldBloc.groupNameList.length,
        itemBuilder: (context, index) {
          return refundOnHoldWidget(
              groupName: refundOnHoldBloc.groupNameList[index]);
        });
  }
  //endregion

  //region Refund On Hold widget
  Widget refundOnHoldWidget({required String groupName}) {
    List<SubOrder> groupedSuborderList = [];

    ///Add all suborders to the suborder list as per the display package number
    groupedSuborderList = refundOnHoldBloc.subOrderList
        .where((element) => element.displayPackageNumber == groupName)
        .toList();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          headerAlignment: ExpandablePanelHeaderAlignment.center,
          iconSize: 40,
          iconColor: AppColors.appBlack,
        ),
        //endregion

        //Header
        //region Header
        header:
            header(headerOrderList: groupedSuborderList, groupName: groupName),
        //endregion
        collapsed: needHelp(groupedSubOrders: groupedSuborderList),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            needHelp(groupedSubOrders: groupedSuborderList),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: groupedSuborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            productInfoCard(
                                context: context,
                                subOrder: groupedSuborderList[index]),

                            // Display refund hold reason
                            Visibility(
                              visible:
                                  groupedSuborderList[index].refundDetails !=
                                          null &&
                                      groupedSuborderList[index]
                                          .refundDetails!
                                          .isNotEmpty &&
                                      groupedSuborderList[index]
                                              .refundDetails!
                                              .first
                                              .refundHoldReason !=
                                          null,
                              child: Container(
                                margin: const EdgeInsets.only(top: 10),
                                child: Text(
                                  "Refund hold reason: ${groupedSuborderList[index].refundDetails?.first.refundHoldReason ?? ''}",
                                  style: AppTextStyle.contentHeading0(
                                      textColor: AppColors.appBlack),
                                ),
                              ),
                            ),

                            verticalSizedBox(10),

                            // Contact Seller button
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                AppCommonWidgets.subOrderButton(
                                    buttonName: "Contact Seller",
                                    onTap: () {
                                      // Navigate to messaging with the store
                                      // Check if static user then open login screen
                                      if (CommonMethods().isStaticUser()) {
                                        CommonMethods().goToSignUpFlow();
                                        return;
                                      }

                                      // Get the first suborder to access store contact info
                                      if (groupedSuborderList.isNotEmpty &&
                                          groupedSuborderList[index]
                                                  .storeContactInfo !=
                                              null) {
                                        // Show contact info bottom sheet
                                        CommonMethods.appMinimumBottomSheets(
                                            bottomSheetName: "Contact Seller",
                                            screen: ContactInfo(
                                                phoneNumbers:
                                                    groupedSuborderList[index]
                                                            .storeContactInfo!
                                                            .phoneNumber ??
                                                        [],
                                                email:
                                                    groupedSuborderList[index]
                                                            .storeContactInfo!
                                                            .emailId ??
                                                        []),
                                            context: context);
                                      } else {
                                        CommonMethods.toastMessage(
                                            "Seller contact information not available",
                                            context);
                                      }
                                    },
                                    horizontalPadding: 10),
                              ],
                            ),
                            verticalSizedBox(10),
                          ],
                        ),
                        //Divider
                        Visibility(
                          visible: groupedSuborderList.length - 1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header(
      {required List<SubOrder> headerOrderList, required String groupName}) {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.packageIcon,
      componentName: "Refund On Hold :$groupName",
      suborderList: headerOrderList,
      isSellerSideDelivered: false,
      additionalWidgets: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              "Refund is on hold for these returned products",
              style: AppTextStyle.contentHeading0(textColor: AppColors.red),
            ),
          ),
        ],
      ),
    );
  }
  //endregion

  //region Need help
  Widget needHelp({required List<SubOrder> groupedSubOrders}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: AppStrings.needHelp,
                onTap: () {
                  CommonMethods.reportAndSuggestion(context: context);
                })),
      ],
    );
  }
  //endregion

  //region Product Info Card
  Widget productInfoCard(
      {required BuildContext context, required SubOrder subOrder}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Product name
                  ///Brand and product name
                  RichText(
                    textScaler: MediaQuery.textScalerOf(
                        AppConstants.globalNavigator.currentContext!),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: subOrder.productBrand,
                          style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        TextSpan(
                            text: " ${subOrder.productName}",
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack,
                            )),
                      ],
                    ),
                  ),
                  verticalSizedBox(5),
                  //Price
                  Text(
                    "₹${subOrder.sellingPrice} X ${subOrder.productQuantity} = ₹${subOrder.sellingPrice! * subOrder.productQuantity!}"
                    "${subOrder.suborderFeeDetails!.productLevelDeliveryFee! == 0 ? "" : "    Delivery: ₹${subOrder.suborderFeeDetails!.productLevelDeliveryFee!}"}",
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                  // Return reason if available
                  if (subOrder.returnReason != null &&
                      subOrder.returnReason!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(
                        "Return reason: ${subOrder.returnReason}",
                        style: AppTextStyle.heading3Regular(
                          textColor: AppColors.orange,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
            horizontalSizedBox(20),
            //Product image
            ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                child: Container(
                    color: AppColors.textFieldFill1,
                    height: 60,
                    width: 60,
                    child: extendedImage(
                      "${subOrder.productImage}",
                      context,
                      100,
                      100,
                      customPlaceHolder: AppImages.productPlaceHolder,
                      cache: true,
                    )))
          ],
        ),
      ),
    );
  }
  //endregion

  //region Divider
  Widget divider() {
    return Divider(
      color: AppColors.lightGray,
      height: 1,
      thickness: 1,
    );
  }
  //endregion
}
