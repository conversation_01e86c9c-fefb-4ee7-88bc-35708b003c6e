import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_store/public_discovery/public_discovery_request_screen.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class UnlockDiscoveryCard {
  static Widget buildTitle() {
    return Text(
      "Unlock Public Discovery",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }

  static Widget buildContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            "Get visibility & orders beyond your network via feed, search, recommendations etc.",
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Container(
          padding: const EdgeInsets.all(5),
          child: Icon(Icons.arrow_forward, color: AppColors.appBlack),
        ),
      ],
    );
  }

  static void showBottomSheet(BuildContext context, String storeReference,
      StoreDashboardService dashboardService) {
    CommonMethods.appBottomSheet(
      bottomSheetName: "Public Discovery",
      screen: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main Heading
              Text(
                "Unlock Public Discovery",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),

              // One-sentence benefit statement
              Text(
                "Get visibility beyond your network – appear in Swadesic's feed, search, and recommendations to reach new buyers nationwide.",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 16),

              // Why not in public discovery yet
              Text(
                "Why Your Store Isn't in Public Discovery Yet",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "Swadesic keeps Public Discovery clean and valuable for both buyers and sellers.",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "Only stores that show credibility and real activity are listed publicly. This way we avoid low-quality stores or random fake stores on the community so buyers always see active, trusted stores, and sellers get higher-quality visibility.",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "Your store is in Private Discovery mode as you just started out and yet to build credibility on Swadesic.",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "A store in private mode is only visible to people who have a store/product link.",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 16),

              // How to unlock heading
              Text(
                "How to Unlock Public Discovery",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),

              // Numbered list 1-4
              _buildNumberedItem(
                  1, "Complete your store with quality products and content."),
              _buildNumberedItem(2,
                  "Share your store link with existing customers in your network. Invite them to support your store."),
              _buildNumberedItem(3,
                  "Receive orders and engagement from your network – let them like your product, comments, posts etc."),
              _buildNumberedItem(
                  4, "Engage Customers – request reviews, build credibility."),
              const SizedBox(height: 16),

              // Requirements heading
              Text(
                "Requirements",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),

              // Bullet list for requirements
              _buildBulletedItem("20+ supporters"),
              _buildBulletedItem("5 orders"),
              _buildBulletedItem("10 likes"),
              _buildBulletedItem("3 reviews (external or internal reviews)"),

              const SizedBox(height: 24),

              // Early unlock section
              Text(
                "Already Have a Big Audience?",
                style:
                    AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "If you already have a large audience outside Swadesic, you can request an early unlock:",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              _buildNumberedItem(
                  1, "Have 10,000+ followers on any social media platform"),
              _buildNumberedItem(
                  2, "Add your Swadesic StoreLink to your social media bio"),
              _buildNumberedItem(3, "Submit your application for review"),
              const SizedBox(height: 16),

              // CTA button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.brandBlack,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PublicDiscoveryRequestScreen(
                          storeReference: storeReference,
                        ),
                      ),
                    );
                  },
                  child: Text(
                    "Request for Public Discovery →",
                    style: AppTextStyle.access1(textColor: AppColors.appWhite),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      context: context,
    );
  }

  // Helper widget for numbered list items within bottom sheet
  static Widget _buildNumberedItem(int number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "$number.",
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  // Helper widget for bulleted list items within bottom sheet
  static Widget _buildBulletedItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Text("•", style: TextStyle(fontSize: 16, height: 1.4)),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }
}
