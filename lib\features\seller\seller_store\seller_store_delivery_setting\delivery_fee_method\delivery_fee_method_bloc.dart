import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivery_setting_bloc.dart';



class DeliveryFeeMethodBloc {
  // region Common Variables
  BuildContext context;
  final SellerStoreDeliverySettingBloc sellerStoreDeliverySettingBloc;
  // endregion

  //region Controller
  //endregion

  //region Text Editing Controller
final TextEditingController feeTextCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  DeliveryFeeMethodBloc(this.context, this.sellerStoreDeliverySettingBloc);
  // endregion

  // region Init
  void init() async{

    //If delivery fee value is null or 0
    if(SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeeValue == null ||
        SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeeValue == 0
    ){
      feeTextCtrl.text = "";
    }
    //Else put the original value
    else{
      feeTextCtrl.text = SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeeValue.toString();
    }
    //On change settings
    sellerStoreDeliverySettingBloc.onChangeSetting();
  }

// endregion


//region On select standard fee
void onSelectStandardFee(){
  //Standard to true
  SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeStandard = true;
  //Free for all to false
  SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeAllFree = false;

  //Delivery fee type to per order
  SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeeValuetype = "per order";
  //print("Standart fee ${SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeStandard }");
  //print("Free for all fee ${SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeAllFree }");

  //On change settings
  sellerStoreDeliverySettingBloc.onChangeSetting();


  ///Refresh
      sellerStoreDeliverySettingBloc.deliverySettingCtrl.sink.add(DeliverySettingState.Success);
  }
//endregion


//region On select free delivery
  void onSelectFreeDelivery(){
    // Store the original delivery fee value from store-level settings if not already set
    String? originalDeliveryFeeValue = SellerStoreDeliverySettingBloc.storeLevelDeliverySettingResponse
        .deliverySettingData?.deliveryfeeValue;

    //Standard to false
    SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeStandard = false;
    //Free for all to true
    SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeAllFree = true;
    
    // Retain the original delivery fee value instead of setting to "0"
    SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeeValue = 
        originalDeliveryFeeValue ?? "0";

    //Mark delivery fee type null
    SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeeValuetype = "";

    //print("Standart fee ${SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeStandard }");
    //print("Free for all fee ${SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeAllFree }");
    //print("Retained delivery fee value: ${SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeeValue}");

    //On change settings
    sellerStoreDeliverySettingBloc.onChangeSetting();

    ///Refresh
    sellerStoreDeliverySettingBloc.deliverySettingCtrl.sink.add(DeliverySettingState.Success);
  }
//endregion



//region On select standard fee type
  void onSelectStandardFeeType({required String value}){
    SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliveryfeeValuetype = value;
    //On change settings
    sellerStoreDeliverySettingBloc.onChangeSetting();
    ///Refresh
    sellerStoreDeliverySettingBloc.deliverySettingCtrl.sink.add(DeliverySettingState.Success);
  }
//endregion



//region Discard
  void dispose() {
    //feeTextCtrl.dispose();
    onSelectFreeDelivery();
  }
//endregion

}
