import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:swadesic/features/common_buyer_seller_screen/no_internet/no_internet_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/no_internet/poem.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class NoInternet extends StatefulWidget {
  const NoInternet({Key? key}) : super(key: key);

  @override
  State<NoInternet> createState() => _NoInternetState();
}

class _NoInternetState extends State<NoInternet> {


  //region Build
  late NoInternetBloc noInternetBloc;
  //endregion
  //region Init
  @override
  void initState() {
    noInternetBloc = NoInternetBloc(context);
    super.initState();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return  WillPopScope(
      onWillPop:noInternetBloc.onTapBackButton,
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        body: SafeArea(child: body()),

      ),
    );
  }
  //endregion

  //region Body
Widget body(){
    return StreamBuilder<bool>(
      stream: noInternetBloc.refreshNoInternetCtrl.stream,
      builder: (context, snapshot) {
        return SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              verticalSizedBox(40),
              noInternet(),
              verticalSizedBox(80),

              weAreStillConnected(),
              appIcon(),
              poem(),
              options(),



            ],
          ),
        );
      }
    );
}
//endregion

  //region No internet
  Widget noInternet(){
    return Container(
      margin: const EdgeInsets.only(left: 40,right: 40),
        height: 270,
        // width: 300,
        child: LottieBuilder.asset(AppImages.noInternetAnimation,height: 300,width: 300,));
  }
  //endregion

//region We are still connected by emotion
Widget weAreStillConnected(){
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 50),
          child: Text(AppStrings.weAreStillConnected,
            textAlign: TextAlign.center,
            style: AppTextStyle.heading1Bold(textColor: AppColors.writingColor2),),
        )
      ],
    );
}
//endregion

//region App icon
Widget appIcon(){
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 20),
        child: Image.asset(AppImages.appIcon,height: 30,width: 30,));
}
//endregion


//region Poem
Widget poem(){
  return StreamBuilder<PoemState>(
    stream: noInternetBloc.poemRefreshCtrl.stream,
    builder: (context, snapshot) {
      if(snapshot.data == PoemState.Loading){
        return const SizedBox();
      }
      return Poem(noInternetBloc: noInternetBloc,);
    }
  );
}
//endregion


//region Options
Widget options(){
  return StreamBuilder<bool>(
      stream: noInternetBloc.refreshNoInternetCtrl.stream,
    builder: (context, snapshot) {
      return Visibility(
        visible: noInternetBloc.isOptionVisible,
        child: Row(
          children: [
            CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: (){
                  noInternetBloc.isActiveHeart = true;
                  noInternetBloc.refreshNoInternetCtrl.sink.add(true);
                },
                child: SvgPicture.asset(noInternetBloc.isActiveHeart?AppImages.heartActive:AppImages.heart,height: 25,width: 25,)),

            CupertinoButton(

                padding: EdgeInsets.zero,
                onPressed: (){
                  CommonMethods.share("https://swadesic.com");
                },
                child: SvgPicture.asset(AppImages.share,height: 25,width: 25,)),
            CupertinoButton(

                padding: EdgeInsets.zero,
                onPressed: (){
                  noInternetBloc.onTapNext();
                },
                child: SvgPicture.asset(AppImages.next,height: 25,width: 25,)),

          ],
        ),
      );
    }
  );
}
//endregion


}
