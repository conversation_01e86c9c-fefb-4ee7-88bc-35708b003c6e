import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_valuation_card_overlay.dart';
import 'package:swadesic/features/widgets/level_badge/level_badge.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class StoreValuationCard {
  static Widget buildTitle(StoreDashBoard storeDashboard) {
    return Row(
      children: [
        Text(
          "Store valuation @ ",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        Text(
          "₹${(storeDashboard.storeValuation ?? 0).toStringAsFixed(0)}",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        // const SizedBox(width: 8),
        // Text(
        //   "₹200 ▲",
        //   style: AppTextStyle.access1(textColor: Colors.red),
        // ),
      ],
    );
  }

  static Widget buildContent(
      StoreDashBoard storeDashboard, BuildContext context) {
    return Consumer<SellerOwnStoreInfoDataModel>(
      builder: (context, storeInfoModel, child) {
        final storeValuation = storeInfoModel.storeInfo?.storeValuation ??
            storeDashboard.storeValuation ??
            1200000;

        final storeLevel = int.tryParse(
                storeInfoModel.storeInfo?.storeLevel?.toString() ??
                    storeDashboard.storeLevel ??
                    '1') ??
            1;

        // Level ranges mapping (dummy data)
        final Map<int, Map<String, double>> levelRanges = {
          1: {'min': 0, 'max': 100000},
          2: {'min': 100000, 'max': 500000},
          3: {'min': 500000, 'max': 1000000},
          4: {'min': 1000000, 'max': 2000000},
          5: {'min': 2000000, 'max': 5000000},
          6: {'min': 5000000, 'max': 10000000},
          7: {'min': 10000000, 'max': 20000000},
          8: {'min': 20000000, 'max': 50000000},
          9: {'min': 50000000, 'max': 50000000}, // Max level
        };

        double progress = 0.0;

        if (storeLevel < 9) {
          final currentRange = levelRanges[storeLevel]!;
          final nextRange = levelRanges[storeLevel + 1]!;
          final currentMin = currentRange['min']!;
          final nextMin = nextRange['min']!;

          progress = (storeValuation - currentMin) / (nextMin - currentMin);
          progress = progress.clamp(0.0, 1.0);
        } else {
          progress = 1.0;
        }

        return Column(
          children: [
            // Progress bar
            Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.appWhite,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: progress,
                  child: Container(
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppColors.brandBlack,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
                // Circular indicator at the end of progress
                Positioned(
                  left: (MediaQuery.of(context).size.width - 85) * progress,
                  top: 0.5,
                  child: Container(
                    width: 11,
                    height: 11,
                    decoration: BoxDecoration(
                      color: AppColors.appWhite,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.brandBlack,
                        width: 2,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateFormat('MMMM d, yyyy').format(DateTime.now()),
                  style: AppTextStyle.smallText(textColor: AppColors.appBlack),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      storeLevel < 9
                          ? "₹${(levelRanges[storeLevel + 1]!['min']!).toStringAsFixed(0)} to "
                          : "Max Level",
                      style: AppTextStyle.smallText2(
                          textColor: AppColors.writingBlack0),
                    ),
                    LevelBadge.createLevelBadge(
                      level: (storeLevel + 1).toString(),
                      badgeType: LevelBadgeType.store,
                      width: 28,
                      height: 28,
                    ),
                  ],
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  static void showOverlay(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return const StoreValuationCardOverlay();
      },
    );
  }
}
