import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class CommonReferralHowSwadesic extends StatefulWidget {
  final CommonReferralPageBloc commonReferralPageBloc;
  const CommonReferralHowSwadesic(
      {super.key, required this.commonReferralPageBloc});

  @override
  State<CommonReferralHowSwadesic> createState() =>
      _CommonReferralHowSwadesicState();
}

class _CommonReferralHowSwadesicState extends State<CommonReferralHowSwadesic> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  Widget bulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 8, right: 8),
            child: Container(
              width: 4,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.appBlack,
                shape: BoxShape.circle,
              ),
            ),
          ),
          Expanded(
            child: Text(
              text,
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  Widget inviteFriendsButton() {
    return StreamBuilder<CommonReferralPageState>(
      stream: widget.commonReferralPageBloc.commonReferralPageStateCtrl.stream,
      initialData: CommonReferralPageState.Loading,
      builder: (context, snapshot) {
        if (widget.commonReferralPageBloc.inviteCode.isNotEmpty) {
          return CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              if(CommonMethods().isStaticUser()){
                CommonMethods().goToSignUpFlow();
                return ;
              }
              CommonMethods.share(
                  "${AppConstants.appData.isUserView! ? AppStrings.userInviteeMessage : AppStrings.storeInviteeMessage}${AppConstants.domainName}?ref=${widget.commonReferralPageBloc.inviteCode}");
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              margin: const EdgeInsets.only(left: 10, right: 10),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: AppColors.brandBlack,
                borderRadius: BorderRadius.circular(
                    MediaQuery.of(context).size.width * 0.5),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(AppImages.basketWithStar, height: 40, width: 40),
                  const SizedBox(width: 10),
                  Text(
                    "Invite Friends & Stores",
                    style: AppTextStyle.access1(textColor: AppColors.appWhite),
                  ),
                ],
              ),
            ),
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget body() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 15),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: AppColors.brandBlack,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              "How else Swadesic benefits you?",
              textAlign: TextAlign.center,
              style: AppTextStyle.introSlideTitle(textColor: AppColors.appBlack)
                  .copyWith(
                      fontSize: 20, fontFamily: AppConstants.leagueSemiBold),
            ),
          ),
          const SizedBox(height: 15),
          Text("The Future of Indian Commerce is Here",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack)),
          const SizedBox(height: 15),
          Text(
            "For Buyers:",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
                .copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 10),
          bulletPoint(
              "Discover Authenticity: Find trending and awesome Swadeshi products—real Indian quality."),
          bulletPoint(
              "Direct Engagement: Connect directly with sellers and fellow buyers."),
          bulletPoint(
              "Seamless Shopping: Enjoy smooth order tracking and in-app communication."),
          RichText(
            text: TextSpan(
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
              children: [
                TextSpan(
                    text:
                        "Empower Yourself: Explore how you can join the revolution as a seller."),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Text(
            "For Stores:",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
                .copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 10),
          bulletPoint(
              "Unified Platform: Simplify branding and e-commerce with easy inventory management."),
          bulletPoint(
              "Build Loyalty: Create a devoted customer community that powers your growth."),
          bulletPoint(
              "Streamlined Operations: Experience efficient order processing and timely payouts."),
          bulletPoint(
              "Multiply Impact: Invite friends and fellow stores to strengthen the Swadeshi revolution."),
          const SizedBox(height: 15),
          RichText(
            text: TextSpan(
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
              children: [
                TextSpan(
                    text:
                        "Join Swadesic—where every transaction builds a self-reliant Bharat."),
              ],
            ),
          ),
          const SizedBox(height: 20),
          inviteFriendsButton(),
        ],
      ),
    );
  }
}
