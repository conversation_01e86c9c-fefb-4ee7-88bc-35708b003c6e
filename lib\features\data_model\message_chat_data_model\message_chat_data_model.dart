import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/messaging_response/dm_meta_data_response.dart';
import 'package:swadesic/model/messaging_response/messaging_detail.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:collection/collection.dart';
import 'package:swadesic/services/messaging_services/messaging_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';

class MessageChatDataModel with ChangeNotifier {
  List<ChatDetail> chatDetailList = [];


  //region Add chat in chat list
  void addChatInList({required List<ChatDetail> dataList}){
    for (var newChat in dataList) {
      // Remove existing chat with same messageId if it exists
      chatDetailList.removeWhere((chat) => chat.messageId == newChat.messageId);
      //Remove duplicates as per the message id
      chatDetailList.removeWhere((chat) => chat.messageId == newChat.messageId);
      //Short this as new message will be added at the bottom
      // chatDetailList.sort((a, b) => b.timestamp!.compareTo(a.timestamp!));

      // Add the new chat
      chatDetailList.add(newChat);

    }
    // Notify listeners about the change
    notifyListeners();
  }
  //endregion



  //region Get initial message detail
  Future<MessageDetailResponse> getInitialMessageDetail({required StreamController streamController, required String toEntityReference}) async {
  try {
    String fromEntityReference = AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!;

    ///Get meta data
    DmMetaData dmMetaData = await MessagingService().getDmMetaData(
        toEntityReference: toEntityReference,
        fromEntityReference: fromEntityReference
    );

    ///2. Initial Api call
    MessageDetailResponse messageDetailResponse = await MessagingService().getMessageDetail(
        body: {
          "from_entity_reference": fromEntityReference,
          "to_entity_reference": toEntityReference,
          "anchor_time": DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(DateTime.now().toUtc()).toString(),
          "direction": "PAST",
          "limit": 20,
        }
    );

    //Add dm meta data in messageDetailResponse
    messageDetailResponse.dmMetaData = dmMetaData;

    //If no message are there then Empty
    if(messageDetailResponse.data!.messages!.isEmpty){
       streamController.sink.add(AppStateEnum.Empty);
       return messageDetailResponse;
    }
    //Add chat in list
    addChatInList(dataList: messageDetailResponse.data!.messages!);

    //Success
    streamController.sink.add(AppStateEnum.Success);

    //Return the list of chat
    return messageDetailResponse;

  } on ApiErrorResponseMessage catch (error) {
    //Failed
    streamController.sink.add(AppStateEnum.Failed);
    return MessageDetailResponse();
  } catch (error) {
    //Failed
    streamController.sink.add(AppStateEnum.Failed);
    return MessageDetailResponse();
  }
}
//endregion


  //region Get pagination message detail
  Future<List<ChatDetail>> getPaginationMessageDetail({required StreamController streamController,required String anchorTime,  required String toEntityReference,required int limit}) async {
    try {

      String fromEntityReference = AppConstants.appData.isUserView!?AppConstants.appData.userReference!:AppConstants.appData.storeReference!;


      //Loading
      streamController.sink.add(AppStateEnum.Loading);

      MessageDetailResponse  messageDetailResponse= await MessagingService().getMessageDetail(
          body: {
            "from_entity_reference":fromEntityReference,
            "to_entity_reference": toEntityReference,
            "anchor_time":DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(DateTime.parse(anchorTime).toUtc()).toString(),
            "direction": "PAST",
            "limit": limit,
          }
      );
//DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(DateTime.parse(anchorTime).toUtc())

      //If no message are there then Empty
      if(messageDetailResponse.data!.messages!.isEmpty){
        streamController.sink.add(AppStateEnum.Empty);
        return [];
      }
      //Add chat in list
      addChatInList(dataList: messageDetailResponse.data!.messages!);

      //Success
      streamController.sink.add(AppStateEnum.Success);

      //Return the list of chat
      return messageDetailResponse.data!.messages!;

    } on ApiErrorResponseMessage catch (error) {
      //Failed
      streamController.sink.add(AppStateEnum.Failed);
      return [];
    } catch (error) {
      //Failed
      streamController.sink.add(AppStateEnum.Failed);
      return [];
    }
  }

  //endregion




//region Update Message delivered status
void updateMessageDeliveredStatus({required ChatDetail chatDetail}){

  ChatDetail detail = chatDetailList.firstWhere((element) => element.messageId == chatDetail.messageId);

  //Update the message
  detail.messageId = chatDetail.messageId;
  detail.isDeliveredToServer = true;



  // Notify listeners about the change
  notifyListeners();
}
//endregion










}
