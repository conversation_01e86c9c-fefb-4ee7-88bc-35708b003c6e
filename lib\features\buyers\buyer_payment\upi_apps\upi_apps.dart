import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_payment/upi_apps/upi_apps_bloc.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:upi_india/upi_india.dart';

class UpiApps extends StatefulWidget {
  final GetCartPriceResponse getCartPriceResponse;
  final String orderNumber;
  const UpiApps({super.key, required this.getCartPriceResponse, required this.orderNumber});

  @override
  State<UpiApps> createState() => _UpiAppsState();
}

class _UpiAppsState extends State<UpiApps> {

  //region Bloc
  late UpiAppBloc upiAppBloc;
  //endregion

  //region Init
  @override
  void initState() {
    upiAppBloc = UpiAppBloc(context,widget.getCartPriceResponse,widget.orderNumber);
    upiAppBloc.init();
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region Body
Widget body(){
    return StreamBuilder<List<UpiApp>>(
      stream: upiAppBloc.upiAppCtrl.stream,
      initialData: [],
      builder: (context, snapshot) {
        //If empty
        if(snapshot.data!.isNotEmpty){
          return GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3, // Set the number of columns here
              mainAxisSpacing: 10.0,
              crossAxisSpacing: 10.0,
              childAspectRatio: 1.0, // Adjust the aspect ratio as needed
            ),
            itemCount: snapshot.data!.length, // Set the number of items here
            itemBuilder: (context, index) {
              return InkWell(
                onTap: () {
                  upiAppBloc.upiInitiateIntent(app: snapshot.data![index]);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  decoration: BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(20))),
                  child: Center(
                    child: Image.memory(snapshot.data![index].icon,height: 60,width: 60,),
                  ),
                ),
              );
            },
          );

        }
        //Not empty
        return Text ("No data");

      }
    );
}
//endregion

}
