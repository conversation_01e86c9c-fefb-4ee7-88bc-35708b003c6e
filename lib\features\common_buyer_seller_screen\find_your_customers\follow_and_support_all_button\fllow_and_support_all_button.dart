import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customer_card/find_your_customer_card_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/follow_and_support_all_button/follow_and_support_all_button_bloc.dart';
import 'package:swadesic/model/find_your_customers_response/get_contact_user_and_store_info_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class FollowAndSupportAllButton extends StatefulWidget {
  final FindYourCustomersBloc findYourCustomersBloc;
  final String reference;

  const FollowAndSupportAllButton(
      {super.key,
      required this.findYourCustomersBloc,
      required this.reference});

  @override
  State<FollowAndSupportAllButton> createState() =>
      _FollowAndSupportAllButtonState();
}

class _FollowAndSupportAllButtonState extends State<FollowAndSupportAllButton> {
  //Bloc
  late FollowAndSupportAllButtonBloc followAndSupportAllButtonBloc;

  //region Init
  @override
  void initState() {
    super.initState();
    followAndSupportAllButtonBloc = FollowAndSupportAllButtonBloc(
        context, widget.findYourCustomersBloc, widget.reference);
    followAndSupportAllButtonBloc.init();
  }

  //endregion

  // //region Did update widget
  // @override
  // void didUpdateWidget(covariant FollowAndSupportAllButton oldWidget) {
  //   followAndSupportAllButtonBloc = FollowAndSupportAllButtonBloc(context, widget.findYourCustomersBloc, widget.reference);
  //   followAndSupportAllButtonBloc.init();
  //   super.didUpdateWidget(oldWidget);
  // }
  // //endregion

  //region Dispose
  @override
  void dispose() {
    followAndSupportAllButtonBloc.dispose();
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return userCard();
  }

//endregion

//region User card
  Widget userCard() {
    return StreamBuilder<FollowAndSupportAllButtonState>(
        stream: followAndSupportAllButtonBloc
            .followAndSupportAllButtonStateCtrl.stream,
        builder: (context, snapshot) {
          return LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
              ///Image radius
              return Container(
                  margin: const EdgeInsets.only(top: 10),
                  child: followAndSupportAllButton());
            },
          );
        });
  }

//endregion

//region Follow and support all buttons
  Widget followAndSupportAllButton() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return Row(
          children: [
            ///Undo
            Visibility(
                visible: followAndSupportAllButtonBloc.isUndoVisible,
                child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          followAndSupportAllButtonBloc.onTapButton(
                              followEnum: FollowEnum.UNFOLLOW);
                        },
                        child: Text(
                          "Undo",
                          style: AppTextStyle.access1(
                              textColor: AppColors.brandBlack),
                        )))),

            ///Buttons
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: Container(
                  margin: EdgeInsets.symmetric(
                      horizontal: constraints.maxWidth * 0.05),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Stack(
                        children: [
                          CupertinoButton(
                              borderRadius: BorderRadius.circular(100),
                              color: AppColors.brandBlack,
                              child: Container(
                                alignment: Alignment.center,
                                width: double.infinity,
                                child: Text(
                                  "Follow and Support all",
                                  style: AppTextStyle.access1(
                                      textColor: AppColors.appWhite),
                                ),
                              ),
                              onPressed: () {
                                //Entity type
                                followAndSupportAllButtonBloc.entityType =
                                    EntityType.BOTH;
                                followAndSupportAllButtonBloc.onTapButton(
                                    followEnum: FollowEnum.FOLLOW);
                              }),
                          StreamBuilder<bool>(
                              stream: followAndSupportAllButtonBloc
                                  .followAndSupportVisibleCtrl.stream,
                              initialData: false,
                              builder: (context, snapshot) {
                                return Positioned(
                                    bottom: 0,
                                    right: 10,
                                    top: 0,
                                    child: CupertinoButton(
                                        onPressed: () {
                                          followAndSupportAllButtonBloc
                                              .onTapArrow(
                                                  value: snapshot.data!);
                                        },
                                        child: SizedBox(
                                            height: 20,
                                            child: RotatedBox(
                                              quarterTurns:
                                                  snapshot.data! ? 2 : 0,
                                              child: SvgPicture.asset(
                                                  color: AppColors.appWhite,
                                                  fit: BoxFit.cover,
                                                  AppImages.arrow),
                                            ))));
                              })
                        ],
                      ),
                      followAndSupport(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
//endregion

//region Follow and support

  Widget followAndSupport() {
    return StreamBuilder<bool>(
        stream:
            followAndSupportAllButtonBloc.followAndSupportVisibleCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          return LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
              return Visibility(
                visible: snapshot.data!,
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 10),
                  child: Column(
                    children: [
                      CupertinoButton(
                          borderRadius: BorderRadius.circular(100),
                          color: AppColors.textFieldFill1,
                          onPressed: snapshot.data!
                              ? () {
                                  //Entity type
                                  followAndSupportAllButtonBloc.entityType =
                                      EntityType.USER;
                                  //Hide the buttons and make undo visible
                                  followAndSupportAllButtonBloc
                                      .followAndSupportVisibleCtrl.sink
                                      .add(false);
                                  followAndSupportAllButtonBloc.onTapButton(
                                      followEnum: FollowEnum.FOLLOW);
                                }
                              : null,
                          child: Container(
                            alignment: Alignment.center,
                            width: double.infinity,
                            child: Text(
                              "Follow contacts only",
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyle.access1(
                                  textColor: AppColors.writingBlack0),
                            ),
                          )),
                      const SizedBox(
                        height: 10,
                      ),
                      CupertinoButton(
                          borderRadius: BorderRadius.circular(100),
                          color: AppColors.textFieldFill1,
                          child: Container(
                            alignment: Alignment.center,
                            width: double.infinity,
                            child: Text(
                              "Support stores only",
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyle.access1(
                                  textColor: AppColors.writingBlack0),
                            ),
                          ),
                          onPressed: () {
                            //Entity type
                            followAndSupportAllButtonBloc.entityType =
                                EntityType.STORE;
                            //Hide the buttons and make undo visible
                            followAndSupportAllButtonBloc
                                .followAndSupportVisibleCtrl.sink
                                .add(false);
                            followAndSupportAllButtonBloc.onTapButton(
                                followEnum: FollowEnum.FOLLOW);
                          }),
                    ],
                  ),
                ),
              );
            },
          );
        });
  }
//endregion
}
