import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signature/signature.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/id_verification/pan/signature/signature_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'dart:io';

class MySignatureFullScreen extends StatefulWidget {
  final SignatureBloc signatureBloc;
  const MySignatureFullScreen({super.key, required this.signatureBloc});

  @override
  State<MySignatureFullScreen> createState() => _MySignatureFullScreenState();
}

class _MySignatureFullScreenState extends State<MySignatureFullScreen> {


  //region Init
  @override
  void initState() {
    // Lock orientation to landscape
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
    ]);
    super.initState();
  }
  //endregion


  //region Dispose
  @override
  void dispose() {
    // Reset orientation to portrait when leaving this screen
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {




    return Scaffold(
      backgroundColor:AppColors.textFieldFill2,
      appBar:appBar(),
      body: signatureAndGalleryImage()

    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: "Signature",
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

//endregion




  //region Signature  and gallery image
  Widget signatureAndGalleryImage(){
    return Stack(
      children: [
        StreamBuilder<File?>(
            stream: widget.signatureBloc.fileFromGalleryOrSignatureToImageCtrl.stream,
            initialData: widget.signatureBloc.fileFromGalleryOrSignatureToImage,
            builder: (context, snapshot) {
              //If signature controller is not cleared then show signature
              if(widget.signatureBloc.signatureController.isNotEmpty || snapshot.data == null) {
                return signature();
              }
              return Image.file(
                width: MediaQuery.of(context).size.width,
                snapshot.data!,fit: BoxFit.contain,
              );
            }
        ),
        Positioned(
            top: 0,right: 0,
            child: clearAndAddFromGallery())
      ],
    );
  }
  //endregion



  //region Signature
  Widget signature(){
    return Signature(
      controller: widget.signatureBloc.signatureController,
      width: double.infinity, height: MediaQuery.of(context).size.height,
      backgroundColor:AppColors.textFieldFill2,
    );
  }
//endregion


//region Clear and add from gallery
  Widget clearAndAddFromGallery() {
    return Row(
      children: [
        CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: (){
              widget.signatureBloc.onTapClear();
            },
            child: Icon(Icons.cleaning_services,color: AppColors.appBlack,size: 20,)),
        CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: (){
              widget.signatureBloc.selectSignatureFromGallery();
            },
            child: Icon(Icons.image,color: AppColors.appBlack,size: 20,)),

        CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: (){
              Navigator.pop(context,true);
              // signatureBloc.goToFullScreen(data:signatureAndGalleryImage() );
            },
            child: Icon(Icons.done,color: AppColors.appBlack,size: 20,)),


      ],
    );
  }
//endregion


}
