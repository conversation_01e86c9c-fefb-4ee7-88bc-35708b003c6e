import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_requested/return_requested_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class ConfirmAndCancelAllOrder extends StatefulWidget {
  final List<SubOrder> suborderList;
  final ReturnRequestedBloc returnRequestedBloc;
  final SellerSubOrderBloc sellerSubOrderBloc;


  const ConfirmAndCancelAllOrder({Key? key, required this.suborderList, required this.returnRequestedBloc, required this.sellerSubOrderBloc})
      : super(key: key);

  @override
  State<ConfirmAndCancelAllOrder> createState() => _ConfirmAndCancelAllOrderState();
}


class _ConfirmAndCancelAllOrderState extends State<ConfirmAndCancelAllOrder> {
  //region Init
  @override
  void initState() {
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {

    super.dispose();
  }
  //endregion



  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: StreamBuilder<bool>(
          stream: widget.returnRequestedBloc.bottomSheetCtrl.stream,
          builder: (context, snapshot) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                selectUnselect(),
                verticalSizedBox(20),
                estimatedReturnPickupDate(),
                verticalSizedBox(20),

                ///Accept return
                acceptReturnButton(),
                AppCommonWidgets.bottomListSpace(context: context),
              ],
            );
          }
      ),
    );
  }

  //region Select unselect
  Widget selectUnselect() {
    return Column(
      children: [
        ///Sub title
        Container(
            margin: const EdgeInsets.all(10),
            child: SellerAllOrdersCommonWidgets.sellerBottomSheetSubTitle(title: AppStrings.ifYouWantToUpdate)),

        ///Drop down
        InkWell(
          onTap: () {
            widget.returnRequestedBloc.selectUnSelectDropdown();
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 6.5),
            decoration: BoxDecoration(
                color: AppColors.lightestGrey2,
                borderRadius: BorderRadius.circular(7)
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///Drop down
                Container(
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      //Grand total
                      Text(AppStrings.selectUnSelect, style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),),
                      Expanded(child: horizontalSizedBox(10)),
                      widget.returnRequestedBloc.isSelectUnselectVisible
                          ? RotatedBox(
                        quarterTurns: 3,
                        child: RotatedBox(
                            quarterTurns: 4,
                            child: SvgPicture.asset(AppImages.arrow3)),
                      )
                          : RotatedBox(
                          quarterTurns: 1,
                          child: SvgPicture.asset(AppImages.arrow3))
                    ],
                  ),
                ),

                ///List of product
                Visibility(
                    visible: widget.returnRequestedBloc.isSelectUnselectVisible,
                    child: subOrderList()),

                ///Selected product count
                Visibility(
                  visible: !widget.returnRequestedBloc.isSelectUnselectVisible,
                  child: Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      child: Text("${CommonMethods.returnHowManySubOrdersSelected(subOrderList: widget.suborderList) == 0 ? "No" : CommonMethods
                          .returnHowManySubOrdersSelected(subOrderList: widget.suborderList)} ${CommonMethods.returnHowManySubOrdersSelected(
                          subOrderList: widget.suborderList) == 1 ? "suborder" : "suborders"} selected",
                        style: AppTextStyle.settingText(textColor: AppColors.appBlack),)),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
  //endregion

  //region Sub orders list
  Widget subOrderList() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.suborderList.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppCommonWidgets.subOrderInfo(subOrder: widget.suborderList[index],
                  onTap: () {
                    widget.returnRequestedBloc.onSelectSubOrder(widget.suborderList[index]);
                  }, context: context),

              index == widget.suborderList.length - 1 ? const SizedBox() : Padding(
                padding: EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                child: Divider(
                  color: AppColors.lightGray,
                  height: 1,
                  thickness: 1,
                ),
              )
            ],
          );
        });
  }

//endregion

//region Accept return button
  Widget acceptReturnButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: AppCommonWidgets.activeButton(
              buttonName: CommonMethods.sellerSelectedSubOrderNumberList(widget.suborderList).length > 1
                ? "Accept All Returns"
                : "Accept Return",
              onTap: () {
                widget.returnRequestedBloc.acceptReturn(
                  selectedSubOrders: CommonMethods.sellerSelectedSubOrderNumberList(widget.suborderList)
                );
              }
            ),
          ),
        ],
      ),
    );
  }
//endregion

//region Estimated return pickup date
  Widget estimatedReturnPickupDate() {
    return StreamBuilder<bool>(
        stream: widget.returnRequestedBloc.bottomSheetCtrl.stream,
        builder: (context, snapshot) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppTitleAndOptions(
                  title: AppStrings.estimatedPickupDate,
                  titleOption: SvgPicture.asset(AppImages.exclamation),
                  option: //Calender
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(widget.returnRequestedBloc.bottomSheetEstimatedReturnPickupDate,
                          style: AppTextStyle.settingText(textColor: AppColors.appBlack),
                        ),
                        horizontalSizedBox(30),
                        InkWell(
                          onTap: () {
                            widget.returnRequestedBloc.onTapCalender();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                                color: AppColors.textFieldFill1,
                                borderRadius: BorderRadius.all(Radius.circular(10))),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SvgPicture.asset(AppImages.calender, color: AppColors.appBlack,),
                                horizontalSizedBox(10),
                                Text("Update Return pickup date", style: AppTextStyle.access0(textColor: AppColors.appBlack),),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        }
    );
  }
//endregion


}
