import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/widget/return_tracking_details/return_components_buyer_view.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/widget/return_tracking_details/return_tracking_details_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';


class ReturnTrackingDetail extends StatefulWidget {
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final bool isSeller;
  final Order order;
  const ReturnTrackingDetail({Key? key, required this.suborderList, required this.buyerSubOrderBloc, required this.isSeller, required this.order}) : super(key: key);

  @override
  State<ReturnTrackingDetail> createState() => _ReturnTrackingDetailState();
}

class _ReturnTrackingDetailState extends State<ReturnTrackingDetail> {
  //region Bloc
  late ReturnTrackingDetailsBloc returnTrackingDetailsBloc;
  //endregion

  //region Init
  @override
  void initState() {
    returnTrackingDetailsBloc = ReturnTrackingDetailsBloc(
      context,
      widget.order,
      widget.buyerSubOrderBloc,
      widget.suborderList,
      widget.suborderList.first.returnPackageNumber!
    );
    returnTrackingDetailsBloc.init();
    super.initState();
  }
  //endregion


  @override
  Widget build(BuildContext context) {
   return StreamBuilder<ReturnTrackingDetailState>(
     stream: returnTrackingDetailsBloc.returnTrackingDetailCtrl.stream,
     builder: (context, snapshot) {
       if(snapshot.data == ReturnTrackingDetailState.Success){
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: SingleChildScrollView(child: body()),
          );
       }
       return const SizedBox();
     }
   );
  }


  //region Body
  Widget body(){
    return selfLogistic();
  }
  //endregion


  // This method is no longer used - we're keeping it for reference
  // but removing all the trackingDetailsBloc references
  /*
  //region Tracking options
  Widget trackingOptions(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Select product delivery method
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "Select return method",
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: AppColors.appBlack,
              ),
            ),
            horizontalSizedBox(10),
          ],
        ),
        verticalSizedBox(20),
        //Logistic and self
        Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              groupName(),
              verticalSizedBox(20),
              additionalNoteSave(),
            ]
        )
      ],
    );
  }
  //endregion
  */


  //region Self and logistic
  Widget selfLogistic(){
    return StreamBuilder<bool>(
      stream: returnTrackingDetailsBloc.logisticCtrl.stream,
      builder: (context, snapshot) {
        return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              groupNameReadOnly(),
              verticalSizedBox(20),
              ///Seller fields
              Visibility(
                  visible: returnTrackingDetailsBloc.returnTrackingDetailResponse.data!.selfReturnByStore ?? false,
                  child: ReturnSellerBuyerView(returnTrackingDetailsBloc: returnTrackingDetailsBloc)),
              ///Logistic fields
              Visibility(
                  visible: returnTrackingDetailsBloc.returnTrackingDetailResponse.data!.returnByLogisticPartner ?? false,
                  child: ReturnLogisticBuyerView(returnTrackingDetailsBloc: returnTrackingDetailsBloc)),
              verticalSizedBox(20),
              additionalNotesReadOnly(),
              AppCommonWidgets.bottomListSpace(context: context),
            ]
        );
      }
    );
  }
  //endregion

  //region Group name read-only
  Widget groupNameReadOnly(){
    return AppTitleAndOptions(
      title: AppStrings.groupName,
      titleOption: SvgPicture.asset(AppImages.exclamation),
      option: Text(
        returnTrackingDetailsBloc.groupNameTextCtrl.text.isNotEmpty
          ? returnTrackingDetailsBloc.groupNameTextCtrl.text
          : "Not assigned",
        style: TextStyle(color: AppColors.appBlack),
      ),
    );
  }
  //endregion

  //region Additional Notes read-only
  Widget additionalNotesReadOnly(){
    return Column(
      children: [
        AppTitleAndOptions(
          title: AppStrings.additionalNotes,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: Text(
            returnTrackingDetailsBloc.notesTextCtrl.text.isNotEmpty
              ? returnTrackingDetailsBloc.notesTextCtrl.text
              : "No additional notes provided",
            style: TextStyle(color: AppColors.appBlack),
          ),
        ),
        verticalSizedBox(13),
      ],
    );
  }
  //endregion

  // Helper method for action button
  Widget sellerAllOrderActionButton({
    required Color colors,
    required String buttonName,
    required Function onPress,
  }) {
    return InkWell(
      onTap: () {
        onPress();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: colors,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          buttonName,
          style: TextStyle(
            color: AppColors.appWhite,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
