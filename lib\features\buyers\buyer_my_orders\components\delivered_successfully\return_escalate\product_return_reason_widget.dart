import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductReturnReasonWidget extends StatelessWidget {
  final SubOrder subOrder;
  final TextEditingController textController;
  final bool isLastItem;

  const ProductReturnReasonWidget({
    Key? key,
    required this.subOrder,
    required this.textController,
    this.isLastItem = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Product info row with image and name
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Product image
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              child: Container(
                color: AppColors.textFieldFill1,
                height: 40,
                width: 40,
                child: extendedImage(
                  subOrder.productImage,
                  context,
                  75, // Reduced by 25% from 100
                  75, // Reduced by 25% from 100
                  customPlaceHolder: AppImages.productPlaceHolder,
                  cache: true,
                ),
              ),
            ),

            // Spacing
            horizontalSizedBox(12),

            // Product name and details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product brand and name
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: subOrder.productBrand ?? "",
                          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                        ),
                        TextSpan(
                          text: " ${subOrder.productName ?? ""}",
                          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        // Spacing
        verticalSizedBox(12),

        // Return reason text field
        AppTextFields.allTextField(
          context: context,
          maxEntry: 200,
          maxLines: 3,
          minLines: 3,
          textEditingController: textController,
          hintText: "${AppStrings.returnReason} for this product",
        ),

        // Divider if not the last item
        if (!isLastItem)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Divider(color: AppColors.lightGray, height: 1, thickness: 1),
          ),
      ],
    );
  }
}
