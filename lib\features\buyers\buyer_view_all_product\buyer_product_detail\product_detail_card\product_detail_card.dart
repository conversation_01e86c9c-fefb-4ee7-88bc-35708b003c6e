import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_product_detail/product_detail_card/product_detail_card_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

//region Product detail card
class ProductDetailCard extends StatefulWidget {
  final String title;
  final String? titleValue;
  final Widget? customTitleValue;
  final Widget expandedWidget;
  final bool isArrowVisible;

  const ProductDetailCard({Key? key, required this.title, required this.expandedWidget, required this.isArrowVisible, this.titleValue, this.customTitleValue}) : super(key: key);

  @override
  State<ProductDetailCard> createState() => _ProductDetailCardState();
}
//endregion

class _ProductDetailCardState extends State<ProductDetailCard> {
  //region Bloc
  late ProductDetailCardBloc productDetailCardBloc;
  //endregion
  //region Init
  @override
  void initState() {
    productDetailCardBloc = ProductDetailCardBloc(context);
    super.initState();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body(){
    return StreamBuilder<bool>(
      stream: productDetailCardBloc.refreshCtrl.stream,
      builder: (context, snapshot) {
        return Container(
          margin: const EdgeInsets.only(bottom: 20,left: 10,right: 10),
          width: double.infinity,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: AppColors.lightestGrey2,
          ),
          child: InkWell(
            onTap: widget.isArrowVisible?(){
              productDetailCardBloc.onTapExpandAndCollapse();
            }:null,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //Title
                Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  padding: const EdgeInsets.symmetric(vertical: 3),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(widget.title,
                          maxLines: 2,
                          style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack),),
                      ),
                      Visibility(
                        visible: widget.isArrowVisible,
                        child: RotatedBox(
                            quarterTurns: productDetailCardBloc.isExpanded?2:0,
                            child: Icon(Icons.keyboard_arrow_down,color: AppColors.appBlack,)),
                      )
                    ],
                  ),
                ),
                //Title value
                //If custom title value is null then show this else normal text widget
                widget.customTitleValue??Text(widget.titleValue!,style: AppTextStyle.heading3Regular(textColor: AppColors.appBlack),),

                //Expanded data
                Visibility(
                  visible: productDetailCardBloc.isExpanded,
                  child: widget.expandedWidget,
                )




              ],
            ),
          ),

        );
      }
    );
  }
//endregion

}
