import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class OkayAndCancelDialogScreen extends StatelessWidget {
  final dynamic onTapSecondButton;
  final dynamic onTapFirstButton;
  final BuildContext previousScreenContext;
  final String message;
  final bool isMessageVisible;
  final String firstButtonName;
  final String secondButtonName;
  const OkayAndCancelDialogScreen({
    Key? key,
    this.onTapSecondButton,
    required this.previousScreenContext,
    this.message = "",
    this.isMessageVisible = false,
    this.firstButtonName = "Save",
    this.secondButtonName = "Discard",
    this.onTapFirstButton,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Visibility(
            visible: isMessageVisible,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Text(
                message,
                style: AppTextStyle.settingText(textColor: AppColors.appBlack),
              ),
            )),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            //Button 1
            Expanded(
              child: InkWell(
                onTap: () {
                  if (onTapFirstButton == null) {
                    Navigator.pop(context);
                  } else {
                    onTapFirstButton() ?? Navigator.pop(context);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 13),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(80),
                    color: AppColors.brandBlack,
                  ),
                  child: Center(
                    child: Text(
                      firstButtonName,
                      style:
                          AppTextStyle.access0(textColor: AppColors.appWhite),
                    ),
                    // child: appText(firstButtonName,color: AppColors.white)
                  ),
                ),
              ),
            ),
            horizontalSizedBox(20),
            //Button 2
            Expanded(
              child: InkWell(
                onTap: () {
                  if (onTapSecondButton == null) {
                    Navigator.pop(context);
                  } else {
                    Navigator.pop(context);
                    onTapSecondButton();
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 13),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(80),
                    color: AppColors.textFieldFill1,
                  ),
                  child: Center(
                    child: Text(
                      secondButtonName,
                      style:
                          AppTextStyle.access0(textColor: AppColors.appBlack),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
