import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/return_period/return_period_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReturnPeriod extends StatefulWidget {
  final SellerReturnStoreWarrantyBloc sellerReturnStoreWarrantyBloc;

  const ReturnPeriod({Key? key, required this.sellerReturnStoreWarrantyBloc})
      : super(key: key);

  @override
  State<ReturnPeriod> createState() => _ReturnPeriodState();
}

class _ReturnPeriodState extends State<ReturnPeriod> {
  //region Bloc
  late ReturnPeriodBloc returnPeriodBloc;

  //endregion

  //region init
  @override
  void initState() {
    returnPeriodBloc =
        ReturnPeriodBloc(context, widget.sellerReturnStoreWarrantyBloc);
    returnPeriodBloc.init();
    // TODO: implement initState
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return returnPeriod();
  }

  //region Return period
  Widget returnPeriod() {
    return StreamBuilder<bool>(
        stream: returnPeriodBloc.returnPeriodCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppTitleAndOptions(
                title: "Return period",
                option: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///A minimum
                    Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Text(
                          AppStrings.aMaximumOf7Days,
                          style: AppTextStyle.subTitle(
                              textColor: AppColors.brandBlack),
                        )),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ///Number
                        Expanded(
                          child: AppCommonWidgets.dropDownOptions(
                            onTap: () {
                              returnPeriodBloc.onTapReturnPeriod();
                            },
                            context: context,
                            hintText: "Days",
                            value: returnPeriodBloc
                                        .sellerReturnStoreWarrantyBloc
                                        .productLevelSettingResponse
                                        .data!
                                        .returnPeriod ==
                                    null
                                ? ""
                                : returnPeriodBloc
                                    .sellerReturnStoreWarrantyBloc
                                    .productLevelSettingResponse
                                    .data!
                                    .returnPeriod
                                    .toString(),
                          ),
                          // child: AppTextFields.onlyNumberTextField(
                          //     context: context,
                          //     maxEntry: 3,
                          //     onChanged: (value) {
                          //       returnPeriodBloc.onChangeDays();
                          //     },
                          //     textEditingController: returnPeriodBloc.returnPeriodTextCtrl,
                          //     hintText: "days"),
                        ),
                        horizontalSizedBox(10),

                        ///Day from delivery
                        Text(
                          "days from delivery day",
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          );
        });
  }
//endregion
}
