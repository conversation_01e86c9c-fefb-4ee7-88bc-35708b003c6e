# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\SRC\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\GIT-drive\\frontend" PROJECT_DIR)

set(FLUTTER_VERSION "2.2.0+53" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 2 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 2 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 53 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\SRC\\flutter"
  "PROJECT_DIR=D:\\GIT-drive\\frontend"
  "FLUTTER_ROOT=D:\\SRC\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\GIT-drive\\frontend\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\GIT-drive\\frontend"
  "FLUTTER_TARGET=D:\\GIT-drive\\frontend\\lib\\main_dev.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9jZjdhOWQwODAwZjJhNWRhMTY2ZGJlMGViOWZiMjQ3NjAxODI2OWIxLw==,RkxVVFRFUl9BUFBfRkxBVk9SPWRldg=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\GIT-drive\\frontend\\.dart_tool\\package_config.json"
)
