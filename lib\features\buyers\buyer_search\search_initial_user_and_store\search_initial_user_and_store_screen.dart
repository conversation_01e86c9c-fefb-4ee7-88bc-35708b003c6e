import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_initial_user_and_store_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_recommended_products/search_recommended_products.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/widgets/app_divider/app_devider.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class SearchInitialUserAndStoreScreen extends StatefulWidget {
  const SearchInitialUserAndStoreScreen({super.key});

  @override
  State<SearchInitialUserAndStoreScreen> createState() => _SearchInitialUserAndStoreScreenState();
}

class _SearchInitialUserAndStoreScreenState extends State<SearchInitialUserAndStoreScreen>with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin<SearchInitialUserAndStoreScreen>  {

  //Keep alive
  @override
  bool get wantKeepAlive => true;
  //Bloc
  late SearchInitialUserAndStoreBloc searchInitialUserAndStoreBloc;
  //Tab controller 
  late TabController tabController = TabController(length: 2, vsync: this, initialIndex: 0);
  
  //region Init
  @override
  void initState() {
    searchInitialUserAndStoreBloc = SearchInitialUserAndStoreBloc(context,tabController);
    searchInitialUserAndStoreBloc.init();
    super.initState();
  }
  //endregion


  //region Dispose
  @override
  void dispose() {
    searchInitialUserAndStoreBloc.dispose();
    super.dispose();
  }
  //endregion
  
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        length: 2,
        child: body());
  }

  //Widget body
Widget body(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        tabs(),
        const AppDivider(),
        Container(
          alignment: Alignment.topCenter,
        height: MediaQuery.of(context).size.height - (MediaQuery.of(context).size.height * 0.23),
        child: tabView()),

      ],
    );
}
//endregion





//region Tabs
  StreamBuilder<bool> tabs() {

    return StreamBuilder<bool>(
        stream: searchInitialUserAndStoreBloc.refreshTabCtrl.stream,
        builder: (context, snapshot) {
          return SizedBox(
            height: kToolbarHeight * 0.8,
            child: TabBar(
                controller: searchInitialUserAndStoreBloc.tabController,
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: AppColors.appBlack,
                    // width: 0.0,
                  ),
                ),
                onTap: (index) {

                  // notificationBloc.tabRefreshCtrl.sink.add(true);
                },
                padding: EdgeInsets.zero,
                // isScrollable: true,
                tabs:[
                  //Store
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.stores,
                          style:
                          AppTextStyle.settingHeading1(textColor: searchInitialUserAndStoreBloc.tabController.index == 0 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                  //Products
                  SizedBox(
                    height: kToolbarHeight * 0.8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppStrings.products,
                          style:
                          AppTextStyle.settingHeading1(textColor: searchInitialUserAndStoreBloc.tabController.index == 1 ? AppColors.appBlack : AppColors.writingBlack1),
                        ),
                      ],
                    ),
                  ),
                  //People
                  // SizedBox(
                  //   height: kToolbarHeight * 0.8,
                  //   child: Row(
                  //     mainAxisSize: MainAxisSize.min,
                  //     mainAxisAlignment: MainAxisAlignment.center,
                  //     crossAxisAlignment: CrossAxisAlignment.center,
                  //     children: [
                  //       Text(
                  //         AppStrings.people,
                  //         style:
                  //         AppTextStyle.settingHeading1(textColor: searchInitialUserAndStoreBloc.tabController.index == 2 ? AppColors.appBlack : AppColors.writingBlack1),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  //Contact
                  // SizedBox(
                  //   height: kToolbarHeight * 0.8,
                  //   child: Row(
                  //     mainAxisSize: MainAxisSize.min,
                  //     mainAxisAlignment: MainAxisAlignment.center,
                  //     crossAxisAlignment: CrossAxisAlignment.center,
                  //     children: [
                  //       Text(
                  //         AppStrings.contact,
                  //         style:
                  //         AppTextStyle.settingHeading1(textColor: searchInitialUserAndStoreBloc.tabController.index == 2 ? AppColors.appBlack : AppColors.writingBlack1),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                ]
            ),
          );
        });

  }
//endregion



//region Tab view
  TabBarView tabView() {
    return TabBarView(
        controller: searchInitialUserAndStoreBloc.tabController,
        children: [
          // SearchInitialStoreScreen(),
          RecommendedStoreAndUserScreen(isRecommendedStore: true, ),
          SearchRecommendedProducts(),
          // RecommendedStoreAndUserScreen(isRecommendedStore: false,),
          // FindYourCustomersScreen(reference: AppConstants.appData.isUserView!?AppConstants.appData.userReference!:AppConstants.appData.storeReference!,isFromSearch: true,)
        ]
    );

  }
//endregion


}
