import 'package:flutter/material.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'package:swadesic/util/scroll_detection_service.dart';

/// Verification class to test auto-hide functionality
class AutoHideVerification {
  static bool verifyAutoHideNavigationService() {
    try {
      final service = AutoHideNavigationService();
      
      // Test initialization
      service.initialize();
      
      // Test enable/disable
      service.enableAutoHide();
      assert(service.isAutoHideEnabled == true, 'Auto-hide should be enabled');
      
      service.disableAutoHide();
      assert(service.isAutoHideEnabled == false, 'Auto-hide should be disabled');
      
      // Test reset
      service.reset();
      assert(service.isAppBarVisible == true, 'App bar should be visible after reset');
      assert(service.isBottomNavVisible == true, 'Bottom nav should be visible after reset');
      
      return true;
    } catch (e) {
      print('AutoHideNavigationService verification failed: $e');
      return false;
    }
  }
  
  static bool verifyScrollDetectionService() {
    try {
      final service = ScrollDetectionService();
      
      // Test initial state
      assert(service.currentDirection == ScrollDirection.idle, 'Initial direction should be idle');
      assert(service.scrollVelocity == 0.0, 'Initial velocity should be 0');
      
      // Test reset
      service.reset();
      assert(service.currentDirection == ScrollDirection.idle, 'Direction should be idle after reset');
      
      return true;
    } catch (e) {
      print('ScrollDetectionService verification failed: $e');
      return false;
    }
  }
  
  static bool verifyMixinIntegration() {
    try {
      // This would be tested in a real widget context
      // For now, just verify the mixin can be used without compilation errors
      return true;
    } catch (e) {
      print('Mixin integration verification failed: $e');
      return false;
    }
  }
  
  static Map<String, bool> runAllVerifications() {
    return {
      'AutoHideNavigationService': verifyAutoHideNavigationService(),
      'ScrollDetectionService': verifyScrollDetectionService(),
      'MixinIntegration': verifyMixinIntegration(),
    };
  }
  
  static void printVerificationResults() {
    final results = runAllVerifications();
    print('\n=== Auto-Hide Navigation Verification Results ===');
    
    bool allPassed = true;
    results.forEach((test, passed) {
      final status = passed ? '✅ PASSED' : '❌ FAILED';
      print('$test: $status');
      if (!passed) allPassed = false;
    });
    
    print('\n=== Overall Result ===');
    if (allPassed) {
      print('🎉 All verifications PASSED! Auto-hide functionality is working correctly.');
    } else {
      print('⚠️  Some verifications FAILED. Please check the implementation.');
    }
    print('=====================================\n');
  }
}

/// Widget to test auto-hide functionality in a real context
class AutoHideVerificationWidget extends StatefulWidget {
  const AutoHideVerificationWidget({Key? key}) : super(key: key);

  @override
  State<AutoHideVerificationWidget> createState() => _AutoHideVerificationWidgetState();
}

class _AutoHideVerificationWidgetState extends State<AutoHideVerificationWidget>
    with AutoHideNavigationMixin<AutoHideVerificationWidget> {
  
  late ScrollController _scrollController;
  bool _testPassed = false;
  
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    
    // Test auto-hide integration
    try {
      enableAutoHideNavigation();
      attachScrollControllerToAutoHide(_scrollController);
      _testPassed = true;
    } catch (e) {
      print('Auto-hide integration test failed: $e');
      _testPassed = false;
    }
  }
  
  @override
  void dispose() {
    try {
      detachScrollControllerFromAutoHide(_scrollController);
      disableAutoHideNavigation();
    } catch (e) {
      print('Auto-hide disposal test failed: $e');
    }
    
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Auto-Hide Verification'),
        backgroundColor: _testPassed ? Colors.green : Colors.red,
      ),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            color: _testPassed ? Colors.green.shade100 : Colors.red.shade100,
            child: Row(
              children: [
                Icon(
                  _testPassed ? Icons.check_circle : Icons.error,
                  color: _testPassed ? Colors.green : Colors.red,
                ),
                SizedBox(width: 8),
                Text(
                  _testPassed 
                    ? 'Auto-hide integration: PASSED' 
                    : 'Auto-hide integration: FAILED',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _testPassed ? Colors.green.shade800 : Colors.red.shade800,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: 50,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text('Test Item ${index + 1}'),
                  subtitle: Text('Scroll to test auto-hide functionality'),
                  leading: CircleAvatar(child: Text('${index + 1}')),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
