import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/maintenance/maintenance_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Maintenance screen
class MaintenanceScreen extends StatefulWidget {
  const MaintenanceScreen({super.key});

  @override
  State<MaintenanceScreen> createState() => _MaintenanceScreenState();
}
//endregion





class _MaintenanceScreenState extends State<MaintenanceScreen> {
  //region Bloc
  late MaintenanceBloc maintenanceBloc;
  //endregion

  //region Init
  @override
  void initState() {
    maintenanceBloc = MaintenanceBloc(context);
    maintenanceBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    maintenanceBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(child: body()),
    );
  }
  //region Body
  Widget body(){
    return StreamBuilder<MaintenanceState>(
        stream: maintenanceBloc.maintenanceStateCtrl.stream,
        initialData: MaintenanceState.Loading,
        builder: (context, snapshot) {
          //Loading
          if(snapshot.data == MaintenanceState.Loading){
            return AppCommonWidgets.appCircularProgress();
          }
          //Success
          if(snapshot.data == MaintenanceState.Success){
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 30),
              child: Column(
                // shrinkWrap: true,
                // mainAxisSize: MainAxisSize.min,
                // mainAxisAlignment: MainAxisAlignment.center,
                // crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  iconTextImage(),
                  Expanded(child: verticalSizedBox(50)),

                  info(),
                  Expanded(child: verticalSizedBox(50)),
                  contactAndSocialMedia(),
                ],
              ),
            );
          }
          return const SizedBox();
        }
    );
  }
//endregion

//region Icon text and image
  Widget iconTextImage(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [

        Image.asset(AppImages.appIcon,height: 50,width: 50,),

        verticalSizedBox(28),
        // const Text("Pause and Refresh",style:TextStyle(
        //   fontFamily: AppConstants.lavishlyRegular,
        //   fontSize: 43,
        //   color: AppColors.appBlack
        // ),),
        verticalSizedBox(15),
        Image.asset(AppImages.maintenanceIcon,height: CommonMethods.calculateWebWidth(context: context)/2,width: CommonMethods.calculateWebWidth(context: context)/2,),
        verticalSizedBox(46),




      ],
    );
  }
//endregion


//region Info
  Widget info(){
    return Column(
      children: [
        //Title
        Text(maintenanceBloc.maintenanceResponse.message!,
          textAlign: TextAlign.center,
          style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack),
        ),
        // verticalSizedBox(10),
        // //Sub-title
        // Text("${maintenanceBloc.maintenanceResponse.returnMessage!} ${maintenanceBloc.maintenanceResponse.returnTime!}",
        //   textAlign: TextAlign.center,
        //   style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack),
        // ),
      ],
    );
  }
//endregion


//region  Contact and Social media
  Widget contactAndSocialMedia(){
    return Column(
      children: [
        //Contact
        Text("In case of emergency, reach out at ${maintenanceBloc.maintenanceResponse.contactInfo!.number!} or ${maintenanceBloc.maintenanceResponse.contactInfo!.email!}",
          textAlign: TextAlign.center,
          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0),),
        verticalSizedBox(16),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 40),
          padding: const EdgeInsets.all(10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                  onTap: (){
                    CommonMethods.launchSocialMediaUrl( url:maintenanceBloc.maintenanceResponse.socialLinks!.whatsapp! );
                  },
                  child: Image.asset(AppImages.maintenanceWhatsAppIcon,height: 32,width: 32,)),
              InkWell(
                  onTap: (){
                    CommonMethods.launchSocialMediaUrl( url:maintenanceBloc.maintenanceResponse.socialLinks!.instagram! );
                  },
                  child: Image.asset(AppImages.maintenanceInstagramAppIcon,height: 32,width: 32,)),
              InkWell(
                  onTap: (){
                    CommonMethods.launchSocialMediaUrl( url:maintenanceBloc.maintenanceResponse.socialLinks!.threads! );
                  },
                  child: Image.asset(AppImages.maintenanceThreadsAppIcon,height: 32,width: 32,)),
              InkWell(
                  onTap: (){
                    CommonMethods.launchSocialMediaUrl( url:maintenanceBloc.maintenanceResponse.socialLinks!.twitter! );
                  },
                  child: Image.asset(AppImages.maintenanceTwitterAppIcon,height: 32,width: 32,)),

            ],
          ),
        ),
      ],
    );
  }
//endregion


}
