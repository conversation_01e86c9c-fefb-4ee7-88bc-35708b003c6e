import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_config_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/deactivate_and_delete_store/deactivate_and_delete_store_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';

//region Deactivate and delete store
class DeactivateAndDeleteStoreScreen extends StatefulWidget {
  final String storeReference;

  const DeactivateAndDeleteStoreScreen({Key? key, required this.storeReference})
      : super(key: key);

  @override
  State<DeactivateAndDeleteStoreScreen> createState() =>
      _DeactivateAndDeleteStoreScreenState();
}
//endregion

class _DeactivateAndDeleteStoreScreenState
    extends State<DeactivateAndDeleteStoreScreen> {
  //region Build
  late final DeactivateAndDeleteStoreBloc deactivateAndDeleteStoreBloc;

  @override
  void initState() {
    super.initState();
    deactivateAndDeleteStoreBloc =
        DeactivateAndDeleteStoreBloc(context, widget.storeReference);
    deactivateAndDeleteStoreBloc.init();
  }

  //endregion

  @override
  void dispose() {
    deactivateAndDeleteStoreBloc.dispose();
    super.dispose();
  }

  //endregion
  //region Build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(
        child: body(),
      ),
    );
  }

  //endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.deActivateAndDeleteStore,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  //region Body
  Widget body() {
    return StreamBuilder<DeactivateAndDeleteStoreState>(
        stream:
            deactivateAndDeleteStoreBloc.deActivateAndDeleteStateCtrl.stream,
        initialData: DeactivateAndDeleteStoreState.Loading,
        builder: (context, snapshot) {
          // return  RefreshScreen(onTapRefreshFunction: deactivateAndDeleteStoreBloc.init,);

          if (snapshot.data == DeactivateAndDeleteStoreState.Success) {
            return ListView(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
              children: [
                // ifYouWant(),
                // verticalSizedBox(20),
                closingStore(),
                verticalSizedBox(30),
                deActivateStore(),
                verticalSizedBox(30),
                deleteStore(),
              ],
            );
          }

          if (snapshot.data == DeactivateAndDeleteStoreState.Loading) {
            return Center(child: AppCommonWidgets.appCircularProgress());
          }
          if (snapshot.data == DeactivateAndDeleteStoreState.Failed) {
            return Center(
                child: AppCommonWidgets.errorWidget(
                    onTap: () {
                      deactivateAndDeleteStoreBloc.init();
                    },
                    errorMessage: AppStrings.commonErrorMessage));
          }
          return const SizedBox();
        });
  }

//endregion

//region If you want
  Widget ifYouWant() {
    return Text(
      AppStrings.activateDeactivateStoreDescription,
      textAlign: TextAlign.left,
      style: AppTextStyle.subTitle(textColor: AppColors.brandBlack),
    );
  }

//endregion

//region Closing store
  Widget closingStore() {
    return Consumer<StoreDashboardDataModel>(
      builder: (BuildContext context,
          StoreDashboardDataModel storeDashboardDataModel, Widget? child) {
        return Consumer<StoreConfigDataModel>(
          builder: (BuildContext context,
              StoreConfigDataModel storeConfigDataModel, Widget? child) {
            return AppTitleAndOptions(
              title: AppStrings.openCloseStore,
              option: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.openCloseStoreDescription,
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack),
                  ),
                  //Button to close and open
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed:
                            storeDashboardDataModel.storeDashBoard.isActive!
                                ? () async {
                                    if (storeDashboardDataModel
                                        .storeDashBoard.openForOrder!) {
                                      await CommonMethods.appDialogBox(
                                        context: context,
                                        widget: SaveOrDiscard(
                                          onTapSave: (value) {
                                            if (value) {
                                              deactivateAndDeleteStoreBloc
                                                  .storeOpenClose();
                                            }
                                          },
                                          previousScreenContext: context,
                                          isMessageVisible: true,
                                          message:
                                              'Are you sure you want to close your store?',
                                          firstButtonName: 'Yes, Close Store',
                                          secondButtonName: 'Cancel',
                                          popPreviousScreen: false,
                                        ),
                                      );
                                    } else {
                                      await deactivateAndDeleteStoreBloc
                                          .storeOpenClose();
                                    }
                                  }
                                : null, // Disable button if store is not active
                        child: Container(
                          margin: const EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            color: !storeDashboardDataModel
                                    .storeDashBoard.isActive!
                                ? AppColors.textFieldFill1.withOpacity(
                                    0.5) // Dimmed color when disabled
                                : storeDashboardDataModel
                                        .storeDashBoard.openForOrder!
                                    ? AppColors.textFieldFill1
                                    : AppColors.brandBlack,
                            borderRadius: BorderRadius.circular(60),
                          ),
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 20),
                          child: Text(
                            storeDashboardDataModel.storeDashBoard.openForOrder!
                                ? AppStrings.closeTheStore
                                : AppStrings.openTheStore,
                            style: AppTextStyle.heading3Medium(
                              textColor: !storeDashboardDataModel
                                      .storeDashBoard.isActive!
                                  ? AppColors.writingBlack0.withOpacity(
                                      0.5) // Dimmed text when disabled
                                  : storeDashboardDataModel
                                          .storeDashBoard.openForOrder!
                                      ? AppColors.writingBlack0
                                      : AppColors.appWhite,
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }

//endregion

//region Deactivate store
  Widget deActivateStore() {
    final bloc = deactivateAndDeleteStoreBloc;
    return Consumer<StoreDashboardDataModel>(
      builder:
          (BuildContext context, StoreDashboardDataModel value, Widget? child) {
        return AppTitleAndOptions(
            title: AppStrings.activateDeactivateStore,
            option: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppStrings.activateDeactivateStoreDescription,
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                ),
                Row(
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () async {
                        if (value.storeDashBoard.trustcenterDetail!) {
                          await CommonMethods.appDialogBox(
                            context: context,
                            widget: SaveOrDiscard(
                              onTapSave: (value) {
                                if (value) {
                                  bloc.activeDeActiveApiCall();
                                }
                              },
                              previousScreenContext: context,
                              isMessageVisible: true,
                              message:
                                  'Are you sure you want to ${value.storeDashBoard.isActive! ? 'deactivate' : 'activate'} your store?',
                              firstButtonName: value.storeDashBoard.isActive!
                                  ? 'Yes, Deactivate'
                                  : 'Yes, Activate',
                              secondButtonName: 'Cancel',
                              popPreviousScreen: false,
                            ),
                          );
                        } else {
                          CommonMethods.toastMessage(
                              AppStrings.completeCheckList, context,
                              toastShowTimer: 5);
                        }
                      },
                      child: Container(
                        margin: const EdgeInsets.only(top: 10),
                        decoration: BoxDecoration(
                            color: value.storeDashBoard.isActive!
                                ? AppColors.orange.withOpacity(0.1)
                                : AppColors.brandBlack,
                            borderRadius: BorderRadius.circular(60)),
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                            vertical: 12, horizontal: 20),
                        // constraints: const BoxConstraints(minWidth: 200),
                        child: Text(
                          value.storeDashBoard.isActive!
                              ? AppStrings.deActivateMyStore
                              : AppStrings.activateMyStore,
                          style: AppTextStyle.heading3Medium(
                              textColor: value.storeDashBoard.isActive!
                                  ? AppColors.orange
                                  : AppColors.appWhite),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ));
      },
    );
  }

//endregion

//region Delete store
  Widget deleteStore() {
    return AppTitleAndOptions(
      title: AppStrings.deleteTheStore,
      option: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.yourStoreWillBeCompletelyDeleted,
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              deactivateAndDeleteStoreBloc.goToExitSurvey();
            },
            child: Container(
              margin: const EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: AppColors.red.withOpacity(.05),
                borderRadius: BorderRadius.circular(60),
              ),
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              // constraints: const BoxConstraints(minWidth: 200),
              child: Text(
                AppStrings.deleteMyStore,
                style: AppTextStyle.access0(textColor: AppColors.red),
              ),
            ),
          )
        ],
      ),
    );
  }
//endregion
}
