import 'package:flutter/foundation.dart';

/// Custom exception class for handling API error responses
class ApiErrorResponseMessage implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  ApiErrorResponseMessage({
    required this.message,
    this.statusCode,
    this.data,
  });

  /// Factory constructor to create an ApiErrorResponseMessage from a JSON response
  factory ApiErrorResponseMessage.fromJson(Map<String, dynamic> json) {
    return ApiErrorResponseMessage(
      message: json['message'] ?? 'An unknown error occurred',
      statusCode: json['statusCode'],
      data: json['data'] ?? json,
    );
  }

  /// Create an ApiErrorResponseMessage from a Dio error response
  factory ApiErrorResponseMessage.fromDioError(dynamic error) {
    try {
      if (error?.response?.data != null) {
        final responseData = error.response.data;
        if (responseData is Map<String, dynamic>) {
          return ApiErrorResponseMessage.fromJson(responseData);
        }
        return ApiErrorResponseMessage(
          message: responseData.toString(),
          statusCode: error.response?.statusCode,
          data: responseData,
        );
      }
    } catch (e) {
      debugPrint('Error parsing API error response: $e');
    }

    return ApiErrorResponseMessage(
      message: error?.toString() ?? 'An unknown error occurred',
      statusCode: error?.response?.statusCode,
    );
  }

  @override
  String toString() => message;
}
