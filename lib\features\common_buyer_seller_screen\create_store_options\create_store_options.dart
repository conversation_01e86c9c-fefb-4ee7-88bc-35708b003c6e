import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/comment_common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_preview_store/create_preview_store_screen.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding_get_started/seller_onboarding_get_started_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class CreateStoreOptions extends StatefulWidget {
  final bool allowCreateStore;
  final bool allowCreateTestStore;
  const CreateStoreOptions(
      {super.key,
      this.allowCreateStore = true,
      this.allowCreateTestStore = true});

  @override
  State<CreateStoreOptions> createState() => _CreateStoreOptionsState();
}

class _CreateStoreOptionsState extends State<CreateStoreOptions> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  // Show other options bottom sheet
  void _showOtherOptionsBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.appWhite,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(bottom: 20),
                decoration: BoxDecoration(
                  // color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Text(
                'Other Store Options',
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 20),
              // Test Store Option
              InkWell(
                onTap: () async {
                  Navigator.pop(context); // Close the bottom sheet
                  if (!widget.allowCreateTestStore) {
                    return CommonMethods.toastMessage(
                        AppStrings.youCanNotCreateMoreThen, context,
                        toastShowTimer: 5);
                  } else if (CommonMethods().isStaticUser()) {
                    return CommonMethods().goToSignUpFlow();
                  }
                  var screen = const SellerOnBoardingGetStartedScreen(
                    isTestStore: true,
                  );
                  var route = MaterialPageRoute(builder: (context) => screen);
                  Navigator.push(
                      AppConstants.userStoreCommonBottomNavigationContext,
                      route);
                },
                child: card(
                    icon: AppImages.testStoreIcon,
                    title: "Create a Test store",
                    subTitle:
                        "Test store will be visible to you only. You can place test orders and test other flows upfront."),
              ),
              const SizedBox(height: 20),
              // Preview Store Option
              InkWell(
                onTap: () async {
                  Navigator.pop(context); // Close the bottom sheet
                  if (!widget.allowCreateTestStore) {
                    return CommonMethods.toastMessage(
                        AppStrings.youCanNotCreateMoreThen, context,
                        toastShowTimer: 5);
                  } else if (CommonMethods().isStaticUser()) {
                    return CommonMethods().goToSignUpFlow();
                  }
                  var screen = const CreatePreviewStoreScreen();
                  var route = MaterialPageRoute(builder: (context) => screen);
                  Navigator.push(
                      AppConstants.userStoreCommonBottomNavigationContext,
                      route);
                },
                child: card(
                    icon: AppImages.previewStoreIcon,
                    title: "Create a Preview store",
                    subTitle:
                        "Live glimpse of your Swadesic store with sample data. Only visible to you and others with store link."),
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  //region Body
  Widget body() {
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            //Create a store
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: InkWell(
                onTap: () async {
                  if (CommonMethods().isStaticUser()) {
                    return CommonMethods().goToSignUpFlow();
                  }
                  var screen = const CreatePreviewStoreScreen();
                  var route = MaterialPageRoute(builder: (context) => screen);
                  Navigator.push(
                      AppConstants.userStoreCommonBottomNavigationContext,
                      route);
                },
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 35,
                      child: Row(
                        // mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            AppImages.storeIcon,
                            height: 30,
                            width: 30,
                            color: AppColors.appBlack,
                          ),
                          horizontalSizedBox(10),
                          Text(
                            "Create a Swadesic store",
                            style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack),
                          ),
                        ],
                      ),
                    ),
                    verticalSizedBox(5),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "- Upgrade your business with professional Storefront with in-built Community.",
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        verticalSizedBox(5),
                        Text(
                          "- Sell your products online across PAN India or offline.",
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        verticalSizedBox(5),
                        Text(
                          "- Simplify your operations with intuitive Order management, Inventory, Tracking, Notifications and complete control over your business, branding and content.",
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        verticalSizedBox(5),
                        Text(
                          "- No upfront fees, No profit eating commissions, No hidden charges.",
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                      ],
                    ),
                    verticalSizedBox(20),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: double.infinity,
                          child: AppCommonWidgets.activeButton(
                            buttonName: "Start now",
                            onTap: () async {
                              //Check do we allow create store
                              if (!widget.allowCreateStore) {
                                return CommonMethods.toastMessage(
                                    AppStrings.youCanNotCreateMoreThen, context,
                                    toastShowTimer: 5);
                              }
                              // Allow store creation on web platform
                              //Else if static user then open login screen
                              else if (CommonMethods().isStaticUser()) {
                                return CommonMethods().goToSignUpFlow();
                              }
                              var screen =
                                  const SellerOnBoardingGetStartedScreen(
                                isTestStore: false,
                              );
                              var route = MaterialPageRoute(
                                  builder: (context) => screen);
                              Navigator.push(
                                  AppConstants
                                      .userStoreCommonBottomNavigationContext,
                                  route);
                            },
                          ),
                        ),
                        verticalSizedBox(10),
                        GestureDetector(
                          onTap: _showOtherOptionsBottomSheet,
                          child: Center(
                            child: Text(
                              "Or explore other options",
                              style: AppTextStyle.contentHeading0(
                                textColor: AppColors
                                    .appBlack, // Make it look clickable
                                // decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                        verticalSizedBox(10),
                        //region Read terms and condition
                        InkWell(
                          onTap: () {
                            CommonMethods.openAppWebView(
                                webUrl: AppConstants.appTermsAndConditionSeller,
                                context: context);
                          },
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(vertical: 20),
                            child: Text(AppStrings.readTerms,
                                style: AppTextStyle.subTitle(
                                    textColor: AppColors.brandBlack,
                                    isUnderline: true)),
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(
              height: 50,
            ),
          ],
        ));
  }
//endregion

//region Card
  Widget card(
      {required String title, required String subTitle, required String icon}) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: AppColors.lightStroke),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(icon, color: AppColors.appBlack),
          const SizedBox(
            width: 5,
          ),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
                Text(
                  subTitle,
                  maxLines: 3,
                  style: AppTextStyle.smallTextRegular(
                      textColor: AppColors.appBlack),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
//endregion
}
