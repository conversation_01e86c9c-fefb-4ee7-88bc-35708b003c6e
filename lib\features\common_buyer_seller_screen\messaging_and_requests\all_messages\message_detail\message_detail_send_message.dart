import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/message_chat_data_model/message_chat_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/messaging_response/messaging_detail.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class MessageDetailSendMessage {
  final ChatDetail chatDetail;
  final BuildContext context;
  final String toEntityReference;
  WebSocketChannel? _channel;
  late MessageChatDataModel messageChatDataModel;

  //region Constructor
  MessageDetailSendMessage({required this.chatDetail, required this.context,required this.toEntityReference}) {

    init();

  }
  //endregion

  //region CInit
  void init()async{
    //Web socket
    // String entityReference = AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!;
    // final wsUrl = Uri.parse("${AppConstants.getAllMessagesWebSocket}?entity_reference=$entityReference");
    // _channel = WebSocketChannel.connect(wsUrl);

    final wsUrl = Uri.parse("${AppConstants.getAllMessagesWebSocket}${AppConstants.appData.messagingToken}");
    _channel = WebSocketChannel.connect(wsUrl);
    await Future.delayed(Duration.zero);
    updateMessageDeliver();
    messageChatDataModel = Provider.of<MessageChatDataModel>(context, listen: false);
    sendMessage();

  }
  //endregion

  //region Send Message
  Future<void> sendMessage() async {
    try {
      String senderEntityReference = AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!;
      //Add the new message in chat detail list
      messageChatDataModel.addChatInList(dataList: [chatDetail]);
      //Wait  for 10 seconds .
      // await Future.delayed(Duration(seconds: 2));

      //Send message through web socket
      sendMessageThroughWebSocket();
    } catch (error) {
      debugPrint("Sending message error : $error");
      //Failed
      // messageDetailStateCtrl.sink.add(MessageDetailState.Failed);
      // context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
    }
  }
  //endregion


  //region Send Message Through WebSocket
  void sendMessageThroughWebSocket() {
    try {
      if (_channel == null) {
        return;
      }

      final messageData = {
        'type': 'send_message',
        'to_entity_reference': toEntityReference,
        'message': "${chatDetail.text}",
        if (chatDetail.attachments != null && chatDetail.attachments!.isNotEmpty)
          'attachments': chatDetail.attachments!.map((attachment) => {
            'originalName': attachment.originalName,
            'filename': attachment.filename,
            'type': attachment.type,
            'size': attachment.size,
            'url': attachment.url,
            'mimetype': attachment.mimetype,
          }).toList(),
      };

      // Send the message through WebSocket
      if (_channel != null) {
        _channel!.sink.add(jsonEncode(messageData));
      }

    } catch (e) {
      debugPrint('Error sending message through WebSocket: $e');
    }
  }
//endregion




  //region Update message deliver
  void updateMessageDeliver() async {
    try {
      // Listen to WebSocket stream
      _channel!.stream.listen((dynamic myData) {
        // Decode the incoming JSON string into a Map
        final Map<String, dynamic> decodedResponse = jsonDecode(myData);
        /// Check the type and process the data
        if (decodedResponse['type'] != null && decodedResponse['type'] == 'message_sent') {
          // Update the message in data model and mark as message send to server as true
          try {
            ChatDetail detail = messageChatDataModel.chatDetailList.firstWhere(
              (element) => element.messageId == chatDetail.messageId
            );
            // Update local data
            detail.messageId = decodedResponse['data']['message_id'];
            detail.isDeliveredToServer = true; // Explicitly set to true
            // Update data model
            messageChatDataModel.updateMessageDeliveredStatus(chatDetail: detail);
          } catch (e) {
            debugPrint('Error updating message status: $e');
          }
        }
      });
    } catch (e) {
      debugPrint('Error connecting to WebSocket: $e');
    }
  }
  //endregion
}