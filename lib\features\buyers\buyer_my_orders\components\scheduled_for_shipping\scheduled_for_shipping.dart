import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/scheduled_for_shipping/scheduled_for_shipping_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ScheduledForShipping extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const ScheduledForShipping(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<ScheduledForShipping> createState() => _ScheduledForShippingState();
}

class _ScheduledForShippingState extends State<ScheduledForShipping> {
  // region Bloc
  late ScheduledForShippingBloc scheduledForShippingBloc;

  // endregion

  // region Init
  @override
  void initState() {
    scheduledForShippingBloc = ScheduledForShippingBloc(
        context, widget.order, widget.buyerSubOrderBloc, widget.subOrderList);
    scheduledForShippingBloc.init();
    super.initState();
  }

  // endregion

  //region Dis update
  @override
  void didUpdateWidget(covariant ScheduledForShipping oldWidget) {
    scheduledForShippingBloc = ScheduledForShippingBloc(
        context, widget.order, widget.buyerSubOrderBloc, widget.subOrderList);
    scheduledForShippingBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed: const SizedBox(height: 1),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            cancel(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: scheduledForShippingBloc.subOrderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // //Confirmed on
                        // Container(
                        //     margin: const EdgeInsets.only(bottom: 10),
                        //     child: Text(
                        //
                        //       "Confirmed on ${CommonMethods.dateTimeAmPm(date:confirmedNotYetShippedBloc.store.orderedDate! )[1]}",
                        //       style: AppTextStyle.heading3SemiBold(textColor: AppColors.appBlack),
                        //     )),
                        verticalSizedBox(10),

                        //Delivery estimation

                        Text(
                          "${AppStrings.deliveryEstimate} ${scheduledForShippingBloc.subOrderList[index].estimatedDeliveryDate!.replaceAll("/", "-")}",
                          style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack),
                        ),
                        productInfoCard(
                            context: context,
                            subOrder:
                                scheduledForShippingBloc.subOrderList[index]),
                        //Cancel
                        Row(
                          children: [
                            AppCommonWidgets.subOrderButton(
                                buttonName: AppStrings.cancel,
                                onTap: () {
                                  // //Mark only selected
                                  scheduledForShippingBloc
                                      .subOrderList[index].isSelected = true;
                                  // //Open bottom sheet
                                  scheduledForShippingBloc.onTapCancel();
                                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                                },
                                horizontalPadding: 25),
                          ],
                        ),
                        verticalSizedBox(10),
                        //Divider
                        Visibility(
                          visible:
                              scheduledForShippingBloc.subOrderList.length -
                                      1 !=
                                  index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
        icon: AppImages.thumbUpIcon,
        componentName: AppStrings.scheduledForShipping,
        suborderList: scheduledForShippingBloc.subOrderList,
        additionalWidgets: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                AppStrings.sellerConfirmedOrder,
                style: AppTextStyle.contentHeading0(
                    textColor: AppColors.brandBlack),
              ),
            ),
            verticalSizedBox(5),
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                AppStrings.productWillSoonBe,
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ),
          ],
        ),
        isBuyerSidePickupDateShow: true,
        isEstimateDeliveryShow: false);
  }

  //endregion

  //region Cancel
  Widget cancel() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: AppStrings.cancel,
                onTap: () {
                  scheduledForShippingBloc.onTapCancel();
                  // CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.subOrderList);
                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                })),
      ],
    );
  }

//endregion
}
