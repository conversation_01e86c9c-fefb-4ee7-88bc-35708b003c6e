import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/providers/theme_provider.dart';

/// A drop-in replacement widget to render raster icons (png/jpg/webp/gif)
/// that automatically adapts to theme by applying a color filter in dark mode.
///
/// Usage:
///   ThemedImageIcon.asset('assets/icons/foo.png', size: 36)
class ThemedImageIcon extends StatelessWidget {
  final String assetName;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Color? lightColorOverlay;
  final Color? darkColorOverlay;
  final BlendMode blendMode;

  const ThemedImageIcon.asset(
    this.assetName, {
    Key? key,
    this.width,
    this.height,
    this.fit = BoxFit.contain,
    this.lightColorOverlay,
    this.darkColorOverlay,
    this.blendMode = BlendMode.srcIn,
  }) : super(key: key);

  bool _isSvg(String path) {
    final p = path.toLowerCase();
    return p.endsWith('.svg');
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: true);
    final isSvg = _isSvg(assetName);

    // Handle SVG rendering
    if (isSvg) {
      final isDark = themeProvider.isDarkMode(context);
      final svg = SvgPicture.asset(
        assetName,
        width: width,
        height: height,
        fit: fit,
      );

      // Apply color filter based on theme
      if (isDark) {
        // For dark mode, use the darkColorOverlay if provided
        if (darkColorOverlay != null) {
          return ColorFiltered(
            colorFilter: ColorFilter.mode(darkColorOverlay!, BlendMode.srcIn),
            child: svg,
          );
        }
        // Otherwise, apply color inversion for dark mode
        else {
          return ColorFiltered(
            colorFilter: const ColorFilter.matrix([
              -1, 0, 0, 0, 255, // Red
              0, -1, 0, 0, 255, // Green
              0, 0, -1, 0, 255, // Blue
              0, 0, 0, 1, 0, // Alpha
            ]),
            child: svg,
          );
        }
      }
      // For light mode, use lightColorOverlay if provided
      else if (lightColorOverlay != null) {
        return ColorFiltered(
          colorFilter: ColorFilter.mode(lightColorOverlay!, BlendMode.srcIn),
          child: svg,
        );
      }
      // Otherwise, return the original SVG
      return svg;
    }

    final isDark = themeProvider.isDarkMode(context);

    // Default filters:
    // - In dark mode, invert and slightly desaturate to improve visibility
    // - In light mode, show as-is unless an overlay is provided
    // For non-SVG images
    final Widget image = Image.asset(
      assetName,
      width: width,
      height: height,
      fit: fit,
      filterQuality: FilterQuality.medium,
    );

    if (!isDark && lightColorOverlay == null) {
      return image; // Show original in light mode
    }

    if (!isDark && lightColorOverlay != null) {
      return ColorFiltered(
        colorFilter: ColorFilter.mode(lightColorOverlay!, blendMode),
        child: image,
      );
    }

    // Dark mode behavior
    if (darkColorOverlay != null) {
      // Use provided overlay if available
      return ColorFiltered(
        colorFilter: ColorFilter.mode(darkColorOverlay!, blendMode),
        child: image,
      );
    }

    // Fallback generic inversion matrix for dark mode
    // This matrix inverses colors and keeps alpha
    const List<double> invertMatrix = <double>[
      -1, 0, 0, 0, 255, // R
      0, -1, 0, 0, 255, // G
      0, 0, -1, 0, 255, // B
      0, 0, 0, 1, 0, // A
    ];

    return ColorFiltered(
      colorFilter: ColorFilter.matrix(invertMatrix),
      child: image,
    );
  }

  /// Returns the appropriate color for SVG based on theme and provided overlays
  Color? _getSvgColor(BuildContext context, bool isDark) {
    if (!isDark) {
      return lightColorOverlay;
    } else {
      // For SVGs, we want to use the darkColorOverlay if provided,
      // otherwise default to white for dark theme
      return darkColorOverlay ?? Colors.white;
    }
  }
}
