import 'package:badges/badges.dart' as badges;
import 'package:badges/badges.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_tab_view.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/download_app_banner/download_app_banner.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/model/bottom_navigation_tab_context_model/bottom_tab_context.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation_bloc.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/initial_search_view_screen/initial_search_view_screen.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/notification/notification_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';

class UserBottomNavigation extends StatefulWidget {
  final bool isFromOnboardingFlow;
  const UserBottomNavigation({Key? key, this.isFromOnboardingFlow = false})
      : super(key: key);

  @override
  State<UserBottomNavigation> createState() => _UserBottomNavigationState();
}

class _UserBottomNavigationState extends State<UserBottomNavigation> {
//region Bloc
  late UserBottomNavigationBloc userBottomNavigationBloc;
  late BuildContext homeContext;

//endregion

  //Tab context
  late BottomTabContext bottomTabContext = BottomTabContext();

  // Auto-hide navigation service
  final AutoHideNavigationService _autoHideService =
      AutoHideNavigationService();

  //region Init
  @override
  void initState() {
    userBottomNavigationBloc = UserBottomNavigationBloc(context);
    userBottomNavigationBloc.init();
    AppConstants.userStoreCommonBottomNavigationContext = context;
    AppConstants.isBottomNavigationMounted.value = true;

    // Initialize auto-hide navigation service
    _autoHideService.initialize();

    //print("User bottom navigation mounted");
    //Make initial tab to index 0
    AppConstants.userPersistentTabController.index = 0;
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  //endregion
  //Is bottom navigation visible
  bool isHideBottomNavigation = true;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, boxConstraints) {
        if (MediaQuery.of(context).size.width < 600) {
          //print("bottom navigateiion is bigger ");
          isHideBottomNavigation = false;
          AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
        } else {
          isHideBottomNavigation = false;
          AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
        }
        return StreamBuilder<bool>(
            stream: AppConstants.bottomNavigationRefreshCtrl.stream,
            builder: (context, snapshot) {
              // Combine the existing bottom navigation refresh stream with auto-hide service
              final isBottomNavRefreshVisible = snapshot.data ?? true;
              return Stack(
                children: [
                  Scaffold(
                    body: PersistentTabView(
                      margin: EdgeInsets.zero,
                      decoration: NavBarDecoration(
                        adjustScreenBottomPaddingOnCurve: false,
                        boxShadow: AppColors.appShadow,
                        colorBehindNavBar:
                            Theme.of(context).scaffoldBackgroundColor,
                        // borderRadius: const BorderRadius.only(
                        //   topLeft: Radius.circular(10),
                        //   topRight: Radius.circular(10),
                        // ),
                      ),

                      ///On select get context
                      selectedTabScreenContext: (context) {
                        bottomTabContext.addContextAndIndex(
                            context: context!,
                            index:
                                AppConstants.userPersistentTabController.index);
                      },
                      context,
                      controller: AppConstants.userPersistentTabController,
                      screens: [
                        // BuyerAndSellerHomeScreen(),
                        BuyerHomeScreen(
                            isFromOnboardingFlow: widget.isFromOnboardingFlow),
                        const BuyerSearchScreen(),
                        const AddPostScreen(),
                        const NotificationScreen(),
                        // AppConstants.isLoggedIn.value
                        //     ?
                        CommonMethods().isStaticUser()
                            ? const SizedBox()
                            : UserProfileScreen(
                                userReference:
                                    AppConstants.appData.userReference!,
                                fromBottomNavigation: true,
                                key: const Key("buyer_home"),
                              )
                        // :
                        // MobileNumberOtpScreen(isFromBottomNavigation: true,)
                        // Center(
                        //         child: Column(
                        //           mainAxisSize: MainAxisSize.min,
                        //           mainAxisAlignment: MainAxisAlignment.center,
                        //           crossAxisAlignment: CrossAxisAlignment.center,
                        //           children: [
                        //             Row(
                        //               mainAxisSize: MainAxisSize.min,
                        //               mainAxisAlignment: MainAxisAlignment.center,
                        //               crossAxisAlignment: CrossAxisAlignment.center,
                        //               children: [
                        //                 CupertinoButton(
                        //                   padding: EdgeInsets.zero,
                        //                   onPressed: () {
                        //                     CommonMethods().goToSignUpFlow();
                        //                   },
                        //                   child: Container(
                        //                     alignment: Alignment.center,
                        //                     padding: const EdgeInsets.symmetric(
                        //                       horizontal: 20,
                        //                       vertical: 10,
                        //                     ),
                        //                     decoration: const BoxDecoration(
                        //                         color: AppColors.brandGreen, borderRadius: BorderRadius.all(Radius.circular(100))),
                        //                     child: Text(
                        //                       "Sign in to view your profile",
                        //                       style: AppTextStyle.access0(textColor: AppColors.appWhite),
                        //                     ),
                        //                   ),
                        //                 ),
                        //               ],
                        //             ),
                        //           ],
                        //         ),
                        //       ),
                      ],
                      confineInSafeArea: true,
                      backgroundColor: Theme.of(context)
                              .bottomNavigationBarTheme
                              .backgroundColor ??
                          Theme.of(context).scaffoldBackgroundColor,
                      handleAndroidBackButtonPress: true,
                      resizeToAvoidBottomInset: true,
                      hideNavigationBarWhenKeyboardShows: true,
                      popAllScreensOnTapOfSelectedTab: true,
                      stateManagement: true,
                      hideNavigationBar: !isBottomNavRefreshVisible ||
                          !_autoHideService.isBottomNavVisible,
                      items: _navBarsItems(),
                      navBarStyle: NavBarStyle.style2,
                    ),
                  ),
                  const Positioned(
                      bottom: kToolbarHeight - 10,
                      left: 0,
                      right: 0,
                      child: DownloadAppBanner())
                ],
              );
            });
      },
    );
  }

  //region Navigation bar
  List<PersistentBottomNavBarItem> _navBarsItems() {
    // Retrieve the data from the user detail data model
    LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context);
    UserDetail userDetail = loggedInUserInfoDataModel.userDetail!;
    // Retrieve the data from the User notification
    UserOrStoreNotificationDataModel userOrStoreNotificationDataModel =
        Provider.of<UserOrStoreNotificationDataModel>(context);
    // Retrieve the data from the All store notification
    AllStoreNotificationDataModel allStoreNotificationDataModel =
        Provider.of<AllStoreNotificationDataModel>(context);

    return [
      ///Home
      PersistentBottomNavBarItem(
        icon: SvgPicture.asset(
          AppImages.homeActive,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
        inactiveIcon: SvgPicture.asset(
          AppImages.homeInActive,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
      ),

      ///Search
      PersistentBottomNavBarItem(
        icon: SvgPicture.asset(
          AppImages.searchActive,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
        inactiveIcon: SvgPicture.asset(
          AppImages.searchInActive,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
      ),

      ///Add post
      PersistentBottomNavBarItem(
        onPressed: (context) {
          userBottomNavigationBloc.goToAddPost();
        },
        icon: SvgPicture.asset(
          AppImages.addPost,
          color: AppColors.appBlack,
          height: 30,
          width: 30,
        ),
        inactiveIcon: SvgPicture.asset(
          AppImages.addPost,
          height: 30,
          width: 30,
          color: AppColors.appBlack,
        ),
      ),

      ///Notification
      PersistentBottomNavBarItem(
        onPressed: (context) {
          if (CommonMethods().isStaticUser()) {
            CommonMethods().goToSignUpFlow();
            return;
          }
          // Navigate to notification tab instead of pushing a new screen
          AppConstants.userPersistentTabController.jumpToTab(3);
          if (AppConstants.currentSelectedTabContext.mounted) {
            Navigator.of(AppConstants.currentSelectedTabContext)
                .popUntil((route) => route.isFirst);
          }
        },
        inactiveIcon: Align(
            child: badges.Badge(
          badgeStyle: BadgeStyle(
            padding: EdgeInsets.all(5),
            badgeColor: AppColors.red,
          ),
          showBadge: (userOrStoreNotificationDataModel
                          .userOrStoreNotificationResponse!.notSeenCount! +
                      allStoreNotificationDataModel
                          .allStoreNotificationResponse!.notSeenCount!) ==
                  0
              ? false
              : true,
          badgeContent: (userOrStoreNotificationDataModel
                          .userOrStoreNotificationResponse!.notSeenCount! +
                      allStoreNotificationDataModel
                          .allStoreNotificationResponse!.notSeenCount!) !=
                  0
              ? Text(
                  "${(userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!) > 9 ? "+9" : (userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!)}",
                  // CommonMethods.calculateNotificationCount(number:userOrStoreNotificationDataModel.getNotificationResponse!.notSeenCount!),
                  // CommonMethods.calculateNotificationCount(number:0),
                  style: TextStyle(
                      color: AppColors.appWhite,
                      fontSize: 10,
                      fontWeight: FontWeight.w700),
                )
              : const SizedBox(),
          //alignment: Alignment.center,
          position: BadgePosition.topEnd(),
          child: SvgPicture.asset(
            AppImages.notificationInActive,
            fit: BoxFit.contain,
            color: AppColors.appBlack,
            height: 28,
            width: 28,
          ),
        )),
        icon: Align(
          child: badges.Badge(
            showBadge: (userOrStoreNotificationDataModel
                            .userOrStoreNotificationResponse!.notSeenCount! +
                        allStoreNotificationDataModel
                            .allStoreNotificationResponse!.notSeenCount!) ==
                    0
                ? false
                : true,
            badgeStyle: BadgeStyle(
              padding: EdgeInsets.all(5),
              badgeColor: AppColors.red,
            ),
            badgeContent: (userOrStoreNotificationDataModel
                            .userOrStoreNotificationResponse!.notSeenCount! +
                        allStoreNotificationDataModel
                            .allStoreNotificationResponse!.notSeenCount!) !=
                    0
                ? Text(
                    "${(userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!) > 9 ? "+9" : (userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!)}",
                    // CommonMethods.calculateNotificationCount(number:userOrStoreNotificationDataModel.getNotificationResponse!.notSeenCount!),
                    // CommonMethods.calculateNotificationCount(number:0),
                    style: TextStyle(
                        color: AppColors.appWhite,
                        fontSize: 10,
                        fontWeight: FontWeight.w700),
                  )
                : const SizedBox(),
            //alignment: Alignment.center,
            position: BadgePosition.topEnd(),
            child: SvgPicture.asset(
              AppImages.notificationActive,
              fit: BoxFit.contain,
              color: AppColors.appBlack,
              height: 28,
              width: 28,
            ),
          ),
        ),
      ),

      ///User profile
      PersistentBottomNavBarItem(
        onPressed: (dd) {
          //If ststic user

          // AppConstants.userPersistentTabController.jumpToTab(4);
          // AppConstants.userPersistentTabController.addListener(() {
          //   //print()
          // });
          if (CommonMethods().isStaticUser()) {
            // Navigator.of(context).push(MaterialPageRoute(builder: (context) => const MobileNumberOtpScreen(isFromBottomNavigation: true,)));
            CommonMethods().goToSignUpFlow();
            return;
          }
          AppConstants.userPersistentTabController.jumpToTab(4);
          if (AppConstants.currentSelectedTabContext.mounted) {
            Navigator.of(AppConstants.currentSelectedTabContext)
                .popUntil((route) => route.isFirst);
            //Pop all the way to the root screen
            //print("Tapped");
          }
        },
        // iconSize: 30,
        icon: CircleAvatar(
          key: const Key("user_bottom_profile"),
          radius: 15,
          backgroundColor: AppColors.brandBlack,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                AppImages.indiaBoarder,
                height: 30,
                width: 30,
              ),
              Positioned.fill(
                right: 0,
                left: 0,
                top: 0,
                bottom: 0,
                child: ClipOval(
                  child: userDetail.icon == null
                      ? Container(
                          height: 28,
                          width: 28,
                          padding: const EdgeInsets.all(2),
                          child: SvgPicture.asset(
                            AppImages.userPlaceHolder,
                            height: 28,
                            width: 28,
                            fit: BoxFit.fill,
                          ),
                        )
                      : Container(
                          padding: const EdgeInsets.all(2),
                          height: 28,
                          width: 28,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(15),
                            child: extendedImage(
                                userDetail.icon, context, 50, 50,
                                fit: BoxFit.fill,
                                imageWidth: 28,
                                imageHeight: 28,
                                boxShape: BoxShape.circle,
                                customPlaceHolder: AppImages.userPlaceHolder,
                                borderColor: AppColors.brandBlack),
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
        inactiveIcon: CircleAvatar(
          radius: 100,
          backgroundColor: AppColors.appWhite,
          child: ClipOval(
            child: userDetail.icon == null
                ? Container(
                    height: 28,
                    width: 28,
                    // padding: const EdgeInsets.all(3),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100),
                        border: Border.all(color: AppColors.appWhite)),
                    child: SvgPicture.asset(
                      AppImages.userPlaceHolder,
                      height: 28,
                      width: 28,
                      fit: BoxFit.fill,
                    ),
                  )
                : SizedBox(
                    // padding: const EdgeInsets.all(1),
                    height: 28,
                    width: 28,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(100),
                      child: extendedImage(
                        userDetail.icon, context, 50, 50, fit: BoxFit.fill,
                        imageWidth: 28,
                        imageHeight: 28,
                        boxShape: BoxShape.circle,
                        customPlaceHolder: AppImages.userPlaceHolder,
                        // borderColor:AppColors.brandGreen
                      ),
                    ),
                  ),
            // extendedImage(AppConstants.bottomUserStoreIcon, customPlaceHolder: AppImages.userPlaceHolder,context, 50, 50,fit: BoxFit.contain,imageWidth: 30,imageHeight: 30,
            //     boxShape: BoxShape.circle
            // ),
          ),
        ),
      ),
    ];
  }
//endregion
}
