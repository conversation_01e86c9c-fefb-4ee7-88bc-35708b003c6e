import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_order_details/product_ordered/product_order_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/variant_display_widget/variant_display_widget.dart';
import 'package:swadesic/model/seller_all_order_response/seller_order_details.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductOrderedScreen extends StatefulWidget {
  final List<GroupedCancelledOrReturnedOrder> groups;
  final List<GroupedCancelledOrReturnedOrder> cancelledReturnOrders;

  const ProductOrderedScreen({
    Key? key,
    required,
    required this.groups,
    required this.cancelledReturnOrders,
  }) : super(key: key);

  @override
  State<ProductOrderedScreen> createState() => _ProductOrderedScreenState();
}

class _ProductOrderedScreenState extends State<ProductOrderedScreen> with AutomaticKeepAliveClientMixin<ProductOrderedScreen>  {
  List<String> groupNameList = [];
  //Keep alive
  @override
  bool get wantKeepAlive => true;
//region Bloc
  late ProductOrderedBloc productOrderedBloc;
//endregion

//region Init
  @override
  void initState() {
    productOrderedBloc = ProductOrderedBloc(context);

    productOrderedBloc.init();
    takeOutGroupName();
    super.initState();
  }
//endregion

//region Take out group name
  void takeOutGroupName() {
    groupNameList.clear();
    for (var data in widget.groups) {
      groupNameList.add(data.groupName!);
    }

    ///Remove duplicates
    groupNameList = groupNameList.toSet().toList();
    //print(groupNameList);
  }
//endregion

  @override
  Widget build(BuildContext context) {
    //print("group name list length is ${groupNameList.length}");
    return StreamBuilder<bool>(
        stream: productOrderedBloc.productOrderCtrl.stream,
        builder: (context, snapshot) {
          return dropDowns();
        });
  }

//region Drop Downs
  Widget dropDowns() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
//Active order detail
        widget.groups.isEmpty
            ? const SizedBox()
            : Container(
                margin: const EdgeInsets.symmetric(horizontal: 6.5),
                decoration: BoxDecoration(color: AppColors.textFieldFill1, borderRadius: BorderRadius.circular(7)),
                child: AppDropDown(
                    borderRadiusOnTopLeftAndRight: 7.0,
                    dropDownWidget: expanded(),
                    marginBelowTitle: 0,
                    dropDownName: AppStrings.productsOrdered,
                    collapsedWidget: collapsed())),
        Visibility(visible: widget.groups.isNotEmpty, child: verticalSizedBox(30)),
        widget.cancelledReturnOrders.isEmpty
            ? const SizedBox()
            : Container(
                margin: const EdgeInsets.symmetric(horizontal: 6.5),
                decoration: BoxDecoration(color: AppColors.textFieldFill1, borderRadius: BorderRadius.circular(7)),
                child: AppDropDown(
                  marginBelowTitle: 0,
                  borderRadiusOnTopLeftAndRight: 7.0,
                  dropDownWidget: cancelledOrReturn(),
                  dropDownName: AppStrings.cancelledAndReturned,
                ),
              )
      ],
    );
  }
//endregion

//region Expanded
  Widget expanded() {
    return Column(
      children: [
        ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: groupNameList.length,
            itemBuilder: (context, rootIndex) {
//Drop down data
              return subOrdersFromStoreExpanded(groupNameList[rootIndex]);
            }),
// productOrderedBloc.isProductOrderedVisible ? verticalSizedBox(40) : verticalSizedBox(20)
      ],
    );
  }
//endregion

//region Collapsed
  Widget collapsed() {
    return Column(
      children: [
        ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: groupNameList.length,
            itemBuilder: (context, rootIndex) {
//Drop down data
              return productIconAndMultiply(groupNameList[rootIndex]);
            }),
        productOrderedBloc.isProductOrderedVisible ? verticalSizedBox(40) : verticalSizedBox(20)
      ],
    );
  }
//endregion

//region Cancelled or return
  Widget cancelledOrReturn() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.cancelledReturnOrders.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              cancelledOrderDetail(subOrder: widget.cancelledReturnOrders[index]),
              // orderDetail(subOrder: widget.cancelledReturnOrders[index]),
              index == widget.cancelledReturnOrders.length - 1
                  ? const SizedBox()
                  : Padding(
                      padding: EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                      child: Divider(
                        color: AppColors.lightGray,
                        height: 1,
                        thickness: 1,
                      ),
                    )
            ],
          );
        });
  }
//endregion

//region Sub orders from store Expanded
  Widget subOrdersFromStoreExpanded(String gName) {
    List<GroupedCancelledOrReturnedOrder> subOrderList = [];
    subOrderList.clear();
    subOrderList.addAll(widget.groups.where((element) => element.groupName == gName));
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        quantityPriceDeliveryFee(subOrderList),
        ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            itemCount: subOrderList.length,
            shrinkWrap: true,
            itemBuilder: (buildContext, index) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
cancelledOrderDetail(subOrder: subOrderList[index]),
                  index == subOrderList.length - 1
                      ? const SizedBox()
                      : Padding(
                          padding: EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                          child: Divider(
                            color: AppColors.lightGray,
                            height: 1,
                            thickness: 1,
                          ),
                        )
                ],
              );
            }),
      ],
    );
  }
//endregion

//region Quantity, price and delivery fee
  Widget quantityPriceDeliveryFee(List<GroupedCancelledOrReturnedOrder> subOrderList) {
    int quantity = 0;
    int price = 0;

    int deliveryFee = 0;

    ///Quantity
    ///
    //print("quantity orders ${subOrderList.length}");
    for (var data in subOrderList) {
      quantity = quantity + data.productQuantity!;
    }

    ///Price
    for (var data in subOrderList) {
      price = price + data.totalProductPrice!;
    }

    ///Delivery fee
    if (subOrderList.length > 1) {
      deliveryFee = subOrderList.first.storeLevelFee!;
    } else {
      deliveryFee = subOrderList.first.productDeliveryFee!;
    }

    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10, top: 20),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text("Qty: $quantity", style: AppTextStyle.subTitle(textColor: AppColors.appBlack)),
          Text("Price: ₹ $price", style: AppTextStyle.subTitle(textColor: AppColors.appBlack)),
          Text("Delivery fee: ₹ $deliveryFee", style: AppTextStyle.subTitle(textColor: AppColors.appBlack)),
          SvgPicture.asset(AppImages.exclamation)
        ],
      ),
    );
  }

//endregion


//region Order Detail
  Widget cancelledOrderDetail({required GroupedCancelledOrReturnedOrder subOrder}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
//Product name and image
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    RichText(
                      textScaleFactor: MediaQuery.textScaleFactorOf(AppConstants.globalNavigator.currentContext!),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: "${subOrder.productBrand}",
                            style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack,
                            ),
                          ),
                          TextSpan(
                              text: " ${subOrder.productName}",
// text:"Nykaa Matte Nail Lacquer & Nail Enamel 2 in 1 coloNykaa Matte Nail Lacquer & Nail Enamel 2 in 1 colors with...rs with",
                              style: AppTextStyle.contentText0(
                                textColor: AppColors.appBlack,
                              )),
// WidgetSpan(child: Text(productName,
// maxLines: 1,
// ))
                        ],
                      ),
                    ),

                    // Add variant information display
                    if (subOrder.variantDetails != null) ...[
                      const SizedBox(height: 4),
                      OrderVariantDisplayWidget(
                        variantDetails: subOrder.variantDetails,
                        showPriceInfo: false,
                      ),
                    ],
                  ],
                ),
              ),
              horizontalSizedBox(50),
              ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(6)),
                child: Container(
                  height: 35,
                  width: 35,
                  decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(6))),
                  child: extendedImage(
                    subOrder.productImage,
                    context,
                    100,
                    100,
                    cache: true,
                  ),
                ),
              ),
//Arrow
            ],
          ),
          verticalSizedBox(7),
//Delivery fee and price
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "₹${subOrder.productPrice} X ${subOrder.productQuantity} = ₹${subOrder.totalProductPrice}",
                style: AppTextStyle.contentHeading0(
                  textColor: AppColors.appBlack,
                ),
              ),
              horizontalSizedBox(15),
              Text(
                "${AppStrings.delivery}:${subOrder.productDeliveryFee != 0 ? subOrder.productDeliveryFee == 0 ? "₹${subOrder.storeLevelFee}" : "₹${subOrder.productDeliveryFee}" : "₹0"}",
                style: AppTextStyle.contentHeading0(
                  textColor: AppColors.appBlack,
                ),
              ),
              horizontalSizedBox(15),
              Text("${AppStrings.returnText}: ${subOrder.returnDays == 0 ? AppStrings.noReturn : "${subOrder.returnDays} days"}",
                  style: AppTextStyle.contentHeading0(
                    textColor: AppColors.appBlack,
                  ))
            ],
          ),
//Status
          Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: AppColors.textFieldFill1),
            margin: const EdgeInsets.only(top: 10),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Text(
              CommonMethods.subOrderStatusToString(subOrderStatus: subOrder.status!),
              style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
            ),
          )
// subOrderTag(AppStrings.confirmedNotYetShipped)
        ],
      ),
    );
  }
//endregion

//region Price, delivery fee and return fee
//   Widget priceDeliveryReturn(
//       {required String productQuantity, required String productPrice, required String totalProductPrice, required String productDeliveryFee}) {
//     return Row(
//       mainAxisSize: MainAxisSize.max,
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         appText("${productQuantity} x ₹ ${productPrice} = ₹ ${totalProductPrice}",
//             color: AppColors.writingColor2, fontWeight: FontWeight.w400, fontFamily: AppConstants.rRegular, fontSize: 14, maxLine: 1),
// // horizontalSizedBox(10),
//         appText("DF: ₹ ${productDeliveryFee}",
//             color: AppColors.writingColor2, fontWeight: FontWeight.w400, fontFamily: AppConstants.rRegular, fontSize: 14, maxLine: 1),
// // horizontalSizedBox(10),
//         appText("Return: 10days",
//             color: AppColors.writingColor2, fontWeight: FontWeight.w400, fontFamily: AppConstants.rRegular, fontSize: 14, maxLine: 1),
//       ],
//     );
//   }
//endregion

//region Product icon and multiply
  Widget productIconAndMultiply(String gName) {
    List<GroupedCancelledOrReturnedOrder> subOrderList = [];
    subOrderList.clear();
    subOrderList.addAll(widget.groups.where((element) => element.groupName == gName));
    //print(subOrderList.length);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        quantityPriceDeliveryFee(subOrderList),
        GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            shrinkWrap: true,
//itemCount: buyerHomeBloc.recentlyVisitedItemCount,
            itemCount: subOrderList.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 2.9),
              mainAxisSpacing: 20,
              crossAxisSpacing: 10,
              mainAxisExtent: 50,
            ),
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (buildContext, index) {
              return Row(
                children: [
                  InkWell(
                    onTap: () {
//productOrderedBloc.goToBuyerViewStore(storeReference: subOrderList[index].productReference!);
                    },
                    child: SizedBox(
                        width: 50,
                        child: ClipRRect(
                          borderRadius: const BorderRadius.all(Radius.circular(10)),
                          child: Container(
                            child: extendedImage(
                              "${subOrderList[index].productImage}",
                              context,
                              100,
                              100,
                              cache: true,
                            ),
                          ),
                        )),
                  ),
                  Text(
                    " X ${subOrderList[index].productQuantity}",
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  )
                ],
              );
            }),
      ],
    );
  }
//endregion
}
