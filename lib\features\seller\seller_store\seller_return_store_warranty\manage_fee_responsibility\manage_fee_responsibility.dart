import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/manage_fee_responsibility/manage_fee_responsibility_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_bloc.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Refund preferences
class ManageFeeResponsibility extends StatefulWidget {
  final SellerReturnStoreWarrantyBloc sellerReturnStoreWarrantyBloc;

  const ManageFeeResponsibility(
      {Key? key, required this.sellerReturnStoreWarrantyBloc})
      : super(key: key);

  @override
  State<ManageFeeResponsibility> createState() =>
      _ManageFeeResponsibilityState();
}

//endregion
class _ManageFeeResponsibilityState extends State<ManageFeeResponsibility> {
  //region Bloc
  late ManageFeeResponsibilityBloc manageFeeResponsibilityBloc;

  //endregion
  //region Init
  @override
  void initState() {
    manageFeeResponsibilityBloc = ManageFeeResponsibilityBloc(
        context, widget.sellerReturnStoreWarrantyBloc);
    manageFeeResponsibilityBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    // Check if it's from store screen
    if (widget.sellerReturnStoreWarrantyBloc.fromStoreScreen) {
      return storeScreenContent();
    } else {
      // For edit product screen
      return storeScreenContent();
    }
  }

//endregion

  //region Store Screen Content
  Widget storeScreenContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        refundPreferencesTitle(),
        storeDefaultFeeResponsibilityTable(),
        verticalSizedBox(20),
        storeNoteSection(),
        verticalSizedBox(25),
        // storeRefundCoverageSettings(),
        verticalSizedBox(30),
      ],
    );
  }
  //endregion


//region Refund preferences title
  Widget refundPreferencesTitle() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.refundPreferences,
          style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(5),
        Text(
          AppStrings.fullRefundAlwaysAppreciated,
          style: AppTextStyle.subTitle(textColor: AppColors.brandBlack),
        ),
        verticalSizedBox(25),
      ],
    );
  }

//endregion

//region Fee responsibility
  Widget feeResponsibility() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.feeResponsibility,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            horizontalSizedBox(50),
            sellerPay(),

            ///Todo Un-comment this when requirement is changed to toggle seller and buyer
            // Expanded(
            //   child: AppCommonWidgets.dropDownOptions(
            //     border: 100,
            //     textAlign: TextAlign.center,
            //     onTap: () {
            //       manageFeeResponsibilityBloc.onTapFeeResponsibility();
            //       //buyerOnBoardingBloc.onTapCity();
            //     },
            //     context: context,
            //     hintText: AppStrings.city,
            //     value: manageFeeResponsibilityBloc.sellerReturnStoreWarrantyBloc.productLevelSettingResponse.data!.returnCostOnSeller!?AppStrings.sellerPays:manageFeeResponsibilityBloc.sellerReturnStoreWarrantyBloc.productLevelSettingResponse.data!.returnCostOnCustomer!?AppStrings.customerPays:"",
            //   ),
            // )
          ],
        ),
        horizontalSizedBox(10),
        Text(
          manageFeeResponsibilityBloc.sellerReturnStoreWarrantyBloc
                  .productLevelSettingResponse.data!.returnCostOnSeller!
              ? AppStrings.refundInFullForCustomer
              : AppStrings.partialRefundForCustomer,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        )
      ],
    );
  }

//endregion

//region Auto cancelled order
  Widget autoCancelledOrder() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.autoCancelledOrders,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            horizontalSizedBox(30),
            sellerPay(),
          ],
        ),
        verticalSizedBox(10),
        Text(
          AppStrings.refundInFullForCustomerStoreCovers,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        )
      ],
    );
  }

//endregion

  //region Seller cancelled waiting order
  Widget sellerCancelledWaitingOrder() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.sellerCancelledWaitingOrder,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            horizontalSizedBox(30),
            sellerPay(),
          ],
        ),
        verticalSizedBox(10),
        Text(
          AppStrings.refundInFullForCancelledBeforeConfirm,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        )
      ],
    );
  }

//endregion

  //region Buyer cancel waiting order
  Widget buyerCancelWaitingOrder() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.buyerCancellWatingOrder,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            horizontalSizedBox(30),
            sellerPay(isSeller: false),
          ],
        ),
        verticalSizedBox(10),
        Text(
          AppStrings.partialRefundForCustomerText,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        )
      ],
    );
  }
//endregion

//region Drop down button
  Widget dropDownButton() {
    return AppCommonWidgets.dropDownOptions(
      onTap: () {
        //buyerOnBoardingBloc.onTapCity();
      },
      context: context,
      hintText: AppStrings.city,
      value: manageFeeResponsibilityBloc.sellerReturnStoreWarrantyBloc
              .productLevelSettingResponse.data!.returnCostOnSeller!
          ? AppStrings.sellerPays
          : manageFeeResponsibilityBloc.sellerReturnStoreWarrantyBloc
                  .productLevelSettingResponse.data!.returnCostOnCustomer!
              ? AppStrings.customerPays
              : "",
    );
    // return Container(
    //   // height: 30,
    //   padding: const EdgeInsets.symmetric(horizontal: 10),
    //   decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), color: AppColors.lightestGrey),
    //   child: DropdownButton<String>(
    //     isExpanded: false,
    //     style: AppTextStyle.heading4Bold(textColor: AppColors.appBlack),
    //     underline: const SizedBox(),
    //     value: ReturnWarrantyDataModel.returnCostOnSeller!
    //         ? manageFeeResponsibilityBloc.refundConstOn.first
    //         : manageFeeResponsibilityBloc.refundConstOn.last,
    //     borderRadius: BorderRadius.circular(10),
    //     items: manageFeeResponsibilityBloc.refundConstOn.map<DropdownMenuItem<String>>((String value) {
    //       return DropdownMenuItem(
    //           value: value,
    //           child: Text(
    //             value,
    //           ));
    //     }).toList(),
    //     onChanged: (value) {
    //       manageFeeResponsibilityBloc.onSelectReturnCost(
    //           isSellerPay: value == manageFeeResponsibilityBloc.refundConstOn.first,
    //           isCustomerPay: value == manageFeeResponsibilityBloc.refundConstOn.last);
    //       // //print(value);
    //       //Refresh data
    //       //preparePackageDeliveryBloc.onSelectDropdown(data: value!, isDeliver: true);
    //     },
    //   ),
    // );

    // return Container(
    //   padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 5),
    //   decoration: BoxDecoration(
    //       color:AppColors.lightestGrey,
    //       borderRadius: BorderRadius.circular(100)
    //   ),
    //   child: Row(
    //     mainAxisSize: MainAxisSize.min,
    //     mainAxisAlignment: MainAxisAlignment.center,
    //     crossAxisAlignment: CrossAxisAlignment.center,
    //     children: [
    //     Container(
    //         height: 20,
    //         width: 100,
    //         alignment: Alignment.center,
    //         child: Text(ReturnWarrantyDataModel.returnCostOnSeller!?"Seller pays":"Customer pays",style: AppTextStyle.heading4Bold(textColor: AppColors.appBlack),)),
    //     Container(
    //       width: 20,
    //         // height: 20,
    //         // width: 20,
    //         alignment: Alignment.center,
    //         // padding: EdgeInsets.symmetric(horizontal: 3,vertical: 5),
    //         child: const Icon(Icons.keyboard_arrow_down_outlined))
    //
    //   ],),
    // );
  }

//endregion

//region Seller pay
  Widget sellerPay({bool isSeller = true}) {
    return Flexible(
      child: AppCommonWidgets.dropDownOptions(
        isArrowVisible: false,
        textAlign: TextAlign.center,
        border: 100,
        onTap: () {
          //buyerOnBoardingBloc.onTapCity();
        },
        context: context,
        hintText: isSeller ? AppStrings.sellerPays : AppStrings.customerPays,
        value: "",
      ),
    );
  }
//endregion

  //region Store Screen Specific Widgets
  Widget storeDefaultFeeResponsibilityTable() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.defaultFeeResponsibility,
          style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(15),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.lightGray, width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // Header row
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.lightGray.withOpacity(0.3),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        AppStrings.scenario,
                        style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        AppStrings.transactionFeeAndTDS,
                        style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        AppStrings.deliveryFeeAndTDSValue,
                        style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              // Data rows
              _buildTableRow(AppStrings.buyerCancels, AppStrings.buyerPays,
                  AppStrings.refundIfNotShipped),
              _buildTableRow(AppStrings.storeCancels, AppStrings.storePays,
                  AppStrings.refundedToBuyer),
              _buildTableRow(AppStrings.autoCancel, AppStrings.storePays,
                  AppStrings.refundedToBuyer),
              _buildTableRow(AppStrings.buyerReturns, AppStrings.storePays,
                  AppStrings.notRefundedAlreadyShipped,
                  isLast: true),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTableRow(String scenario, String fee, String refund,
      {bool isLast = false}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: isLast
            ? null
            : Border(
                bottom: BorderSide(color: AppColors.lightGray, width: 1),
              ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              scenario,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              fee,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              refund,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget storeNoteSection() {
    return Text(
      AppStrings.noteTransactionFeeText,
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
    );
  }

  Widget storeRefundCoverageSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.refundCoverageSettings,
          style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
        Text(
          AppStrings.chooseWhichFeesToCover,
          style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(20),
        // Disabled checkboxes
        _buildDisabledCheckbox(AppStrings.coverTransactionFeeAndTDS,
            AppStrings.buyerGetsFullRefund),
        verticalSizedBox(15),
        _buildDisabledCheckbox(AppStrings.coverDeliveryFeeEvenAfterShipping,
            AppStrings.buyerGetsFullDeliveryRefund),
        _buildCoverageSettingsQuestion(),
      ],
    );
  }

  Widget _buildDisabledCheckbox(String title, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Opacity(
        opacity: 0.3, // 50% opacity
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              Icons.check_box_outline_blank,
              color: AppColors.borderColor1,
              size: 30,
            ),
            horizontalSizedBox(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyle.smallTextRegular(
                        textColor: AppColors.appBlack),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverageSettingsQuestion() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.doYouWantTheseCoverageSettings,
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(15),
          Row(
            children: [
              Expanded(
                child: CupertinoButton(
                  color: AppColors.brandBlack,
                  borderRadius: BorderRadius.circular(100),
                  onPressed: () {
                    _navigateToSupport();
                  },
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Text(
                    AppStrings.yesCoverage,
                    style: AppTextStyle.access0(textColor: AppColors.appWhite),
                  ),
                ),
              ),
              horizontalSizedBox(15),
              Expanded(
                child: CupertinoButton(
                  color: AppColors.lightGray,
                  borderRadius: BorderRadius.circular(100),
                  onPressed: () {
                    _navigateToSupport();
                  },
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Text(
                    AppStrings.noCoverage,
                    style: AppTextStyle.access0(textColor: AppColors.appBlack),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  void _navigateToSupport() {
    var screen = const SupportScreen(isReport: false);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

}
