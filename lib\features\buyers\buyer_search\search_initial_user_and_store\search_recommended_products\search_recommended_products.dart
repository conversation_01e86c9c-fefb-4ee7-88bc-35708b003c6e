import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/initial_store_and_people_card.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_initial_user_and_store_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_recommended_products/search_recommended_products_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_recommended_products/search_recommended_products_pagination.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/widgets/app_divider/app_devider.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/features/widgets/support_the_swadesic_movement/support_the_swadesic_movement.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

class SearchRecommendedProducts extends StatefulWidget {
  const SearchRecommendedProducts({super.key});

  @override
  State<SearchRecommendedProducts> createState() =>
      _SearchRecommendedProductsState();
}

class _SearchRecommendedProductsState extends State<SearchRecommendedProducts>
    with
        SingleTickerProviderStateMixin,
        AutomaticKeepAliveClientMixin<SearchRecommendedProducts> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //Bloc
  late SearchRecommendedProductsBloc searchRecommendedProductsBloc;

  //Tab controller
  late TabController tabController =
      TabController(length: 2, vsync: this, initialIndex: 0);

  //region Init
  @override
  void initState() {
    searchRecommendedProductsBloc = SearchRecommendedProductsBloc(context);
    searchRecommendedProductsBloc.init();
    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    searchRecommendedProductsBloc.dispose();
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double width = constraints.maxWidth;
        return body(width: width);
      },
    );
  }

  //region Body
  Widget body({required double width}) {
    // Get reference to the RecommendedStoreAndUserDataModel
    return Consumer<ProductDataModel>(
      builder: (BuildContext context, ProductDataModel productDataModel,
          Widget? child) {
        //Recommended list
        List<Product> recommendedList = productDataModel.allProducts
            .where((element) => searchRecommendedProductsBloc
                .recommendedProductsList
                .any((e) => e.productReference == element.productReference))
            .toList();

        return StreamBuilder<SearchRecommendedProductsState>(
            stream: searchRecommendedProductsBloc
                .searchRecommendedProductsCtrl.stream,
            initialData: SearchRecommendedProductsState.Loading,
            builder: (context, snapshot) {
              //Success
              if (snapshot.data == SearchRecommendedProductsState.Success) {
                return RefreshIndicator(
                    color: AppColors.brandBlack,
                    onRefresh: () async {
                      await searchRecommendedProductsBloc
                          .getSearchRecommendedProducts();
                    },
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 50),
                        child: Column(
                          children: [
                            GridView.builder(
                                addAutomaticKeepAlives: false,
                                addRepaintBoundaries: false,
                                padding: const EdgeInsets.only(bottom: 20),
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: recommendedList.length,
                                // itemCount: 6,
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.6),
                                  crossAxisCount: 2,
                                  mainAxisSpacing: 0,
                                  crossAxisSpacing: 0,
                                  mainAxisExtent: (width / 2) +
                                      CommonMethods.textHeight(
                                          context: context,
                                          textStyle:
                                              AppTextStyle.contentHeading0(
                                                  textColor:
                                                      AppColors.appBlack)) +
                                      CommonMethods.textHeight(
                                          context: context,
                                          textStyle:
                                              AppTextStyle.contentHeading0(
                                                  textColor:
                                                      AppColors.appBlack)) +
                                      CommonMethods.textHeight(
                                          context: context,
                                          textStyle: AppTextStyle.access0(
                                              textColor: AppColors.appBlack)) +
                                      5,
                                ),
                                itemBuilder: (context, index) {
                                  return InkWell(
                                    onTap: () {
                                      searchRecommendedProductsBloc
                                          .goToViewProductScreen(
                                              product: recommendedList[index],
                                              index: index);
                                    },
                                    child: Opacity(
                                      opacity:
                                          recommendedList[index].inStock == 0
                                              ? 0.4
                                              : 1.0,
                                      child: Container(
                                          padding: EdgeInsets.zero,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                const BorderRadius.only(
                                                    topLeft:
                                                        Radius.circular(10),
                                                    topRight:
                                                        Radius.circular(10)),
                                            color: AppColors.appWhite,
                                            border: Border.all(
                                                color: AppColors.lightGray2),
                                            boxShadow: [
                                              BoxShadow(
                                                offset: const Offset(0, 1),
                                                blurRadius: 4,
                                                color: AppColors.appBlack
                                                    .withOpacity(0.1),
                                              ),
                                            ],
                                          ),
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              ///Product card
                                              AppCommonWidgets
                                                  .productCardInGrid(
                                                // productImage:
                                                //     recommendedList[index]
                                                //             .prodImages!
                                                //             .isEmpty
                                                //         ? null
                                                //         : recommendedList[index]
                                                //             .prodImages![0]
                                                //             .productImage,
                                                // productBrand:
                                                //     recommendedList[index]
                                                //         .brandName!,
                                                // productName:
                                                //     recommendedList[index]
                                                //         .productName!,
                                                // sellingPrice:
                                                //     recommendedList[index]
                                                //         .sellingPrice!
                                                //         .toString(),
                                                // mrp: recommendedList[index]
                                                //     .mrpPrice!
                                                //     .toString(),
                                                context: context,
                                                screenWidth: width,
                                                product: recommendedList[index],
                                              ),

                                              // ///Shadow
                                              // recommendedList[index].inStock == 0
                                              //     ? Container(
                                              //   padding: EdgeInsets.zero,
                                              //   decoration: BoxDecoration(
                                              //     borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                                              //     color: AppColors.appWhite.withOpacity(0.2),
                                              //     border: Border.all(color: AppColors.lightGray2),
                                              //     boxShadow: [
                                              //       BoxShadow(
                                              //         offset: const Offset(0, 1),
                                              //         blurRadius: 5,
                                              //         color: AppColors.appBlack.withOpacity(0.2),
                                              //       ),
                                              //     ],
                                              //   ),
                                              // )
                                              //     : const SizedBox()
                                              ///Todo un-comment
                                              // Positioned(left: 5, top: 5, child: productRatings(index)),
                                              // Positioned(top: 0, right: 0, child: save(index))
                                            ],
                                          )),
                                    ),
                                    // child: Container(
                                    //     padding: EdgeInsets.zero,
                                    //     decoration: BoxDecoration(
                                    //
                                    //       borderRadius: BorderRadius.only(topLeft: Radius.circular(10),
                                    //           topRight: Radius.circular(10)),
                                    //       color: Colors.white,
                                    //       border: Border.all(color: AppColors.lightGray2),
                                    //       boxShadow: [
                                    //         BoxShadow(
                                    //           offset: const Offset(0, 1),
                                    //           blurRadius: 5,
                                    //           color: AppColors.appBlack.withOpacity(0.2),
                                    //         ),
                                    //       ],
                                    //     ),
                                    //     child: Stack(
                                    //       alignment: Alignment.center,
                                    //       children: [
                                    //         Column(
                                    //           mainAxisAlignment: MainAxisAlignment.start,
                                    //           crossAxisAlignment: CrossAxisAlignment.start,
                                    //           mainAxisSize: MainAxisSize.min,
                                    //           children: [
                                    //             Expanded(
                                    //                 child: productImage(index)
                                    //             ),
                                    //             SizedBox(
                                    //               height: 70,
                                    //               child: Column(
                                    //                 mainAxisAlignment: MainAxisAlignment.center,
                                    //                 crossAxisAlignment: CrossAxisAlignment.start,
                                    //                 mainAxisSize: MainAxisSize.min,
                                    //                 children: [
                                    //                   verticalSizedBox(5),
                                    //                   Expanded(child: productName(index)),
                                    //                   productPrice(index),
                                    //                   verticalSizedBox(5)
                                    //                 ],
                                    //               ),
                                    //             )
                                    //
                                    //
                                    //           ],
                                    //         ),
                                    //         Positioned(
                                    //             left: 5,
                                    //             top: 5,
                                    //
                                    //             child: productRatings(index)),
                                    //         Positioned(
                                    //             top: 0,
                                    //             right: 0,
                                    //             child: save(index))
                                    //       ],
                                    //     )
                                    // ),
                                  );
                                }),
                            paginationLoading()
                          ],
                        ),
                      ),
                    ));
              }
              //Empty
              if (snapshot.data == SearchRecommendedProductsState.Empty) {
                return NoResult(message: AppStrings.noResults);
                // return AppCommonWidgets.errorWidget(onTap:(){
                //   recommendedStoreAndUserBloc.init();
                // },errorMessage: AppStrings.noResults);
              }
              //Loading
              if (snapshot.data == SearchRecommendedProductsState.Loading) {
                return AppCommonWidgets.appCircularProgress();
              }
              //Failed
              if (snapshot.data == SearchRecommendedProductsState.Failed) {
                return AppCommonWidgets.errorWidget(
                    onTap: () {
                      searchRecommendedProductsBloc.init();
                    },
                    errorMessage: AppStrings.unableToFetchRecommendedProducts);
              }
              return const SizedBox();
            });
      },
    );
  }

//endregion

//region Pagination loading
  Widget paginationLoading() {
    return StreamBuilder<SearchRecommendedProductsPaginationState>(
        stream: searchRecommendedProductsBloc
            .searchRecommendedProductsPagination
            .searchRecommendedProductsPaginationStateCtrl
            .stream,
        initialData: SearchRecommendedProductsPaginationState.Loading,
        builder: (context, snapshot) {
          //If  empty then return
          // if (recommendedList.isEmpty || recommendedList.length < 10) {
          //   return const SupportTheSwadesicMovement();
          // }
          //End
          if (snapshot.data == SearchRecommendedProductsPaginationState.Done) {
            return const SupportTheSwadesicMovement();
          }
          //Loading
          if (snapshot.data ==
              SearchRecommendedProductsPaginationState.Loading) {
            return VisibilityDetector(
                key: UniqueKey(),
                onVisibilityChanged: (visibilityInfo) {
                  var visiblePercentage = visibilityInfo.visibleFraction * 100;
                  if (visiblePercentage == 100) {
                    searchRecommendedProductsBloc
                        .searchRecommendedProductsPagination
                        .onPaginationLoadingVisible();
                  }
                },
                child: AppCommonWidgets.appCircularProgress(
                    isPaginationProgress: true));
          }
          return const SupportTheSwadesicMovement();
        });
  }
//endregion
}
