import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/prepare_package_delivery/prepare_package_delivery_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivery_setting_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';

class PreparePackageDelivery extends StatefulWidget {
  final SellerStoreDeliverySettingBloc sellerStoreDeliverySettingBloc;
  const PreparePackageDelivery({Key? key, required this.sellerStoreDeliverySettingBloc}) : super(key: key);

  @override
  State<PreparePackageDelivery> createState() => _PreparePackageDeliveryState();
}

class _PreparePackageDeliveryState extends State<PreparePackageDelivery> {
  //region Bloc
  late PreparePackageDeliveryBloc preparePackageDeliveryBloc;
  //endregion

  //region Init
  @override
  void initState() {
    preparePackageDeliveryBloc = PreparePackageDeliveryBloc(context,widget.sellerStoreDeliverySettingBloc);
    preparePackageDeliveryBloc.init();
    super.initState();
  }
  //endregion
  //region Dispose
  @override
  void dispose() {
    preparePackageDeliveryBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return  StreamBuilder<bool>(
      stream: preparePackageDeliveryBloc.preparePackageDisplayCtrl.stream,
      builder: (context, snapshot) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///Time to prepare & ship package

            AppTitleAndOptions(
              title: AppStrings.timeToPrepareAndShipPackage,
              option:Row(children: [
                Expanded(
                  child: AppCommonWidgets.dropDownOptions(
                      onTap: () {
                        preparePackageDeliveryBloc.onTapTimeToPrepare();
                      },
                      context: context,
                      hintText: "Days",
                      value: preparePackageDeliveryBloc.selectedTimeToPrepare),
                ),
                // Expanded(
                //   child:   AppCommonWidgets.dropDownOptions(onTap: (){
                //     preparePackageDeliveryBloc.onTapTimeToPrepare();
                //     // sellerStoreLocationBloc.onTapState();
                //   },
                //       context: context,
                //       hintText:AppStrings.state,
                //       value: preparePackageDeliveryBloc.selectedTimeToPrepare
                //   ),
                // ),
                horizontalSizedBox(9),
                Text(AppStrings.fromConfirmation,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),)

              ],),
            ),

            verticalSizedBox(20),

            ///Time to deliver
            AppTitleAndOptions(
              title: AppStrings.timeToDeliverTheOrder,
              option:Row(children: [
                Expanded(
                  child: AppCommonWidgets.dropDownOptions(
                      onTap: () {
                        preparePackageDeliveryBloc.onTapTimeToDeliver();                      },
                      context: context,
                      hintText: "Days",
                      value: preparePackageDeliveryBloc.selectedDelivery,
                  ),
                ),
                horizontalSizedBox(9),
                Text(AppStrings.fromDateOfOrder,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),)

              ],),
            ),
            //If delivery time is less then prepare time
            Visibility(
                visible:
                SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.timeToPrepare !=null &&
                    SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.timeToDeliver != null
                &&
                SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.timeToDeliver!
                    <=SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.timeToPrepare!,
                child: Text(AppStrings.estimatedDaysCanNotSmallerThenPrepareTime,style: AppTextStyle.smallText(textColor: AppColors.red),)),
            verticalSizedBox(23)
          ],

        );
      }
    );
  }


}
