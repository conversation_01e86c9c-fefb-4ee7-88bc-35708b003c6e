import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/seller_home_service/seller_home_service.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/store_user_product_navigation/store_user_product_navigation.dart';
import 'package:encrypt/encrypt.dart' as encrypt;

enum AllStoreNotificationState { Loading, Success, Failed, Empty }

class AllStoreNotificationBloc {
  // region Common Variables
  BuildContext context;
  late GetNotificationResponse allStoreNotificationResponse = GetNotificationResponse();
  late AllStoreNotificationDataModel allStoreNotificationDataModel;
  final Function(int) unSeenCount;

  ///Get Store List
  late SellerHomeService sellerHomeService;
  static late StoreListResponse storeListResponse;

  // endregion

  //region Controller
  final allStoreNotificationCtrl = StreamController<AllStoreNotificationState>.broadcast();
  final tabRefreshCtrl = StreamController<bool>.broadcast();

  // region | Constructor |
  AllStoreNotificationBloc(this.context, this.unSeenCount);

  // endregion

  // region Init
  init() {
    //Notification data to data model initialize
    allStoreNotificationDataModel = Provider.of<AllStoreNotificationDataModel>(context, listen: false);

    //Get  store list create dby user
    getStoreListCreatedByUser();
    //For ground
    forGroundNotification();
    //Get all store notification
    getAllStoreNotification();
  }

// endregion

  ///1
  //region For ground notification
  void forGroundNotification() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      //If not mounted
      if (context.mounted) {
        //Get all store notification
        getAllStoreNotification();
      }
    });
  }

  //endregion

  ///3
  //region Get All store notification
  Future<void> getAllStoreNotification() async {
    //region Try
    try {
      //Api call
      allStoreNotificationResponse = await NotificationService().getAllStoreNotification();
      //Add all data in data all store notification data model
      allStoreNotificationDataModel.addAllStoreNotification(notificationData: allStoreNotificationResponse);
      //Empty
      if (allStoreNotificationResponse.notifications!.isEmpty) {
        allStoreNotificationCtrl.sink.add(AllStoreNotificationState.Empty);
        return;
      }
      //On seen count call back
      unSeenCount(allStoreNotificationResponse.notSeenCount!);
      //Success
      context.mounted ? allStoreNotificationCtrl.sink.add(AllStoreNotificationState.Success) : null;
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //Failed
      allStoreNotificationCtrl.sink.add(AllStoreNotificationState.Failed);
      CommonMethods.toastMessage(error.message!, context);
      //CommonMethods.snackBar(error.message!, context);
    } catch (error) {
      //print(error);
      //Failed
      allStoreNotificationCtrl.sink.add(AllStoreNotificationState.Failed);
    }
  }

  //endregion

  //region Get StoreList created by user
  void getStoreListCreatedByUser() async {
    try {
      storeListResponse = await SellerHomeService().getSellerStore();
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion

  //region Update Notification
  updateNotification({required NotificationDetail notificationDetail}) async {
    //region Try
    try {
      //If product is seen then return
      if (notificationDetail.notificationStatus == "NOT_SEEN") {
        //Update ui is seen
        allStoreNotificationDataModel.markNotificationAsSeen(notificationDetail: notificationDetail);
        //Api call
        // await NotificationService().updateNotification(notificationReference: [notificationDetail.notificationReference!], notifiedUserOrStore:notificationDetail.notifiedUser!);
         NotificationService().updateNotification(body: {
          "notification_list": [notificationDetail.notificationReference!],
          "notified_user": [notificationDetail.notifiedUser!],
          "is_all_notifications": false
        });
      }
      //If notification contains a url
      navigateToScreen(notificationDetail: notificationDetail);

      // if(notificationDetail.externalUrl != null){
      //   context.mounted?CommonMethods.opeAppWebView(webUrl: notificationDetail.externalUrl!, context: context):null;
      //   return;
      // }else{
      //   //Navigate
      //   navigateToScreen(notificationDetail: notificationDetail);
      // }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message!, context);
      //CommonMethods.snackBar(error.message!, context);
    } catch (error) {
      //print(error);
      // var snackBar =  SnackBar(content: Text(error.toString()));
      // ScaffoldMessenger.of(context).showSnackBar(snackBar);
      // return;
    }
  }

  //endregion

  //region Navigation
  navigateToScreen({required NotificationDetail notificationDetail}) async {
    ///1
    //Check is notified reference is same as current store reference.
    // If it is true then call navigation
    if (notificationDetail.notifiedUser == AppConstants.appData.storeReference) {
      //Go to comment
      // if (notificationDetail.notificationType == NotificationEnumns.CONTENT_LIKED.name || notificationDetail.notificationType == NotificationEnumns.SOMEONE_COMMENTED_ON_CONTENT.name) {
      //   goToComment(commentOrPostReference: notificationDetail.notificationAbout!, notificationEnumns: notificationDetail.notificationType!);
      //   return;
      // }
      //If actionPage is not null
      if (notificationDetail.actionPage != null) {
        //Navigate
        StoreUserProductNavigation().goToScreen(actionPage:notificationDetail.actionPage! );
        return;
      } else {
        //Navigation
        StoreUserProductNavigation().navigateToStoreProductAndStore(references: notificationDetail.notificationAbout!);
        return;
      }
    }

    ///2
    ///Check is the notified reference is different then current store reference
    ///If it is true then switch to the exact store and call navigation
    if (notificationDetail.notifiedUser != AppConstants.appData.storeReference) {
      //Switch to seller with notification tab navigation
      CommonMethods.switchToSeller(
          storeReference: notificationDetail.notifiedUser!,
          storeId: storeListResponse.storeList!.firstWhere((element) => element.storeReference == notificationDetail.notifiedUser!).storeid!,
          context: context,
          switchToNotificationTab: true);

      //Show message that switching to the another store
      context.mounted?CommonMethods.toastMessage("Switching to ${storeListResponse.storeList!.firstWhere((element) => element.storeReference == notificationDetail.notifiedUser!).storehandle}", AppConstants.userStoreCommonBottomNavigationContext,toastShowTimer: 3):null;

      //Wait for account switch to complete before navigation
      await Future.delayed(const Duration(milliseconds: 800));

      //Navigate immediately after store switch
      //If actionPage is not null
      if (notificationDetail.actionPage != null) {
        //Navigate
        StoreUserProductNavigation().goToScreen(actionPage:notificationDetail.actionPage! );
        return;
      }
      else{
        //Navigation
        StoreUserProductNavigation().navigateToStoreProductAndStore(references: notificationDetail.notificationAbout!);
      }

    }
  }

  //endregion

  //region Go to comment
  void goToComment({required String commentOrPostReference, required String notificationEnumns}) async {
    var screen = SinglePostViewScreen(
      postReference: commentOrPostReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }

  //endregion

  void encryptData(String data, String keyString) {
    final key = encrypt.Key.fromUtf8(keyString);
    final iv = encrypt.IV.fromLength(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    final encrypted = encrypter.encrypt(data, iv: iv);
    //print("Encrypted data is ${encrypted.base64}");
    decryptData(encrypted.base64, keyString);
  }

  void decryptData(String encryptedData, String keyString) {
    final key = encrypt.Key.fromUtf8(keyString);
    final iv = encrypt.IV.fromLength(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
    final decrypted = encrypter.decrypt(encrypted, iv: iv);
    //print("Decrypted data is $decrypted");
  }

//region Dispose
  void dispose() {}
//endregion
}
