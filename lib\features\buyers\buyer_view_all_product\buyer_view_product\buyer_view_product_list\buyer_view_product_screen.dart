import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/buy_button.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_product_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_products_pagination.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_label_dropdown/product_label_dropdown.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

// region Buyer View Product Screen
class BuyerViewProductScreen extends StatefulWidget {
  // final List<int> productIdList;
  //final StoreProductResponse storeProductResponse;
  final SearchScreenEnum openingFrom;
  final List<Product> productList;
  final int index;
  final String? searchedText;
  final bool isFromAddProduct;
  // final Function? paginationApiCall;

  // final String? productReference;

  //final StoreProductResponse storeProductResponse;

  const BuyerViewProductScreen({
    Key? key,
    required this.productList,
    required this.index,
    this.searchedText,
    this.isFromAddProduct = false,
    required this.openingFrom,
    // this.productReference
  }) : super(key: key);

  @override
  _BuyerViewProductScreenState createState() => _BuyerViewProductScreenState();
}
// endregion

class _BuyerViewProductScreenState extends State<BuyerViewProductScreen> {
  //Width
  double width = 0.0;

  // region Bloc
  late BuyerViewProductBloc buyerViewProductBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerViewProductBloc = BuyerViewProductBloc(
        context,
        widget.productList,
        // widget.productReference==null?"":widget.productReference!,
        widget.productList.first.storeReference!,
        widget.isFromAddProduct,
        widget.index,
        widget.openingFrom,
        widget.searchedText ?? "");
    buyerViewProductBloc.init();
    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    imageCache.clear();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        width = constraints.maxWidth;
        return StreamBuilder<bool>(
            stream: buyerViewProductBloc.screenRefreshCtrl.stream,
            builder: (context, snapshot) {
              return Scaffold(
                backgroundColor: AppColors.appWhite,
                appBar: widget.isFromAddProduct ? null : appBar(),
                resizeToAvoidBottomInset: false,
                body: body(),
                // body: SafeArea(
                //     child: body()
                //     child:ListView.builder(
                //         shrinkWrap: true,
                //         //controller: buyerViewProductBloc.listViewPageCtrl,
                //         scrollDirection: Axis.vertical,
                //         itemCount: 50,
                //         itemBuilder:(BuildContext,index){
                //           return Container(height: 100,width: 100,color: index%2 == 0 ?Colors.red:Colors.green);
                //         }
                //
                //     ),
              );
            });
      },
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: widget.searchedText == null
            ? buyerViewProductBloc.productList.first.storehandle!.toLowerCase()
            : widget.searchedText!,
        isCartVisible:
            AppConstants.appData.storeReference != null ? false : true,
        isMembershipVisible:
            AppConstants.appData.storeReference != null ? false : true,
        isDefaultMenuVisible: false,
        onTapCart: !widget.isFromAddProduct
            ? () {
                buyerViewProductBloc.gotoShoppingCart();
              }
            : null);
  }

  //endregion

  // region Body
  Widget body() {
    /// Retrieve the data from the StoreInfoModel
    SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
        Provider.of<SellerOwnStoreInfoDataModel>(context);

    return StreamBuilder<BuyerViewProductState>(
        stream: buyerViewProductBloc.singleProductViewCtrl.stream,
        initialData: BuyerViewProductState.Success,
        builder: (context, snapshot) {
          if (snapshot.data == BuyerViewProductState.Success) {
            return Consumer<ProductDataModel>(
              builder: (BuildContext context, ProductDataModel productDataModel,
                  Widget? child) {
                //Get all product list which are in buyer view product bloc
                List<Product> productList = [];
                productList = productDataModel.allProducts
                    .where((element) => buyerViewProductBloc.productList
                        .map((e) => e.productReference)
                        .contains(element.productReference))
                    .toList();
                return ScrollablePositionedList.builder(
                    // padding: const EdgeInsets.only(bottom: 10),
                    shrinkWrap: true,
                    initialScrollIndex: widget.index,
                    // itemScrollController: buyerViewProductBloc.itemScrollController,
                    scrollDirection: Axis.vertical,
                    // controller:buyerViewProductBloc.scrollController ,
                    itemCount: productList.length + 1,
                    itemBuilder: (context, index) {
                      if (index < productList.length) {
                        return ProductDetailFullCard(
                          product: productList[index],
                          isFromAddProduct: widget.isFromAddProduct,
                        );
                        // return Column(
                        //   mainAxisSize: MainAxisSize.min,
                        //   mainAxisAlignment: MainAxisAlignment.start,
                        //   crossAxisAlignment: CrossAxisAlignment.center,
                        //   // padding: EdgeInsets.zero,
                        //   children: [
                        //     productAppBar(
                        //         productImage: productList[index].prodImages == null ||
                        //             productList[index].prodImages!.isEmpty
                        //             ? null
                        //             : productList[index].prodImages!.first.productImage,
                        //         productReference: productList[index].productReference!,
                        //         storeHandle: productList[index].storehandle!,
                        //         storeImage: widget.isFromAddProduct
                        //             ? sellerOwnStoreInfoDataModel.storeInfo!.icon
                        //             : productList[index].storeIcon,
                        //         storeReference: productList[index].storeReference!),
                        //     Container(
                        //         color: AppColors.textFieldFill1,
                        //         width: width,
                        //         height: width,
                        //         child: productList[index].prodImages!.isEmpty
                        //             ? SvgPicture.asset(AppImages.productPlaceHolder)
                        //             : productImage(product: productList[index])),
                        //     verticalSizedBox(1),
                        //
                        //     ///
                        //     brandProduct(index),
                        //
                        //     ProductAvailability(product: productList[index]),
                        //     soldAndReturns(index),
                        //     productLabels(product: productList[index]),
                        //     buyDetail(index),
                        //
                        //     //Counters
                        //     counts(product:productList[index] ),
                        //     //Action
                        //     action(product:productList[index])
                        //   ],
                        // );
                      }
                      //If empty
                      else if (productList.isEmpty) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          Navigator.pop(context);
                        });
                        return const SizedBox();
                      }
                      //Pagination loading
                      else {
                        return paginationLoading();
                      }
                    });
              },
            );
          }
          return const SizedBox();
        });
  }

// endregion

  //region Product appbar
  Widget productAppBar({
    required String productReference,
    required String storeReference,
    required String storeHandle,
    required String? storeImage,
    required String? productImage,
    required Product product,
  }) {
    return GestureDetector(
      onTap: () {
        buyerViewProductBloc.goToStore(storeReference: storeReference);
      },
      child: Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.all(10),
        child: Row(
          children: [
            ///Un-comment
            CustomImageContainer(
              width: 30,
              height: 30,
              imageUrl: storeImage,
              imageType: CustomImageContainerType.store,
            ),
            horizontalSizedBox(10),
            Text(
              storeHandle.toLowerCase(),
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            Expanded(child: horizontalSizedBox(10)),
            CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  //If from add product then return
                  widget.isFromAddProduct
                      ? null
                      : buyerViewProductBloc.onTapDrawer(
                          productReference: productReference,
                          productImage: productImage,
                          storeReference: storeReference,
                          product: product);
                },
                child: SvgPicture.asset(AppImages.drawerIcon))
          ],
        ),
      ),
    );
  }

  //endregion

//region Product images
  Widget productImage({required Product product}) {
    int onScreenImageIndex = 0;
    return Stack(
      alignment: Alignment.center,
      children: [
        GestureDetector(
          onDoubleTap: () {
            buyerViewProductBloc.onTapHeart(
                product: product, isDoubleTapped: true);
          },
          child: PageView.builder(
              controller: buyerViewProductBloc.pageController,
              allowImplicitScrolling: true,
              onPageChanged: (index) {
                onScreenImageIndex = index;
                //print(index);
                buyerViewProductBloc.onChangeSlider(index);
              },
              itemCount: product.prodImages!.length,

              ///controller: buyerViewProductBloc.imageSliderPageCtrl,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: !widget.isFromAddProduct
                      ? () {
                          //print(onScreenImageIndex);
                          buyerViewProductBloc.goToBuyerProductImageScreen(
                              productImage: product.prodImages!,
                              imageIndex: onScreenImageIndex);
                        }
                      : null,
                  child: CommonMethods.networkOrLocal(
                          product.prodImages![index].productImage!)
                      ? extendedImage(
                          product.prodImages![index].productImage!,
                          fit: BoxFit.cover,
                          context,
                          500,
                          500,
                          cache: true,
                          customPlaceHolder: AppImages.productPlaceHolder)
                      : Image.file(
                          File(product.prodImages![index].productImage!),
                          fit: BoxFit.cover, // Adjust the fit as needed
                        ),
                );
              }),
        ),
        Positioned(
          bottom: 0,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: SizedBox(
              height: 6,
              child: StreamBuilder<int>(
                  stream: buyerViewProductBloc.sliderCtrl.stream,
                  initialData: 0,
                  builder: (context, snapshot) {
                    return ListView.builder(
                        itemCount: product.prodImages!.length,
                        scrollDirection: Axis.horizontal,
                        shrinkWrap: true,
                        itemBuilder: (Context, Index) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: SvgPicture.asset(
                              AppImages.dot,
                              height: 5.29,
                              width: 5.29,
                              color: snapshot.data == Index
                                  ? AppColors.darkGray
                                  : AppColors.darkStroke,
                            ),
                          );
                        });
                  }),
            ),
          ),
        ),
        const Positioned(
          top: 20,
          left: 15,
          right: 15,
          child: SizedBox(
            height: 35,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // productRatings(productIndex),
                // save(productIndex),
              ],
            ),
          ),
        )
      ],
    );
  }

//endregion

//region Product Rating
  Widget productRatings(int productIndex) {
    return Container();
    // return buyerViewProductBloc.productList[productIndex].rating==null?SizedBox():Container(
    //
    //   height: 30,
    //   width: 60,
    //   padding: EdgeInsets.symmetric(horizontal: 5,vertical: 5),
    //   decoration: const BoxDecoration(
    //     color: AppColors.white,
    //     borderRadius: BorderRadius.all(Radius.circular(15)),
    //   ),
    //   child: Row(
    //     mainAxisSize: MainAxisSize.min,
    //     mainAxisAlignment: MainAxisAlignment.center,
    //     crossAxisAlignment: CrossAxisAlignment.center,
    //     children: [
    //       Text("${buyerViewProductBloc.productList[productIndex].rating==null?0:buyerViewProductBloc.productList[productIndex].rating!} ",style: TextStyle(
    //         fontSize: 16,
    //         fontWeight: FontWeight.w700,
    //         color: AppColors.yellow,
    //         fontFamily: "LatoBold",
    //       ),),
    //       Text("| ${buyerViewProductBloc.productList[productIndex].countOfRatings==null?0:buyerViewProductBloc.productList[productIndex].countOfRatings!}" ,style: TextStyle(
    //         fontSize: 12,
    //         fontWeight: FontWeight.w600,
    //         color:AppColors.writingColor3,
    //         fontFamily: "LatoSemibold",
    //       ),)
    //     ],
    //   ),
    // );
  }

//endregion

//region Save
  Widget save(int productIndex) {
    return StreamBuilder<BuyerViewProductState>(
        stream: buyerViewProductBloc.saveCtrl.stream,
        builder: (context, snapshot) {
          return Visibility(
            ///Un-comment
            // visible: AppConstants.appData.storeReference==null,
            visible: false,
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                buyerViewProductBloc.saveUnSaveProduct(
                    buyerViewProductBloc.productList[productIndex]);
              },
              child: SvgPicture.asset(
                buyerViewProductBloc.productList[productIndex].saveStatus!
                    ? AppImages.bookmarkActive
                    : AppImages.bookmarkInactive,
                fit: BoxFit.fill,
              ),
            ),
          );
        });
  }

//endregion

  ///Modified
//region Brand name And Product name
  Widget brandProduct(int productIndex) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 10, bottom: 7),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                      buyerViewProductBloc.productList[productIndex].brandName!,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)),
                  Text(
                      buyerViewProductBloc
                          .productList[productIndex].productName!,
                      maxLines: 3,
                      style: AppTextStyle.contentText0(
                          textColor: AppColors.writingBlack0)),
                ],
              ),
            ),
            CupertinoButton(
                onPressed: () {
                  buyerViewProductBloc.goToProductDetail(
                      buyerViewProductBloc.productList[productIndex]);
                },
                child: SvgPicture.asset(
                  AppImages.arrow3,
                  height: 30,
                ))
          ],
        ),
      ),
    );
  }
//endregion

  ///New
//region Sold and returns
  Widget soldAndReturns(int productIndex) {
    return Visibility(
      visible:
          buyerViewProductBloc.productList[productIndex].ordersCount != 0 ||
              buyerViewProductBloc.productList[productIndex].returnCount! != 0,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 7),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Visibility(
                  visible: buyerViewProductBloc
                          .productList[productIndex].ordersCount! >
                      0,
                  child: Text(
                      CommonMethods.singularPluralText(
                          item: buyerViewProductBloc
                              .productList[productIndex].ordersCount!,
                          singular: "purchase",
                          plural: "purchases"),
                      style: AppTextStyle.smallText(
                          textColor: AppColors.brandBlack))),
              const SizedBox(
                width: 5,
              ),
              Visibility(
                  visible: buyerViewProductBloc
                          .productList[productIndex].returnCount! >
                      0,
                  child: Text(
                      CommonMethods.singularPluralText(
                          item: buyerViewProductBloc
                              .productList[productIndex].returnCount!,
                          singular: "return",
                          plural: "returns"),
                      style:
                          AppTextStyle.smallText(textColor: AppColors.orange))),

              // Text("₹ ${buyerViewProductBloc.productList[productIndex].sellingPrice!}", style: AppTextStyle.access0(textColor: AppColors.appBlack)),
              // horizontalSizedBox(17),
              // Text("₹ ${buyerViewProductBloc.productList[productIndex].mrpPrice!}",
              //     style: AppTextStyle.access0(textColor: AppColors.writingBlack1, isLineThrough: true)),
            ],
          ),
        ),
      ),
    );
  }

//endregion

  //region Product labels
  Widget productLabels({required Product product}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 7),
      child: ProductLabelDropdown(
        product: product,
      ),
    );
  }

  //endregion

//region Store Handle
  Widget storeHandle(int productIndex) {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: 27,
      child: InkWell(
        onTap: () {
          buyerViewProductBloc.goToStore(
              storeReference: buyerViewProductBloc
                  .productList[productIndex].storeReference!);
        },
        // padding: EdgeInsets.zero,
        child: Row(
          children: [
            Text(
              "Store:",
              style: AppTextStyle.contentHeading0(
                  textColor: AppColors.writingBlack1),
            ),
            horizontalSizedBox(5),
            InkWell(
              child: Text(
                buyerViewProductBloc.productList[productIndex].storehandle!
                    .toLowerCase(),
                style: AppTextStyle.contentHeading0(
                    textColor: AppColors.brandBlack),
              ),
            )
          ],
        ),
      ),
    );
  }

//endregion

//region Product Details
  Widget productDetails(int productIndex) {
    return InkWell(
      onTap: () {
        buyerViewProductBloc
            .goToProductDetail(buyerViewProductBloc.productList[productIndex]);
      },
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 9),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(AppStrings.productDetails.toLowerCase(),
                style: AppTextStyle.settingHeading1(
                    textColor: AppColors.appBlack)),
            SvgPicture.asset(
              AppImages.productDetailsRight,
              color: AppColors.writingColor2,
              fit: BoxFit.fill,
            )
          ],
        ),
      ),
    );
  }

//endregion

//region Buy Now and View detail
//   Widget buyDetail(int productIndex) {
//     // Retrieve the data from the StoreInfoModel
//     ShoppingCartQuantityDataModel shoppingCartQuantityDataModel = Provider.of<ShoppingCartQuantityDataModel>(context);
//
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 18),
//       child: Row(
//         mainAxisSize: MainAxisSize.max,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           //
//           Expanded(
//             child: Container(
//               decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10))),
//               child: StreamBuilder<bool>(
//                   stream: buyerViewProductBloc.buyNowAddCartCtrl.stream,
//                   initialData: false,
//                   builder: (context, snapshot) {
//                     return BuyButton(
//                       product: widget.productList[productIndex],
//                       isFromAddProduct: widget.isFromAddProduct,
//                       isGoToLatestVersion: false,  // Added missing parameter
//                     );
//
//                     ///If from add product
//                     // if (widget.isFromAddProduct) {
//                     //   return ProductCommonWidgets.buyButton(buttonName: AppStrings.buyNow, onTap: () {});
//                     // }
//                     //
//                     // ///If is accept is false and user view
//                     // if (!widget.productList[productIndex].configReceiveOrders! && AppConstants.appData.isUserView!) {
//                     //   return CupertinoButton(
//                     //     borderRadius: BorderRadius.circular(150),
//                     //     // disabledColor: AppColors.writingBlack1.withOpacity(0.4),
//                     //     color: AppColors.brandGreen,
//                     //     padding: EdgeInsets.zero,
//                     //     onPressed: widget.productList[productIndex].configReceiveOrders!
//                     //         ? () {
//                     //             buyerViewProductBloc.addToCart(
//                     //                 storeId: buyerViewProductBloc.productList[productIndex].storeid!,
//                     //                 productReference: buyerViewProductBloc.productList[productIndex].productReference!,
//                     //                 storeRference: buyerViewProductBloc.productList[productIndex].storeReference!);
//                     //             // buyerViewProductBloc.onTapBuyAddCart(buyerViewProductBloc.storeProductResponse.data.[]);
//                     //           }
//                     //         : null,
//                     //     child: Container(
//                     //       decoration: const BoxDecoration(
//                     //         borderRadius: BorderRadius.all(Radius.circular(150)),
//                     //       ),
//                     //       padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
//                     //       child: Text(AppStrings.addToCart, style: AppTextStyle.button2Bold(textColor: AppColors.appWhite)),
//                     //     ),
//                     //   );
//                     // }
//                     //
//                     // ///If seller viewing own product
//                     // if (AppConstants.appData.storeReference == widget.productList.first.storeReference) {
//                     //   return ProductCommonWidgets.buyButton(
//                     //       buttonName: AppStrings.updateStock,
//                     //       onTap: () {
//                     //         buyerViewProductBloc.onTapUpdateStock(product: widget.productList[productIndex]);
//                     //       });
//                     // }
//                     //
//                     // ///If Store view other store
//                     // if (AppConstants.appData.isStoreView! && AppConstants.appData.storeReference! != widget.productList.first.storeReference) {
//                     //   return ProductCommonWidgets.buyButton(
//                     //       buttonName: AppStrings.switchToBuyer,
//                     //       onTap: () {
//                     //         buyerViewProductBloc.switchToBuyer(productReference: widget.productList[productIndex].productReference!);
//                     //       });
//                     // }
//                     //
//                     // ///If same product already in card
//                     // if (shoppingCartQuantityDataModel.productReferenceList.contains(widget.productList[productIndex].productReference)) {
//                     //   return ProductCommonWidgets.buyButton(
//                     //       buttonName: AppStrings.goToCart,
//                     //       textColor: AppColors.appWhite,
//                     //       onTap: () {
//                     //         buyerViewProductBloc.gotoShoppingCart();
//                     //       });
//
//                     //   return CupertinoButton(
//                     //     borderRadius: BorderRadius.circular(150),
//                     //     color: AppColors.brandGreen,
//                     //     padding: EdgeInsets.zero,
//                     //     onPressed: widget.productList[productIndex].inStock != 0
//                     //         ? () {
//                     //             buyerViewProductBloc.gotoShoppingCart();
//
//                     //             // buyerViewProductBloc.onTapBuyAddCart(buyerViewProductBloc.storeProductResponse.data.[]);
//                     //           }
//                     //         : null,
//                     //     child: Center(
//                     //       child: Text(
//                     //         "Go to cart",
//                     //         style: AppTextStyle.button2Bold(textColor: AppColors.appWhite),
//                     //       ),
//                     //     ),
//                     //   );
//                     // }
//                     //
//                     // ///If cat is not empty
//                     // if (shoppingCartQuantityDataModel.productReferenceList.isNotEmpty) {
//                     //   return CupertinoButton(
//                     //     borderRadius: BorderRadius.circular(150),
//                     //     color: AppColors.brandGreen,
//                     //     padding: EdgeInsets.zero,
//                     //     onPressed: widget.productList[productIndex].inStock != 0
//                     //         ? () {
//                     //             buyerViewProductBloc.addToCart(
//                     //                 storeId: buyerViewProductBloc.productList[productIndex].storeid!,
//                     //                 productReference: buyerViewProductBloc.productList[productIndex].productReference!,
//                     //                 storeRference: buyerViewProductBloc.productList[productIndex].storeReference!);
//                     //             // buyerViewProductBloc.onTapBuyAddCart(buyerViewProductBloc.storeProductResponse.data.[]);
//                     //           }
//                     //         : null,
//                     //     child: Container(
//                     //       decoration: const BoxDecoration(
//                     //         borderRadius: BorderRadius.all(Radius.circular(150)),
//                     //       ),
//                     //       padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
//                     //       child: Text(AppStrings.addToCart, style: AppTextStyle.button2Bold(textColor: AppColors.appWhite)),
//                     //     ),
//                     //   );
//                     // }
//                     //
//                     // ///Buy
//                     // return CupertinoButton(
//                     //     borderRadius: BorderRadius.circular(150),
//                     //     color: AppColors.brandGreen,
//                     //     padding: EdgeInsets.zero,
//                     //     onPressed: widget.productList[productIndex].inStock != 0
//                     //         ? () {
//                     //             buyerViewProductBloc.addToCart(
//                     //                 storeId: buyerViewProductBloc.productList[productIndex].storeid!,
//                     //                 productReference: buyerViewProductBloc.productList[productIndex].productReference!,
//                     //                 goToCart: true,
//                     //                 storeRference: buyerViewProductBloc.productList[productIndex].storeReference!);
//                     //             // buyerViewProductBloc.onTapBuyAddCart(buyerViewProductBloc.storeProductResponse.data.[]);
//                     //           }
//                     //         : null,
//                     //     child: Container(
//                     //       decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(150))),
//                     //       padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
//                     //       child: Text(AppStrings.buyNow, style: AppTextStyle.access0(textColor: AppColors.appWhite)),
//                     //     ));
//                   }),
//             ),
//           ),
//           //Make it visible only if product belongs to logged in store
//           //Is not from add product and store reference has to be same as logged in store reference
//           Visibility(
//             visible: !widget.isFromAddProduct && widget.productList.first.storeReference == AppConstants.appData.storeReference,
//             child: CupertinoButton(
//               padding: EdgeInsets.zero,
//                 onPressed: (){
//                 buyerViewProductBloc.onTapEditDetails(productReference:widget.productList[productIndex].productReference!,storeId:widget.productList[productIndex].storeid!  );
//                 },
//                 child: const Icon(Icons.edit,color: AppColors.appBlack,)),
//           )
//           // horizontalSizedBox(10),
//           //Edit
//           // ProductCommonWidgets.buyButton(
//           //     buttonColor: AppColors.appWhite,
//           //     buttonName: widget.isFromAddProduct
//           //         ? AppStrings.viewDetails
//           //         : AppConstants.appData.storeReference == buyerViewProductBloc.productList[productIndex].storeReference
//           //             ? AppStrings.editDetails
//           //             : AppStrings.viewDetails,
//           //     borderColor: AppColors.darkGray,
//           //     textColor: AppColors.appBlack,
//           //     onTap: () {
//           //
//           //       //If from add product
//           //       if(widget.isFromAddProduct){
//           //         return buyerViewProductBloc.goToProductDetail(buyerViewProductBloc.productList[productIndex]);
//           //       }
//           //       //If from admin
//           //       if(AppConstants.appData.storeReference == buyerViewProductBloc.productList[productIndex].storeReference){
//           //         return buyerViewProductBloc.onTapEditDetails(
//           //             productReference: buyerViewProductBloc.productList[productIndex].productReference!,
//           //             storeId: buyerViewProductBloc.productList[productIndex].storeid!);
//           //       }
//           //       //Else go to product detail
//           //       else{
//           //         return buyerViewProductBloc.goToProductDetail(buyerViewProductBloc.productList[productIndex]);
//           //       }
//           //     }),
//           // Expanded(child: horizontalSizedBox(50)),
//           // CupertinoButton(
//           //     padding: EdgeInsets.zero,
//           //     onPressed: !widget.isFromAddProduct
//           //         ? () {
//           //             buyerViewProductBloc.onTapShare(
//           //                 imageUrl: buyerViewProductBloc.productList[productIndex].prodImages!.first.productImage,
//           //                 productReference: buyerViewProductBloc.productList[productIndex].productReference!);
//           //           }
//           //         : null,
//           //     child: SvgPicture.asset(
//           //       AppImages.shareIcon,
//           //       color: AppColors.brandGreen,
//           //     ))
//         ],
//       ),
//     );
//   }

//endregion

//region Add Comment
  Widget addComment(int productIndex) {
    return InkWell(
      onTap: !widget.isFromAddProduct
          ? () {
              buyerViewProductBloc.addComment(
                  storeReference: buyerViewProductBloc
                      .productList[productIndex].storeReference!,
                  productRef: buyerViewProductBloc
                      .productList[productIndex].productReference!,
                  productId:
                      buyerViewProductBloc.productList[productIndex].productid!,
                  isWriteComment: true);
            }
          : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        // height: 43,
        decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              AppImages.emoji,
              fit: BoxFit.contain,
            ),
            Expanded(
              child: TextFormField(
                enabled: false,

                readOnly: true,

                maxLines: 1,
                //controller: addProductBloc.hashTagsTextCtrl,

                decoration: InputDecoration(
                  // prefixIcon: SvgPicture.asset(AppImages.emoji,fit:BoxFit.contain,),
                  filled: true,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  fillColor: AppColors.textFieldFill1,
                  isDense: true,
                  hintText: AppStrings.commentHint,
                  hintStyle: AppTextStyle.contentText0(
                      textColor: AppColors.writingBlack1),
                  border: InputBorder.none,
                  // focusedBorder: OutlineInputBorder(
                  //     borderRadius: BorderRadius.circular(22),
                  //     borderSide: BorderSide.none
                  //
                  // ),
                  // enabledBorder: OutlineInputBorder(
                  //
                  //     borderRadius: BorderRadius.circular(22),
                  //     borderSide: BorderSide.none
                  // ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
//endregion

  //region Counts
  Widget counts({required Product product}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
      child: Row(
        children: [
          Visibility(
            visible: product.likeCount != 0,
            child: InkWell(
              onTap: () {
                buyerViewProductBloc.goToLikedUsedOrStoreScreen(
                    reference: product.productReference!);
              },
              child: Container(
                  margin: const EdgeInsets.only(right: 5),
                  child: Text(
                    "${product.likeCount} ${product.likeCount == 1 ? "like" : "likes"}",
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack0),
                  )),
            ),
          ),
          Visibility(
            visible: product.commentCount != 0,
            child: Container(
                margin: const EdgeInsets.only(right: 5),
                child: Text(
                    "${product.commentCount} ${product.commentCount == 1 ? "comment" : "comments"}",
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack0))),
          ),
          // const Expanded(child: SizedBox()),
          // Container(margin: EdgeInsets.only(right: 5), child: Text("710 reposts", style: AppTextStyle.smallText(textColor: AppColors.writingBlack1))),
          // Container(margin: EdgeInsets.only(right: 5), child: Text("50 shares", style: AppTextStyle.smallText(textColor: AppColors.writingBlack1))),
          //
        ],
      ),
    );
  }
  //endregion

  //region Action
  Widget action({required Product product}) {
    return IgnorePointer(
      ignoring: widget.isFromAddProduct,
      child: Container(
        margin: const EdgeInsets.only(bottom: 10),
        padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 15),
        child: Row(
          children: [
            //Like
            Container(
              margin: const EdgeInsets.only(right: 10),
              height: 26,
              width: 26,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  buyerViewProductBloc.onTapHeart(product: product);
                  // widget.onTapHeart();
                },
                child: SvgPicture.asset(
                  fit: BoxFit.fill,
                  product.likeStatus!
                      ? AppImages.postLike
                      : AppImages.postDisLike,
                  color:
                      product.likeStatus! ? AppColors.red : AppColors.appBlack,
                ),
              ),
            ),

            //Comment
            Container(
              margin: const EdgeInsets.only(right: 10),
              height: 26,
              width: 26,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  buyerViewProductBloc.viewComment(
                      productRef: product.productReference!);
                },
                child: SvgPicture.asset(AppImages.postComment,
                    color: AppColors.appBlack),
              ),
            ),

            //Save
            AppToolTip(
              message: AppStrings.thisFeatureIsCommingSoon,
              toolTipWidget: Opacity(
                opacity: 0.2,
                child: SizedBox(
                    height: 26,
                    width: 26,
                    child: SvgPicture.asset(AppImages.savePost,
                        color: AppColors.appBlack)),
              ),
            ),
            const Expanded(child: SizedBox()),
            //Repost
            AppToolTip(
              message: AppStrings.thisFeatureIsCommingSoon,
              toolTipWidget: Opacity(
                opacity: 0.2,
                child: SizedBox(
                  height: 26,
                  width: 26,
                  child: SvgPicture.asset(AppImages.repost,
                      color: AppColors.appBlack),
                ),
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            //Share
            SizedBox(
              height: 26,
              width: 26,
              child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: !widget.isFromAddProduct
                      ? () {
                          buyerViewProductBloc.onTapShare(
                              imageUrl: product.prodImages!.isEmpty
                                  ? null
                                  : product.prodImages!.first.productImage,
                              productReference: product.productReference!);
                        }
                      : null,
                  child: SvgPicture.asset(
                    AppImages.sharePost,
                    color: AppColors.appBlack,
                  )),
            ),
          ],
        ),
      ),
    );
  }
//endregion

//region Pagination loading
  Widget paginationLoading() {
    return StreamBuilder<BuyerViewPaginationState>(
        stream: buyerViewProductBloc.buyerViewProductPagination
            .buyerViewProductsPaginationStateCtrl.stream,
        initialData: BuyerViewPaginationState.Loading,
        builder: (context, snapshot) {
          //Empty if current state and the post list is smaller then 10
          if (buyerViewProductBloc.buyerViewProductPagination
                      .currentBuyerViewProductsPaginationState ==
                  BuyerViewPaginationState.Empty ||
              buyerViewProductBloc.productList.length < 2) {
            return const SizedBox();
          }
          //Loading and
          if (snapshot.data == BuyerViewPaginationState.Loading) {
            return VisibilityDetector(
                key: UniqueKey(),
                onVisibilityChanged: (visibilityInfo) {
                  var visiblePercentage = visibilityInfo.visibleFraction * 100;
                  if (visiblePercentage == 100) {
                    buyerViewProductBloc.buyerViewProductPagination
                        .onVisibleLoading();
                  }
                },
                child: AppCommonWidgets.appCircularProgress(
                    isPaginationProgress: true));
          }
          return const SizedBox();
        });
  }
//endregion
}
