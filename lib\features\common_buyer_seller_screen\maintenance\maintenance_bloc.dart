import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/maintenance_response/maintenance_response.dart';
import 'package:swadesic/services/maintenance_services/maintenance_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum MaintenanceState { Loading, Success, Failed, Empty }

class MaintenanceBloc {
  // region Common Methods
  BuildContext context;
  late MaintenanceService maintenanceService;
  late MaintenanceResponse maintenanceResponse;
  // endregion
  //region Controller
  final maintenanceStateCtrl = StreamController<MaintenanceState>.broadcast();

  //endregion

  // region | Constructor |
  MaintenanceBloc(this.context);

  // endregion

  // region Init
  init() {
    maintenanceService = MaintenanceService();
    getMaintenanceDetailApiCall();
  }
  // endregion


  //region Get Maintenance Detail Api Call
  getMaintenanceDetailApiCall() async {
    try {
      //Loading
      maintenanceStateCtrl.sink.add(MaintenanceState.Loading);
      //Api call
      maintenanceResponse = await maintenanceService.getMaintenanceInfo();
      //Success
      maintenanceStateCtrl.sink.add(MaintenanceState.Success);

    } on ApiErrorResponseMessage {
      maintenanceStateCtrl.sink.add(MaintenanceState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      maintenanceStateCtrl.sink.add(MaintenanceState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
  //endregion


//region Dispose
void dispose(){

}
//endregion


}
