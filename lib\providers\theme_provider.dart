import 'package:flutter/material.dart';
import 'package:swadesic/services/theme_service/theme_service.dart';
import 'package:swadesic/util/theme_mode.dart';

class ThemeProvider with ChangeNotifier {
  AppThemeMode _themeMode = AppThemeMode.system;
  final ThemeService _themeService = ThemeService();

  AppThemeMode get themeMode => _themeMode;

  /// Initialize theme from storage
  Future<void> initializeTheme() async {
    _themeMode = await _themeService.getThemeMode();
    notifyListeners();
  }

  /// Set theme mode and save to storage
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    if (_themeMode != themeMode) {
      _themeMode = themeMode;
      await _themeService.setThemeMode(themeMode);
      notifyListeners();
    }
  }

  /// Get the actual Flutter ThemeMode
  ThemeMode getThemeMode(BuildContext context) {
    return _themeService.getActualThemeMode(_themeMode, context);
  }

  /// Check if current theme is dark
  bool isDarkMode(BuildContext context) {
    return _themeService.isDarkMode(context, _themeMode);
  }
}
