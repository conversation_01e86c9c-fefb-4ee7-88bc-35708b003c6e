import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customer_card/find_your_customer_card_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_bloc.dart';
import 'package:swadesic/model/find_your_customers_response/get_contact_user_and_store_info_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class FindYourCustomerUserScreen extends StatefulWidget {
  final ContactUserAndStoreInfo contactUserAndStoreInfo;
  final FindYourCustomersBloc findYourCustomersBloc;
  final String reference;
  const FindYourCustomerUserScreen({super.key, required this.contactUserAndStoreInfo, required this.findYourCustomersBloc, required this.reference});

  @override
  State<FindYourCustomerUserScreen> createState() => _FindYourCustomerUserScreenState();
}

class _FindYourCustomerUserScreenState extends State<FindYourCustomerUserScreen> {

  //Bloc
  late FindYourCustomerCardBloc findYourCustomerCardBloc;



  //region Init
  @override
  void initState() {
    super.initState();
    findYourCustomerCardBloc = FindYourCustomerCardBloc(context,widget.contactUserAndStoreInfo,widget.findYourCustomersBloc,widget.reference);
    findYourCustomerCardBloc.init();
  }
  //endregion


  //region Did update widget
  @override
  void didUpdateWidget(covariant FindYourCustomerUserScreen oldWidget) {
    findYourCustomerCardBloc = FindYourCustomerCardBloc(context,widget.contactUserAndStoreInfo,widget.findYourCustomersBloc,widget.reference);
    findYourCustomerCardBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    findYourCustomerCardBloc.dispose();
    super.dispose();
  }
  //endregion


  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
Widget body(){
    return userCard();
}
//endregion

//region User card
Widget userCard(){
    return StreamBuilder<FindYourCustomerCardState>(
      stream: findYourCustomerCardBloc.findYourCustomerCardStateCtrl.stream,
      builder: (context, snapshot) {
        return LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints){
            ///Image radius
            double imageRadius = findYourCustomerCardBloc.contactUserAndStoreInfo.entityType == EntityType.STORE.name?35 * 0.4130:40.0;

            return Container(
              margin: const EdgeInsets.only(top: 10),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  //Image
                  InkWell(
                    onTap: (){
                      findYourCustomerCardBloc.onTapRegisterUserAndStoreIcon(contactUserAndStoreInfo:findYourCustomerCardBloc.contactUserAndStoreInfo);
                    },
                    child: Container(
                      margin: const EdgeInsets.only(right: 5),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(imageRadius),
                        child: SizedBox(
                          height: 35,
                          width: 35,
                          child: extendedImage(
                              findYourCustomerCardBloc.contactUserAndStoreInfo.icon, customPlaceHolder: AppImages.userPlaceHolder, context, 200, 200,fit: BoxFit.cover),
                        ),
                      ),
                    ),
                  ),


                  //Name and number
                  //Put the phone number if handle is null
                  Expanded(

                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("${findYourCustomerCardBloc.contactUserAndStoreInfo.name}",
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
                        Text(findYourCustomerCardBloc.contactUserAndStoreInfo.handle!= null?"@${findYourCustomerCardBloc.contactUserAndStoreInfo.handle}":"${findYourCustomerCardBloc.contactUserAndStoreInfo.phonenumber}",
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),)
                      ],
                    ),
                  ),
                  const SizedBox(width: 20,),
                  //Button
                  StreamBuilder<FindYourCustomerCardState>(
                    stream: findYourCustomerCardBloc.findYourCustomerCardStateCtrl.stream,
                    initialData: FindYourCustomerCardState.Success,
                    builder: (context, snapshot) {
                      return SizedBox(
                        // width: constraints.maxWidth * 0.4,
                        width: CommonMethods.textWidth(context: context,
                          text: "Support back",
                          textStyle:AppTextStyle.access0(textColor: AppColors.appWhite),),
                        child: CupertinoButton(
                          padding: EdgeInsets.zero,
                          onPressed: snapshot.data != FindYourCustomerCardState.Loading?(){
                            findYourCustomerCardBloc.onTapAction(contactUserAndStoreInfo:findYourCustomerCardBloc.contactUserAndStoreInfo );
                          }:null,
                          child: Container(
                            alignment: Alignment.center,
                            width: double.infinity,
                            decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(100)
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 5),
                            child: Text("${findYourCustomerCardBloc.contactUserAndStoreInfo.followStatus}",
                              overflow:TextOverflow.ellipsis ,
                              style: AppTextStyle.access0(textColor: Colors.green),),
                          ),
                        ),
                      );
                    }
                  ),



                ],
              ),
            );
          },

        );
      }
    );
}
//endregion

}
