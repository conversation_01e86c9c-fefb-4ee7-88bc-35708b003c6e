import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_product_detail/buyer_product_details_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_product_detail/product_detail_card/product_detail_card.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_product_detail/product_detail_common_widget.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_availability/product_availability.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_label_dropdown/product_label_dropdown.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_refund_responsibility_card/product_refund_responsibility_card.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart'
    as products;
import 'package:swadesic/services/pickup_location_service/pickup_location_service.dart';
import 'package:swadesic/model/seller_return_warranty_response/address_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/pickup_locations_bottom_sheet.dart';

// region Buyer Product Details
class BuyerProductDetailsScreen extends StatefulWidget {
  final products.Product product;
  final String? orderNumber;
  final bool isFromAddProduct;

  // final String? productDesc;
  // final String url;
  // final String productVersion;
  // final String productCategory;
  // final String productReference;
  // final String updatedDate;
  // final String createdDate;
  // final String storeReference;
  // final int returnPeriod;
  // final bool isAskSellerVisible ;
  // final int? deliveryDays;
  // final List<String>? returnReasons;
  // final String? returnCostPaidBy;
  // final String? logisticPartnerName;
  // final String? deliveryPartner;
  // final String? deliveryFee;
  //
  // final String? returnPickupBy;
  const BuyerProductDetailsScreen({
    Key? key,
    // this.productDesc,
    // required this.url,
    // required this.productVersion,
    // required this.productCategory,
    // required this.productReference,
    // required this.updatedDate,
    // required this.storeReference,
    // required this.createdDate,
    // required this.returnPeriod,
    // this.isAskSellerVisible = true,
    // this.deliveryDays,
    // this.returnReasons,
    // this.returnCostPaidBy,
    // this.logisticPartnerName,
    // this.deliveryPartner,
    // this.deliveryFee,
    // this.returnPickupBy,
    required this.product,
    this.orderNumber,
    this.isFromAddProduct = false,
  }) : super(key: key);

  @override
  _BuyerProductDetailsScreenState createState() =>
      _BuyerProductDetailsScreenState();
}
// endregion

class _BuyerProductDetailsScreenState extends State<BuyerProductDetailsScreen> {
  late BuyerProductDetailBloc bloc;
  List<Data>? pickupLocations;
  final PickupLocationService _pickupLocationService = PickupLocationService();

  @override
  void initState() {
    super.initState();
    bloc = BuyerProductDetailBloc(context, widget.product, widget.orderNumber);
    bloc.init();
    _loadPickupLocations();
  }

  Future<void> _loadPickupLocations() async {
    if (widget.product.productReference != null) {
      try {
        final response = await _pickupLocationService
            .getPickupLocationsForProduct(widget.product.productReference!);
        setState(() {
          pickupLocations = response.data;
        });
      } catch (e) {
        debugPrint('Error loading pickup locations: $e');
      }
    }
  }

  // region Bloc
  // late BuyerProductDetailBloc buyerProductDetailsBloc;

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(child: body()),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.productDetails,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  // region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: bloc.productDetailLoadingCtrl.stream,
        initialData: true,
        builder: (context, snapshot) {
          //True
          if (snapshot.data!) {
            return AppCommonWidgets.appCircularProgress();
          }
          return SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    aboutProducts(),
                    category(),
                    Container(
                        margin: const EdgeInsets.only(top: 20),
                        child: ProductLabelDropdown(product: widget.product)),
                    availableIn(),
                    moreDetails(),
                    productCode(),
                    // Only show delivery, return, and refund section if not in-store pickup
                    if (widget.product.fulfillmentOptions != "IN_STORE_PICKUP")
                      deliveryReturnRefundSection(),
                    productVersion(),
                    productAddedOn(),
                    askSellerButton(),
                    verticalSizedBox(50)
                    // AppCommonWidgets.bottomListSpace(context: context),
                  ],
                )
              ],
            ),
          );
        });
  }

// endregion

  //region About Product heading and Abouts
  Widget aboutProducts() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          ProductDetailCommonWidget.title(title: AppStrings.aboutTheProduct),
          RichText(
            textScaleFactor: MediaQuery.textScaleFactorOf(
                AppConstants.globalNavigator.currentContext!),
            text: TextSpan(
                children: CommonMethods.makeClickableText(
                    sentence: bloc.product.productDescription ?? "No description available",
                    context: context)),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Category
  Widget category() {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            ProductDetailCommonWidget.title(title: "Category"),
            Text(
              bloc.product.productCategory ?? "Category not available",
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ],
        ),
      ),
    );
  }

  //endregion

    //region Category
  Widget productCode() {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            ProductDetailCommonWidget.title(title: "Product Code"),
            bloc.product.productCode?.isNotEmpty == true
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        onLongPress: () {
                          CommonMethods.copyText(context, bloc.product.productCode!);
                        },
                        child: Text(
                          bloc.product.productCode!,
                          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                        ),
                      ),
                      const SizedBox(width: 10),
                      GestureDetector(
                        onTap: () {
                          CommonMethods.copyText(context, bloc.product.productCode!);
                        },
                        child: SvgPicture.asset(
                          AppImages.copyIconFilled,
                          height: 20,
                          width: 20,
                          color: AppColors.brandBlack,
                        ),
                      ),
                    ],
                  )
                : Text(
                    "Not available",
                    style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                  ),
          ],
        ),
      ),
    );
  }

  //region Available in
  Widget availableIn() {
    //Product available
    // return Visibility(
    //   visible: !widget.isFromAddProduct,
    //   child: Container(
    //       margin: const EdgeInsets.only(top: 20,bottom: 20),
    //       child: ProductAvailability(product: widget.product)),
    // );
    return Visibility(
      visible: !widget.isFromAddProduct,
      child: Container(
        margin: const EdgeInsets.only(top: 20),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              ProductDetailCommonWidget.title(title: AppStrings.availability),
              Row(
                children: [
                  if (widget.product.fulfillmentOptions != "IN_STORE_PICKUP") ...[
                    ProductAvailability(
                      product: widget.product,
                      isUnderLineText: true,
                    ),
                    const SizedBox(width: 16),
                  ],
                  if (pickupLocations != null && pickupLocations!.isNotEmpty)
                    InkWell(
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.white,
                          shape: const RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.vertical(top: Radius.circular(20)),
                          ),
                          builder: (context) => PickupLocationsBottomSheet(
                            locations: pickupLocations!,
                          ),
                        );
                      },
                      child: Text(
                        "View pickup locations",
                        style: AppTextStyle.contentText0(
                          textColor: AppColors.appBlack,
                          isUnderline: true,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  //endregion

  //region More Details
  Widget moreDetails() {
    return Visibility(
      visible: bloc.product.promotionLink != null &&
          bloc.product.promotionLink!.isNotEmpty,
      child: Container(
        margin: const EdgeInsets.only(top: 20),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              ProductDetailCommonWidget.title(title: AppStrings.moreDetails),
              InkWell(
                onTap: () {
                  CommonMethods.openAppWebView(
                      webUrl: bloc.product.promotionLink!, context: context);
                },
                child: Text(
                  bloc.product.promotionLink!,
                  textAlign: TextAlign.left,
                  style: AppTextStyle.contentText0(
                      textColor: AppColors.brandBlack, isUnderline: true),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  //endregion

  //region Combined Delivery, Return Policy, and Refund Section
  Widget deliveryReturnRefundSection() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Product policies
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "${AppStrings.buyOnSwadesicConsiderations}:",
              style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack),
            ),
          ),
          const SizedBox(height: 5),

          if (widget.product.productStatusMessage != null &&
              widget.product.productStatusMessage!.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "${widget.product.productStatusMessage}",
                  style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
                ),
              ),
            ),
          ],
          // const SizedBox(height: 10),
          // Delivery by card
          _buildDeliveryCard(),
          // Return policy
          _buildReturnPolicyCard(),
          // Refund if cancellation
          _buildRefundCard(),
        ],
      ),
    );
  }

  Widget _buildDeliveryCard() {
    if (widget.isFromAddProduct) {
      return Container(
        margin: const EdgeInsets.only(top: 5),
        child: ProductDetailCard(
            isArrowVisible: false,
            title: AppStrings.deliveryBy,
            titleValue: AppStrings.publishYpurProductToSeeDetails,
            expandedWidget: const SizedBox()),
      );
    } else {
      DateTime data =
          DateTime.now().add(Duration(days: bloc.product.deliveryBy ?? 0));
      String deliveryBy = DateFormat('d\'th\' MMMM, y').format(data);
      return Container(
        margin: const EdgeInsets.only(top: 5),
        child: ProductDetailCard(
          isArrowVisible: true,
          title: AppStrings.deliveryBy,
          customTitleValue: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                deliveryBy,
                style:
                    AppTextStyle.heading3Regular(textColor: AppColors.appBlack),
              ),

              horizontalSizedBox(10),
              //If delivery fee is null then make it visible
              Visibility(
                visible: bloc.product.deliveryFee == null,
                child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: AppColors.brandBlack)),
                    child: Text(
                      AppStrings.freeDelivery,
                      style: AppTextStyle.contentText0(
                          textColor: AppColors.appBlack),
                    )),
              )
            ],
          ),
          expandedWidget: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              verticalSizedBox(10),
              //Delivery fee
              Visibility(
                visible: bloc.product.deliveryFee != null,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ProductDetailCommonWidget.title(
                        title: AppStrings.deliveryFee),
                    ProductDetailCommonWidget.subTitle(
                        subtitle: "${bloc.product.deliveryFee}",
                        bottomMargin: 10),
                  ],
                ),
              ),

              ProductDetailCommonWidget.title(
                  title: AppStrings.deliveryPartner),
              ProductDetailCommonWidget.subTitle(
                  subtitle: CommonMethods.capitalFirstLetter(
                      value: bloc.product.logisticPartnerName == null
                          ? "Seller"
                          : "Logistics partner - ${bloc.product.logisticPartnerName}"),
                  bottomMargin: 0),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildReturnPolicyCard() {
    if (widget.isFromAddProduct) {
      return ProductDetailCard(
          isArrowVisible: false,
          title: AppStrings.returnPolicy,
          titleValue: AppStrings.publishYpurProductToSeeDetails,
          expandedWidget: const SizedBox());
    } else {
      List<String> modifiedStrings = bloc.product.returnConditions != null
          ? bloc.product.returnConditions!.map((str) => '  ●  $str').toList()
          : <String>[];
      String finalReturnCondition = modifiedStrings.join("\n");

      return ProductDetailCard(
        isArrowVisible: (bloc.product.returnPeriod ?? 0) != 0,
        title: AppStrings.returnPolicy,
        titleValue: (bloc.product.returnPeriod ?? 0) == 0
            ? AppStrings.notAccepted
            : "Accepted in ${CommonMethods.singularPluralText(item: bloc.product.returnPeriod ?? 0, plural: "days", singular: "day")} from Delivery day.",
        expandedWidget: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            verticalSizedBox(10),
            Visibility(
                visible: finalReturnCondition.isNotEmpty,
                child: ProductDetailCommonWidget.title(
                    title: AppStrings.returnCondition)),
            Visibility(
                visible: finalReturnCondition.isNotEmpty,
                child: ProductDetailCommonWidget.subTitle(
                    subtitle: finalReturnCondition, bottomMargin: 10)),
            // Only show return pickup by if it's available
            if (bloc.product.returnPickupBy != null &&
                bloc.product.returnPickupBy!.isNotEmpty)
              ProductDetailCommonWidget.title(title: AppStrings.returnPickupBy),
            if (bloc.product.returnPickupBy != null &&
                bloc.product.returnPickupBy!.isNotEmpty)
              ProductDetailCommonWidget.subTitle(
                  subtitle: AppStrings.sellerOrSwadesicShipping,
                  bottomMargin: 0),
          ],
        ),
      );
    }
  }

  //endregion

  Widget _buildRefundCard() {
    //If not from add product
    if (!widget.isFromAddProduct && widget.product.refundResponsibilityList != null) {
      return ProductRefundResponsibilityCard(
        refundResponsibilityList: widget.product.refundResponsibilityList!,
      );
    } else {
      return const SizedBox();
    }
  }

  //endregion

  //region Product version
  Widget productVersion() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          ProductDetailCommonWidget.title(title: AppStrings.productVersion),
          ProductDetailCommonWidget.subTitle(
            subtitle: bloc.product.productVersion != null && bloc.product.updatedDate != null
                ? "version : ${bloc.product.productVersion}, updated on ${CommonMethods.dateTimeAmPm(date: bloc.product.updatedDate!)[1]} ${CommonMethods.dateTimeAmPm(date: bloc.product.updatedDate!)[2]}"
                : "version : ${bloc.product.productVersion ?? "1.0.0"}",
          ),
        ],
      ),
    );
  }

  //endregion

  //region Product added on
  Widget productAddedOn() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          ProductDetailCommonWidget.title(
            title: AppStrings.productAddedOn,
          ),
          ProductDetailCommonWidget.subTitle(
            subtitle: bloc.product.createdDate != null
                ? CommonMethods.dateTimeToStringMonth(date: bloc.product.createdDate!)
                : "Date not available",
          ),
        ],
      ),
    );
  }

//endregion

  //region Ask seller button
  Widget askSellerButton() {
    return StreamBuilder<bool>(
        stream: bloc.refreshCtrl.stream,
        builder: (context, snapshot) {
          return Visibility(
            visible: true,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ProductDetailCommonWidget.title(
                    title: AppStrings.haveQuestions,
                  ),
                  InkWell(
                    onTap: () {
                      bloc.getSingleStoreInfo();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 11),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(150),
                          color: AppColors.brandBlack),
                      child: Text(
                        AppStrings.askSellerAbout,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style:
                            AppTextStyle.access0(textColor: AppColors.appWhite),
                      ),
                      //     child:appText(
                      //   AppStrings.askSellerAbout,
                      //       color: AppColors.white,
                      //       fontFamily: AppConstants.rRegular,
                      //       fontWeight: FontWeight.w400,
                      //       fontSize: 14
                      // ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }
//endregion
}
