import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_document/edit_document/edit_document_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_document/seller_document_bloc.dart';
import 'package:swadesic/model/seller_trust_center_response/get_seller_trust_center_document_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Edit document
class EditDocument extends StatefulWidget {
  final Document document;
  final SellerDocumentBloc sellerDocumentBloc;
  final String storeReference;
  final BuildContext sellerDocumentContext;
  const EditDocument(
      {Key? key,
      required this.document,
      required this.sellerDocumentBloc,
      required this.storeReference,
      required this.sellerDocumentContext})
      : super(key: key);

  @override
  State<EditDocument> createState() => _EditDocumentState();
}
//endregion

class _EditDocumentState extends State<EditDocument> {
  //region Bloc
  late EditDocumentBloc editDocumentBloc;
  //endregion

  //region Init
  @override
  void initState() {
    editDocumentBloc = EditDocumentBloc(
        context,
        widget.document,
        widget.sellerDocumentBloc,
        widget.storeReference,
        widget.sellerDocumentContext);
    editDocumentBloc.init();
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: editDocumentBloc.editDocumentRefreshCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                AppImages.documentPlaceHolder,
                fit: BoxFit.cover,
              ),

              verticalSizedBox(20),

              Row(
                children: [
                  Text(AppStrings.addDocumentName,
                      style: TextStyle(
                          fontFamily: "LatoSemibold",
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          color: AppColors.writingColor2)),
                  Expanded(child: horizontalSizedBox(10))
                ],
              ),
              verticalSizedBox(10),
              colorFilledTextField(
                  context: context,
                  textFieldCtrl: editDocumentBloc.documentNameTextCtrl,
                  hintText: AppStrings.documentHintText,
                  textFieldMaxLine: 1,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.done),
              verticalSizedBox(10),

              Row(
                children: [
                  InkWell(
                    onTap: () {
                      editDocumentBloc.sellerDocumentBloc.onTapAddFile();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 5),
                      decoration: BoxDecoration(
                          color: AppColors.textFieldFill1,
                          borderRadius: BorderRadius.all(Radius.circular(5))),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            AppImages.plus,
                            fit: BoxFit.cover,
                            color: AppColors.writingColor2,
                          ),
                          horizontalSizedBox(10),
                          Text(
                            "upload a new file",
                            style: TextStyle(
                                fontFamily: "LatoBold",
                                fontWeight: FontWeight.w700,
                                fontSize: 15,
                                color: AppColors.writingColor2),
                          )
                        ],
                      ),
                    ),
                  ),
                  horizontalSizedBox(15),
                  StreamBuilder<bool>(
                      stream: editDocumentBloc.sellerDocumentBloc
                          .selectedDocumentRefreshCtrl.stream,
                      builder: (context, snapshot) {
                        return InkWell(
                          onTap: () {
                            CommonMethods.openLocalFile(
                                path: editDocumentBloc
                                    .sellerDocumentBloc.documentPath!.path!,
                                context: context);
                          },
                          child: Text(
                            editDocumentBloc.sellerDocumentBloc.fileName == null
                                ? ""
                                : editDocumentBloc.sellerDocumentBloc.fileName!,
                            softWrap: false,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.left,
                            style: TextStyle(
                                fontFamily: "LatoRegular",
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                                color: AppColors.writingColor2),
                          ),
                        );
                      })
                ],
              ),

              //Check box
              appRadioCheckBox(
                  text: "Show this to public",
                  isRadio: false,
                  isActive: editDocumentBloc.document.showToPublic!,
                  fontSize: 14,
                  isExpand: false,
                  onTap: () {
                    editDocumentBloc.document.showToPublic =
                        !editDocumentBloc.document.showToPublic!;
                    //Refresh edit document
                    editDocumentBloc.editDocumentRefreshCtrl.sink.add(true);
                  }),

              verticalSizedBox(25),

              InkWell(
                onTap: () {
                  editDocumentBloc.sellerDocumentBloc.documentPath == null
                      ? editDocumentBloc.editDocumentNameApiCall(
                          document: editDocumentBloc.document)
                      : editDocumentBloc.editDocumentNameFileApiCall(
                          document: editDocumentBloc.document);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  decoration: BoxDecoration(
                      color: AppColors.brandBlack,
                      borderRadius: BorderRadius.all(Radius.circular(5))),
                  child: Text(
                    "Done",
                    style: TextStyle(
                        fontFamily: "LatoBold",
                        fontWeight: FontWeight.w700,
                        fontSize: 15,
                        color: AppColors.appWhite),
                  ),
                ),
              ),
            ],
          );
        });
  }
//endregion
}
