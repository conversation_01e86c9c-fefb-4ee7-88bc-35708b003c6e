import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class DownloadAppBannerBloc {
  //region Common variable
  BuildContext context;
//endregion


  //region Controller
  final isHideCtrl = StreamController<bool>.broadcast();
  //endregion

//region Constructor
  DownloadAppBannerBloc(this.context);
  //endregion


  //region Init
  void init() {
    // isHideCtrl.sink.add(kIsWeb?false: true);
  }
  //endregion



//region On tap close button
  void onTapCloseButton() {
    isHideCtrl.sink.add(true);
  }
//endregion
}