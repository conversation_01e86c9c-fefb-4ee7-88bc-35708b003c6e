import 'package:flutter/material.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/features/navigation/config/navigation_actions.dart';

/// Enum for different navigation types
enum NavigationType { buyer, seller }

/// Enum for gesture types
enum GestureType { tap, doubleTap, longPress, swipe }

/// Configuration for a single tab
class TabConfig {
  final String name;
  final String iconOutlined;
  final String iconFilled;
  final Widget Function() screenBuilder;
  final Map<GestureType, VoidCallback> gestures;

  const TabConfig({
    required this.name,
    required this.iconOutlined,
    required this.iconFilled,
    required this.screenBuilder,
    required this.gestures,
  });
}

/// Centralized navigation configuration
class NavigationConfig {
  static const Map<NavigationType, List<TabConfig>> _tabConfigs = {
    NavigationType.buyer: _buyerTabs,
    NavigationType.seller: _sellerTabs,
  };

  /// Get tab configurations for a specific navigation type
  static List<TabConfig> getTabConfigs(NavigationType type) {
    return _tabConfigs[type] ?? [];
  }

  /// Get tab icons for a specific navigation type
  static List<String> getTabIcons(NavigationType type, {bool filled = false}) {
    final configs = getTabConfigs(type);
    return configs
        .map((config) => filled ? config.iconFilled : config.iconOutlined)
        .toList();
  }

  /// Get tab names for a specific navigation type
  static List<String> getTabNames(NavigationType type) {
    final configs = getTabConfigs(type);
    return configs.map((config) => config.name).toList();
  }

  /// Get tab screens for a specific navigation type
  static List<Widget> getTabScreens(NavigationType type) {
    final configs = getTabConfigs(type);
    return configs.map((config) => config.screenBuilder()).toList();
  }

  /// Execute gesture action for a specific tab
  static void executeGesture(
      NavigationType type, int tabIndex, GestureType gestureType) {
    final configs = getTabConfigs(type);
    if (tabIndex < configs.length) {
      final gesture = configs[tabIndex].gestures[gestureType];
      gesture?.call();
    }
  }

  /// Buyer tab configurations
  static const List<TabConfig> _buyerTabs = [
    TabConfig(
      name: "Home",
      iconOutlined: AppImages.homeIconOutlined,
      iconFilled: AppImages.homeIconFilled,
      screenBuilder: _buildBuyerHomeScreen,
      gestures: {
        GestureType.tap: _buyerHomeTap,
        GestureType.doubleTap: _buyerHomeDoubleTap,
        // GestureType.longPress: _buyerHomeLongPress,
        // GestureType.swipe: _buyerHomeSwipe,
      },
    ),
    TabConfig(
      name: "Search",
      iconOutlined: AppImages.searchIconOutlined,
      iconFilled: AppImages.searchIcon2Filled,
      screenBuilder: _buildBuyerSearchScreen,
      gestures: {
        GestureType.tap: _buyerSearchTap,
        GestureType.doubleTap: _buyerSearchDoubleTap,
        // GestureType.longPress: _buyerSearchLongPress,
        // GestureType.swipe: _buyerSearchSwipe,
      },
    ),
    TabConfig(
      name: "Notifications",
      iconOutlined: AppImages.notificationIconOutlined,
      iconFilled: AppImages.notificationIconFilled,
      screenBuilder: _buildBuyerNotificationScreen,
      gestures: {
        GestureType.tap: _buyerNotificationTap,
        GestureType.doubleTap: _buyerNotificationDoubleTap,
        // GestureType.longPress: _buyerNotificationLongPress,
        // GestureType.swipe: _buyerNotificationSwipe,
      },
    ),
    TabConfig(
      name: "Add",
      iconOutlined: AppImages.addIconOutlined,
      iconFilled: AppImages.addIconOutlined,
      screenBuilder: _buildBuyerAddScreen,
      gestures: {
        GestureType.tap: _buyerAddTap,
        // GestureType.doubleTap: _buyerAddDoubleTap,
        GestureType.longPress: _buyerAddLongPress,
        // GestureType.swipe: _buyerAddSwipe,
      },
    ),
  ];

  /// Seller tab configurations
  static const List<TabConfig> _sellerTabs = [
    TabConfig(
      name: "Home",
      iconOutlined: AppImages.homeIconOutlined,
      iconFilled: AppImages.homeIconFilled,
      screenBuilder: _buildSellerHomeScreen,
      gestures: {
        GestureType.tap: _sellerHomeTap,
        GestureType.doubleTap: _sellerHomeDoubleTap,
        // GestureType.longPress: _sellerHomeLongPress,
        // GestureType.swipe: _sellerHomeSwipe,
      },
    ),
    TabConfig(
      name: "Search",
      iconOutlined: AppImages.searchIconOutlined,
      iconFilled: AppImages.searchIcon2Filled,
      screenBuilder: _buildSellerSearchScreen,
      gestures: {
        GestureType.tap: _sellerSearchTap,
        GestureType.doubleTap: _sellerSearchDoubleTap,
        // GestureType.longPress: _sellerSearchLongPress,
        // GestureType.swipe: _sellerSearchSwipe,
      },
    ),
    TabConfig(
      name: "Orders",
      iconOutlined: AppImages.ordersIconOutlined,
      iconFilled: AppImages.ordersIconFilled,
      screenBuilder: _buildSellerOrdersScreen,
      gestures: {
        GestureType.tap: _sellerOrdersTap,
        GestureType.doubleTap: _sellerOrdersDoubleTap,
        GestureType.longPress: _sellerOrdersLongPress,
        // GestureType.swipe: _sellerOrdersSwipe,
      },
    ),
    TabConfig(
      name: "Notifications",
      iconOutlined: AppImages.notificationIconOutlined,
      iconFilled: AppImages.notificationIconFilled,
      screenBuilder: _buildSellerNotificationScreen,
      gestures: {
        GestureType.tap: _sellerNotificationTap,
        GestureType.doubleTap: _sellerNotificationDoubleTap,
        // GestureType.longPress: _sellerNotificationLongPress,
        // GestureType.swipe: _sellerNotificationSwipe,
      },
    ),
    TabConfig(
      name: "Add",
      iconOutlined: AppImages.addIconOutlined,
      iconFilled: AppImages.addIconOutlined,
      screenBuilder: _buildSellerAddScreen,
      gestures: {
        GestureType.tap: _sellerAddTap,
        // GestureType.doubleTap: _sellerAddDoubleTap,
        GestureType.longPress: _sellerAddLongPress,
        // GestureType.swipe: _sellerAddSwipe,
      },
    ),
  ];

  // Screen builders
  static Widget _buildBuyerHomeScreen() =>
      NavigationActions.buildBuyerHomeScreen();
  static Widget _buildBuyerSearchScreen() =>
      NavigationActions.buildBuyerSearchScreen();
  static Widget _buildBuyerNotificationScreen() =>
      NavigationActions.buildBuyerNotificationScreen();
  static Widget _buildBuyerAddScreen() =>
      NavigationActions.buildBuyerAddScreen();

  static Widget _buildSellerHomeScreen() =>
      NavigationActions.buildSellerHomeScreen();
  static Widget _buildSellerSearchScreen() =>
      NavigationActions.buildSellerSearchScreen();
  static Widget _buildSellerOrdersScreen() =>
      NavigationActions.buildSellerOrdersScreen();
  static Widget _buildSellerNotificationScreen() =>
      NavigationActions.buildSellerNotificationScreen();
  static Widget _buildSellerAddScreen() =>
      NavigationActions.buildSellerAddScreen();

  // Gesture handlers
  static void _buyerHomeTap() {}
  static void _buyerHomeDoubleTap() =>
      NavigationActions.doubleTapAction(_buildBuyerHomeScreen(), 0);
  static void _buyerHomeLongPress() => NavigationActions.buyerHomeLongPress();
  static void _buyerHomeSwipe() {}

  static void _buyerSearchTap() {}
  static void _buyerSearchDoubleTap() =>
      NavigationActions.doubleTapAction(_buildBuyerSearchScreen(), 1);
  static void _buyerSearchLongPress() =>
      NavigationActions.buyerSearchLongPress();
  static void _buyerSearchSwipe() {}

  static void _buyerNotificationTap() {}
  static void _buyerNotificationDoubleTap() =>
      NavigationActions.doubleTapAction(_buildBuyerNotificationScreen(), 2);
  static void _buyerNotificationLongPress() =>
      NavigationActions.buyerNotificationLongPress();
  static void _buyerNotificationSwipe() {}

  static void _buyerAddTap() {}
  static void _buyerAddDoubleTap() =>
      NavigationActions.doubleTapAction(_buildBuyerAddScreen(), 3);
  static void _buyerAddLongPress() => NavigationActions.buyerAddLongPress();
  static void _buyerAddSwipe() {}

  static void _sellerHomeTap() {}
  static void _sellerHomeDoubleTap() =>
      NavigationActions.doubleTapAction(_buildSellerHomeScreen(), 0);
  static void _sellerHomeLongPress() => NavigationActions.sellerHomeLongPress();
  static void _sellerHomeSwipe() {}

  static void _sellerSearchTap() {}
  static void _sellerSearchDoubleTap() =>
      NavigationActions.doubleTapAction(_buildSellerSearchScreen(), 1);
  static void _sellerSearchLongPress() =>
      NavigationActions.sellerSearchLongPress();
  static void _sellerSearchSwipe() {}

  static void _sellerOrdersTap() {}
  static void _sellerOrdersDoubleTap() =>
      NavigationActions.doubleTapAction(_buildSellerOrdersScreen(), 2);
  static void _sellerOrdersLongPress() =>
      NavigationActions.sellerOrdersLongPress();
  static void _sellerOrdersSwipe() {}

  static void _sellerNotificationTap() {}
  static void _sellerNotificationDoubleTap() =>
      NavigationActions.doubleTapAction(_buildSellerNotificationScreen(), 3);
  static void _sellerNotificationLongPress() =>
      NavigationActions.sellerNotificationLongPress();
  static void _sellerNotificationSwipe() {}

  static void _sellerAddTap() {}
  static void _sellerAddDoubleTap() =>
      NavigationActions.doubleTapAction(_buildSellerAddScreen(), 4);
  static void _sellerAddLongPress() => NavigationActions.sellerAddLongPress();
  static void _sellerAddSwipe() {}
}
