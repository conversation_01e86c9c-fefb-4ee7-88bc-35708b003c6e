import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_tab_view.dart';
import 'package:swadesic/util/auto_hide_navigation_controller.dart';

/// A wrapper widget that provides auto-hiding functionality for bottom navigation
class AutoHideBottomNavigation extends StatefulWidget {
  final Widget child;
  final AutoHideNavigationController? autoHideController;
  final bool enableAutoHide;
  final double bottomNavigationHeight;

  const AutoHideBottomNavigation({
    Key? key,
    required this.child,
    this.autoHideController,
    this.enableAutoHide = true,
    this.bottomNavigationHeight = kBottomNavigationBarHeight,
  }) : super(key: key);

  @override
  State<AutoHideBottomNavigation> createState() => _AutoHideBottomNavigationState();
}

class _AutoHideBottomNavigationState extends State<AutoHideBottomNavigation> {
  @override
  Widget build(BuildContext context) {
    // If auto-hide is disabled or no controller provided, return standard widget
    if (!widget.enableAutoHide || widget.autoHideController == null) {
      return widget.child;
    }

    // Wrap the bottom navigation with slide animation
    return AnimatedBuilder(
      animation: widget.autoHideController!,
      builder: (context, child) {
        return SlideTransition(
          position: widget.autoHideController!.bottomNavSlideAnimation,
          child: widget.child,
        );
      },
    );
  }
}

/// Enhanced PersistentTabView with auto-hide functionality
class AutoHidePersistentTabView extends StatefulWidget {
  final BuildContext context;
  final PersistentTabController? controller;
  final List<Widget> screens;
  final List<PersistentBottomNavBarItem> items;
  final AutoHideNavigationController? autoHideController;
  final bool enableAutoHide;
  
  // All other PersistentTabView parameters
  final bool confineInSafeArea;
  final Color backgroundColor;
  final bool handleAndroidBackButtonPress;
  final bool resizeToAvoidBottomInset;
  final bool hideNavigationBarWhenKeyboardShows;
  final bool popAllScreensOnTapOfSelectedTab;
  final bool stateManagement;
  final NavBarStyle navBarStyle;
  final NavBarDecoration? decoration;
  final EdgeInsets margin;
  final PopActionScreensType popActionScreens;
  final Function(BuildContext?)? selectedTabScreenContext;

  const AutoHidePersistentTabView({
    Key? key,
    required this.context,
    this.controller,
    required this.screens,
    required this.items,
    this.autoHideController,
    this.enableAutoHide = true,
    this.confineInSafeArea = true,
    this.backgroundColor = Colors.white,
    this.handleAndroidBackButtonPress = true,
    this.resizeToAvoidBottomInset = true,
    this.hideNavigationBarWhenKeyboardShows = true,
    this.popAllScreensOnTapOfSelectedTab = true,
    this.stateManagement = true,
    this.navBarStyle = NavBarStyle.style1,
    this.decoration,
    this.margin = EdgeInsets.zero,
    this.popActionScreens = PopActionScreensType.all,
    this.selectedTabScreenContext,
  }) : super(key: key);

  @override
  State<AutoHidePersistentTabView> createState() => _AutoHidePersistentTabViewState();
}

class _AutoHidePersistentTabViewState extends State<AutoHidePersistentTabView> {
  @override
  Widget build(BuildContext context) {
    final persistentTabView = PersistentTabView(
      widget.context,
      controller: widget.controller,
      screens: widget.screens,
      items: widget.items,
      confineInSafeArea: widget.confineInSafeArea,
      backgroundColor: widget.backgroundColor,
      handleAndroidBackButtonPress: widget.handleAndroidBackButtonPress,
      resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
      hideNavigationBarWhenKeyboardShows: widget.hideNavigationBarWhenKeyboardShows,
      popAllScreensOnTapOfSelectedTab: widget.popAllScreensOnTapOfSelectedTab,
      stateManagement: widget.stateManagement,
      navBarStyle: widget.navBarStyle,
      decoration: widget.decoration ?? NavBarDecoration(),
      margin: widget.margin,
      popActionScreens: widget.popActionScreens,
      selectedTabScreenContext: widget.selectedTabScreenContext,
      // Control visibility based on auto-hide controller
      hideNavigationBar: widget.enableAutoHide && 
                        widget.autoHideController != null ? 
                        !widget.autoHideController!.isBottomNavVisible : false,
    );

    // If auto-hide is disabled or no controller provided, return standard persistent tab view
    if (!widget.enableAutoHide || widget.autoHideController == null) {
      return persistentTabView;
    }

    // Return the persistent tab view with auto-hide animation
    return AnimatedBuilder(
      animation: widget.autoHideController!,
      builder: (context, child) {
        return persistentTabView;
      },
    );
  }
}

/// Mixin to provide auto-hide functionality to existing bottom navigation widgets
mixin AutoHideBottomNavigationMixin<T extends StatefulWidget> on State<T> {
  
  AutoHideNavigationController? _autoHideController;
  
  AutoHideNavigationController? get autoHideController => _autoHideController;
  
  /// Initialize auto-hide functionality
  void initializeAutoHide({bool enableAutoHide = true, required TickerProvider tickerProvider}) {
    if (enableAutoHide) {
      _autoHideController = AutoHideNavigationController();
      _autoHideController!.initialize(tickerProvider);
    }
  }
  
  /// Dispose auto-hide functionality
  void disposeAutoHide() {
    _autoHideController?.dispose();
    _autoHideController = null;
  }
  
  /// Attach scroll controller to auto-hide functionality
  void attachScrollController(ScrollController scrollController) {
    if (_autoHideController != null) {
      scrollController.addListener(() {
        _autoHideController!.handleScroll(scrollController);
      });
    }
  }
  
  /// Detach scroll controller from auto-hide functionality
  void detachScrollController(ScrollController scrollController) {
    if (_autoHideController != null) {
      scrollController.removeListener(() {
        _autoHideController!.handleScroll(scrollController);
      });
    }
  }
  
  /// Force show navigation bars
  void forceShowNavigationBars() {
    _autoHideController?.forceShowNavigationBars();
  }
  
  /// Force hide navigation bars
  void forceHideNavigationBars() {
    _autoHideController?.forceHideNavigationBars();
  }
  
  /// Reset auto-hide state
  void resetAutoHide() {
    _autoHideController?.reset();
  }
}
