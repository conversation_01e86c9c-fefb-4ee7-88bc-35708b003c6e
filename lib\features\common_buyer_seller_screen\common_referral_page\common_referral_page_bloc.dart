import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/invite_reward_info/invite_reward_info_response.dart';
import 'package:swadesic/model/user_rewards_and_invitees_response/user_rewards.dart';
import 'package:swadesic/services/invite_reward_info_service/invite_reward_info_service.dart';
import 'package:swadesic/services/user_rewards_and_invitees_service/user_reward_and_invitees_service.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
enum CommonReferralPageState { Loading, Success, Failed }

class CommonReferralPageBloc {
  // region Common Variables
  BuildContext context;

  late InviteRewardInfo inviteRewardInfo = InviteRewardInfo();
  List<String> userViewImages = [AppImages.plantTree,
    AppImages.shareThreeLineTransparent,
    AppImages.storeWithShareWithTransparent,
  ];
  String inviteCode = "";
  // endregion


  //region Controller
  final commonReferralPageStateCtrl = StreamController<CommonReferralPageState>.broadcast();
  final dropDownCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  CommonReferralPageBloc(this.context);
  // endregion

  // region Init
  void init() {
    getRewardInfo();
  }
// endregion

  //
  // //region On tap dropdown
  // void onTapDropDown({required bool value}){
  //   dropDownCtrl.sink.add(!value);
  // }
  // //endregion


  //region Get reward info
  Future <void> getRewardInfo() async {
    try {
      inviteRewardInfo = await InviteRewardInfoService().getInviteRewardInfo();
      inviteCode = await UserRewardAndInviteesService().getInviteCode();
      //Add imaged in reward info
      if (inviteRewardInfo.commonView?.allAboutInfinityDetailList != null) {
        for (int i = 0; i < inviteRewardInfo.commonView!.allAboutInfinityDetailList!.length; i++) {
          if (i < userViewImages.length) {
            inviteRewardInfo.commonView!.allAboutInfinityDetailList![i].image = userViewImages[i];
          } else {
            break; // Exit the loop if we run out of images
          }
        }
      }
      //Success
      commonReferralPageStateCtrl.sink.add(CommonReferralPageState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      // For static users, show the page anyway with default content
      if (CommonMethods().isStaticUser()) {
        // Initialize with empty data for static users
        inviteRewardInfo = InviteRewardInfo();
        inviteCode = ""; // Empty invite code for static users
        //Success - show page even for static users
        commonReferralPageStateCtrl.sink.add(CommonReferralPageState.Success);
      } else {
        //Failed for authenticated users
        commonReferralPageStateCtrl.sink.add(CommonReferralPageState.Failed);
      }
    } catch (error) {
      // For static users, show the page anyway with default content
      if (CommonMethods().isStaticUser()) {
        // Initialize with empty data for static users
        inviteRewardInfo = InviteRewardInfo();
        inviteCode = ""; // Empty invite code for static users
        //Success - show page even for static users
        commonReferralPageStateCtrl.sink.add(CommonReferralPageState.Success);
      } else {
        //Failed for authenticated users
        commonReferralPageStateCtrl.sink.add(CommonReferralPageState.Failed);
      }
    }
  }
  //endregion


//region Dispose
  void dispose(){
    dropDownCtrl.close();
    commonReferralPageStateCtrl.close();
  }
//endregion



}
