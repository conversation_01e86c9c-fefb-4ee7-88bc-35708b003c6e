import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/dashboard_card_slider.dart';

class ModularStoreDashboardContainer extends StatefulWidget {
  final String storeReference;

  const ModularStoreDashboardContainer({
    Key? key,
    required this.storeReference,
  }) : super(key: key);

  @override
  State<ModularStoreDashboardContainer> createState() =>
      _ModularStoreDashboardContainerState();
}

class _ModularStoreDashboardContainerState
    extends State<ModularStoreDashboardContainer> {

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return Consumer<StoreDashboardDataModel>(
      builder: (context, dashboardModel, _) {
        final storeDashBoard = dashboardModel.storeDashBoard;

        return DashboardCardSlider(
          storeDashboard: storeDashBoard,
          storeReference: widget.storeReference,
        );
      },
    );
  }

}
