import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward/balance_and_invite_code/balance_and_invite_code_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/all_about_infinity/all_about_infinity.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class BalanceAndInviteCode extends StatefulWidget {
  const BalanceAndInviteCode({super.key});

  @override
  State<BalanceAndInviteCode> createState() => _BalanceAndInviteCodeState();
}

class _BalanceAndInviteCodeState extends State<BalanceAndInviteCode> {
  //region Bloc
  late BalanceAndInviteCodeBloc balanceAndInviteCodeBloc;
  //endregion

  //region Init
  @override
  void initState() {
    balanceAndInviteCodeBloc = BalanceAndInviteCodeBloc(context);
    balanceAndInviteCodeBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<BalanceAndInviteCodeState>(
        stream: balanceAndInviteCodeBloc.balanceAndInviteCodeCtrl.stream,
        initialData: BalanceAndInviteCodeState.Loading,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == BalanceAndInviteCodeState.Loading) {
            return Container(
              height: MediaQuery.of(context).size.width / 2,
              alignment: Alignment.center,
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          //Success
          if (snapshot.data == BalanceAndInviteCodeState.Success) {
            return Column(
              children: [
                accountBalanceAndInvite(),
                earningAndSaving(),
                const AllAboutInfinity(
                  entityType: EntityType.USER,
                ),
              ],
            );
          }

          //Failed
          return AppCommonWidgets.errorWidget(
              errorMessage: AppStrings.unableToLoadBalance,
              height: MediaQuery.of(context).size.width,
              onTap: () {
                balanceAndInviteCodeBloc.init();
              });
        });
  }

  //region Account balance and invite
  Widget accountBalanceAndInvite() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill2,
                  borderRadius: BorderRadius.all(Radius.circular(
                      MediaQuery.of(context).size.width * 0.02))),
              padding: const EdgeInsets.only(
                  top: 15.5, bottom: 15.5, right: 5, left: 10),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(AppImages.infinity, height: 40, width: 40),
                  const Flexible(child: SizedBox(width: 22)),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Text(
                          "₹${balanceAndInviteCodeBloc.userRewards.rewardData!.currentBalance ?? 0}",
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            fontSize: 30,
                            fontWeight: FontWeight.w600,
                            fontFamily: "RobotoRegular",
                            color: AppColors.appBlack,
                          ),
                        ),
                      ),
                      Text(
                        "Current balance",
                        style: AppTextStyle.smallText(
                            textColor: AppColors.appBlack),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Container(
                decoration: BoxDecoration(
                    color: AppColors.textFieldFill2,
                    borderRadius: BorderRadius.all(Radius.circular(
                        MediaQuery.of(context).size.width * 0.02))),
                padding: const EdgeInsets.only(
                    top: 15.5, bottom: 15.5, right: 5, left: 10),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                            margin: const EdgeInsets.only(
                                top: 3.5, bottom: 3.5, right: 5),
                            child: Image.asset(
                              AppImages.inviteCode,
                              height: 18,
                              width: 18,
                            )),
                        Text(
                          AppStrings.inviteCode,
                          style: AppTextStyle.sectionHeading(
                              textColor: AppColors.appBlack),
                        )
                      ],
                    ),
                    Row(
                      children: [
                        // Container(
                        //     margin: const EdgeInsets.only(top: 3.5,bottom: 3.5,right: 5),
                        //     child: Image.asset(AppImages.inviteCode,height: 18,width: 18,)),
                        Flexible(
                          child: Text(
                            "${balanceAndInviteCodeBloc.userRewards.rewardData?.inviteCode}",
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: AppTextStyle.pageHeading(
                                textColor: AppColors.brandBlack),
                          ),
                        ),
                        const SizedBox(width: 10),
                        SizedBox(
                            height: 22,
                            width: 22,
                            child: CupertinoButton(
                                padding: EdgeInsets.zero,
                                onPressed: () {
                                  CommonMethods.copyText(
                                      context,
                                      balanceAndInviteCodeBloc
                                          .userRewards.rewardData!.inviteCode!);
                                },
                                child: Image.asset(
                                  AppImages.copy,
                                  height: 22,
                                  width: 22,
                                ))),
                        const SizedBox(width: 10),
                        SizedBox(
                          height: 22,
                          width: 22,
                          child: CupertinoButton(
                              padding: EdgeInsets.zero,
                              onPressed: () {
                                CommonMethods.share(
                                    "${AppConstants.domainName}?ref=${balanceAndInviteCodeBloc.userRewards.rewardData!.inviteCode}");
                              },
                              child: Image.asset(
                                AppImages.shareThreeLine,
                                height: 22,
                                width: 22,
                              )),
                        ),
                      ],
                    ),
                  ],
                )),
          ),
        ],
      ),
    );
  }
  //endregion

  //region Earning and saving
  Widget earningAndSaving() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 3),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill2,
                  borderRadius: BorderRadius.all(Radius.circular(
                      MediaQuery.of(context).size.width * 0.02))),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              child: Text(
                "Total earnings: ₹${balanceAndInviteCodeBloc.userRewards.rewardData!.totalEarnings}",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill2,
                  borderRadius: BorderRadius.all(Radius.circular(
                      MediaQuery.of(context).size.width * 0.02))),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              child: Text(
                "Total savings: ₹${balanceAndInviteCodeBloc.userRewards.rewardData!.totalSavings}",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ),
          )
        ],
      ),
    );
  }
  //endregion
}
