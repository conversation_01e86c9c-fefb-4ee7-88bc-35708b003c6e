import 'dart:async';

import 'package:contacts_service/contacts_service.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customer_or_friends_disclaimer/find_your_customer_or_friends_disclaimer.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/find_your_customers_response/get_contact_user_and_store_info_response.dart';
import 'package:swadesic/model/maintenance_response/maintenance_response.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/app_permission_handler/app_permission_handler.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/services/maintenance_services/maintenance_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum FindYourCustomersState {
  Loading,
  Success,
  Failed,
  Empty,
  PermissionDenied,
  PermissionPermanatlyDenied,
  Search_Empty
}

class FindYourCustomersBloc {
  // region Common Methods
  BuildContext context;
  List<ContactUserAndStoreInfo> contactUserAndStoreInfoList = [];
  List<ContactUserAndStoreInfo> searchResult = [];
  // final String reference;
  AppBoolEnum syncStatus = AppBoolEnum.TRUE;
  List<Contact> allContactFromMobile = [];
  final bool iSFromOnboarding;

  GlobalKey<RefreshIndicatorState> refreshKey =
      GlobalKey<RefreshIndicatorState>();

  // endregion
  //region Controller
  final findYourCustomerStateCtrl =
      StreamController<FindYourCustomersState>.broadcast();
  final TextEditingController searchTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  FindYourCustomersBloc(
      this.context,
      // this.reference,
      this.iSFromOnboarding);

  // endregion

  // region Init
  Future<void> init() async {
    await Future.delayed(Duration.zero);
    // getMaintenanceDetailApiCall();
    await openDialog();
    // await requestPermission();
    searchTextCtrl.addListener(() {
      onChangeText();
    });
  }

  // endregion

  //region Disclaimer
  Future openDialog() async {
    PermissionStatus status = await Permission.contacts.status;
    //If permission not allowed
    if (status == PermissionStatus.granted) {
      return requestPermission();
    }
    return CommonMethods.appDialogBox(
        barrierDismissible: false,
        context: context,
        widget: SaveOrDiscard(
          onTapSave: (value) {
            //print("Hello");
            requestPermission();
          },
          onTapCancel: (data) {
            //Not granted
            findYourCustomerStateCtrl.sink
                .add(FindYourCustomersState.PermissionDenied);
            goToBuyerBottomNavigation();
          },
          popPreviousScreen: true,
          firstButtonName: AppStrings.okay,
          secondButtonName: AppStrings.later,
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.contactPermissionDisclaimer,
        )).then((value) {
      //If from onboarding and the contact permission is not accepted
      if (iSFromOnboarding && status != PermissionStatus.granted) {
        //Close popup and push to bottom navigation
        // goToBuyerBottomNavigation();
      }
    });
  }
//endregion

  //region Go to buyer Bottom Navigation
  void goToBuyerBottomNavigation() async {
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    //If sign up process from static user
    if (AppConstants.isSignInScreenOpenedForStatisUser) {
      //Get user detail
      await getLoggedInUserDetail();
      // //Current
      // Navigator.pop(context);
      // //Pop add picture only if the pic is null
      // if(loggedInUserInfoDataModel.userDetail!.icon == null || loggedInUserInfoDataModel.userDetail!.icon == "") {
      //   Navigator.pop(context);
      // }
      // //Otp filed screen pop
      // Navigator.pop(context);
      // //Mobile number screen pop
      // // Navigator.pop(context);
      // //Otp screen
      // Navigator.pop(context);
      Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
          .popUntil((route) => route.isFirst);

      return;
    }
    await Future.delayed(Duration.zero);

    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
            builder: (context) => const UserBottomNavigation(
                  isFromOnboardingFlow: true,
                )),
        (Route<dynamic> route) => false);
    // Navigator.of(context).pushReplacement(
    //   MaterialPageRoute(
    //     builder: (context) => const UserBottomNavigation(),
    //   ),
    // );

    // var screen =   UserBottomNavigation();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
  //endregion

  //region Get logged in user detail
  Future<void> getLoggedInUserDetail() async {
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    //region Try
    try {
      //selectedAddressRefreshCtrl.sink.add(SellerReturnWarrantyState.Loading);
      GetUserDetailsResponse userDetailsResponse = await UserDetailsServices()
          .getLoggedInUserDetail(
              userReference: AppConstants.appData.userReference!);

      ///Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(
          data: userDetailsResponse.userDetail!);
      //Update the buy button to refresh in all loaded product
      for (var product in productDataModel.allProducts) {
        product.isPinCodeChanged = true;
      }
      //Update ui
      productDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      debugPrint(error.message);
    } catch (error) {
      debugPrint(error.toString());
    }
  }
  //endregion

  ///Single follow
  //region On tap Follow and support
  void onTapFollowAndSupport(
      {required ContactUserAndStoreInfo contactUserAndStoreInfo}) async {
    try {
      contactUserAndStoreInfo.followStatus = await FindYourCustomersServices()
          .followAndUnFollow(reference: contactUserAndStoreInfo.reference!);

      //Update ui

      //Success
      findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
    } on ApiErrorResponseMessage {
      //Success
      findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
    } catch (error) {
      findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
    }
  }

  //endregion

  /// Step 1
  // //region Request permission
  // Future<PermissionStatus> requestPermission() async {
  //   //Check permission
  //   PermissionStatus permissionStatus = await AppPermissionHandler.checkContactsPermission();
  //   //Allowed
  //   if (permissionStatus == PermissionStatus.granted) {
  //     await getContact();
  //   }
  //   //If denied then call again this same method
  //   else if (permissionStatus == PermissionStatus.denied) {
  //     findYourCustomerStateCtrl.sink.add(FindYourCustomersState.PermissionDenied);
  //     //Un-sync contacts
  //     unSyncContact();
  //   }
  //   //If permission is permanently denied
  //   else if (permissionStatus == PermissionStatus.permanentlyDenied) {
  //     findYourCustomerStateCtrl.sink.add(FindYourCustomersState.PermissionPermanatlyDenied);
  //     //Un-sync contacts
  //     unSyncContact();
  //   }
  //   return permissionStatus;
  // }
  //
  // //endregion

  //region Request permission
  Future<PermissionStatus> requestPermission() async {
    try {
      //Check permission
      PermissionStatus permissionStatus =
          await AppPermissionHandler.checkContactsPermission();

      //Allowed
      if (permissionStatus == PermissionStatus.granted) {
        await getContact();
      }
      //If denied then call again this same method
      else if (permissionStatus == PermissionStatus.denied) {
        findYourCustomerStateCtrl.sink
            .add(FindYourCustomersState.PermissionDenied);
        //Un-sync contacts
        unSyncContact();
      }
      //If permission is permanently denied
      else if (permissionStatus == PermissionStatus.permanentlyDenied) {
        findYourCustomerStateCtrl.sink
            .add(FindYourCustomersState.PermissionPermanatlyDenied);
        //Un-sync contacts
        unSyncContact();
      }

      return permissionStatus;
    } catch (e) {
      // Handle any exceptions
      //print('Error requesting permission: $e');
      PermissionStatus permissionStatus =
          await AppPermissionHandler.checkContactsPermission();

      CommonMethods.toastMessage(
          "Exception is${e.toString()} and current permission status is ${permissionStatus.toString()}",
          context);
      return PermissionStatus.permanentlyDenied;
    }
  }
//endregion

  ///Un-sync
  //region Un-sync contact
  Future<void> unSyncContact() async {
    try {
      await FindYourCustomersServices().unSyncContacts();
    } on ApiErrorResponseMessage {
      //Success
      // findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
    } catch (error) {
      // findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
    }
  }

  ///Step 2
  //region Get contact
  Future<void> getContact() async {
    //Number list
    List<String> contactNumberList = [];
    //Valid number list
    List<String> validContactsList = [];
    // Fetch all contacts from mobile
    allContactFromMobile = await ContactsService.getContacts(
      photoHighResolution: false,
      withThumbnails: false,
    );
    // //("Contact is fetched");

    //Run a loop and get all contacts
    for (var contact in allContactFromMobile) {
      //If contact is not null
      if (contact.phones != null) {
        //Add all contact number
        contactNumberList.addAll(contact.phones!.map(
            (e) => CommonMethods().fixMobileNumber(mobileNumber: e.value!)));
      } else {
        continue;
      }
    }

    //Filter valid number
    for (var number in contactNumberList) {
      //If then number is same as logged in user then ignore
      if (number == AppConstants.appData.mobileNumber) {
        continue;
      }

      //If valid then add to the valid list
      if (CommonMethods().isValidNumber(mobileNumber: number)) {
        validContactsList.add(number);
      } else {
        continue;
      }
    }

    //Get user and store by contact number
    await getUserAndStoreByContactNumber(contactList: validContactsList);
  }

//endregion8075421186

  ///Step 3
  //region Get user and store by contact number
  Future<void> getUserAndStoreByContactNumber(
      {required List<String> contactList}) async {
    //If Contact list is empty the return
    if (contactList.isEmpty) {
      findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Empty);
      return;
    }
    try {
      findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Loading);

      contactUserAndStoreInfoList = await FindYourCustomersServices()
          .getContactUserAndStoreInfo(
              contactNumberList: contactList, appBoolEnum: syncStatus);
      //Clear search
      searchResult.clear();
      //Assign the name to the contact
      await assignTheNameToUnRegisterContact();
      //Add api data to search result
      searchResult.addAll(contactUserAndStoreInfoList);
      //Sort
      searchResult.sort((a, b) => a.name!.compareTo(b.name!));

      //Success
      findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
    } on ApiErrorResponseMessage catch (e) {
      //Success
      findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Failed);
    } catch (error) {
      findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Failed);
    }
  }

  //endregion

  ///Step 4
  //region Assign the name to the un-register contact
  Future<void> assignTheNameToUnRegisterContact() async {
    // Fetch all contacts from mobile
    // List<Contact> allContactFromMobile = await ContactsService.getContacts();

    //If user is un-register
    if (contactUserAndStoreInfoList
        .any((element) => element.entityType == EntityType.UNREGISTERED.name)) {
      //Contact loop
      for (var phone in allContactFromMobile) {
        //If phone is null then continue
        if (phone.phones == null) {
          continue;
        }
        //Value
        for (var value in phone.phones!) {
          //(value.value);
          //Run loop for searchResult
          for (var data in contactUserAndStoreInfoList) {
            //If data number and value is same then replace then name with the phone name
            if (data.phonenumber ==
                CommonMethods().fixMobileNumber(mobileNumber: value.value!)) {
              data.name = phone.displayName;
            }
            // else{
            //   data.name = AppStrings.deletedContact;
            // }
          }
        }
      }
    }
  }

  //endregion

//region On change text
  void onChangeText() {
    //(searchTextCtrl.text);
    // Clear search
    searchResult.clear();

    // If data is empty, add all items
    if (searchTextCtrl.text.isEmpty) {
      searchResult.addAll(contactUserAndStoreInfoList);
    } else {
      // Filter and add based on the lowercase name
      searchResult.addAll(contactUserAndStoreInfoList
          .where((element) => element.name!
              .toLowerCase()
              .contains(searchTextCtrl.text.toLowerCase()))
          .toList());
      if (searchResult.isEmpty) {
        return findYourCustomerStateCtrl.sink
            .add(FindYourCustomersState.Search_Empty);
      }
      //(searchResult.length);
    }

    //Sort
    searchResult.sort((a, b) => a.name!.compareTo(b.name!));

    // Update UI
    findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
  }

//endregion

//region Open disclaimer

  Future openDisclaimer() {
    return CommonMethods.appBottomSheet(
      context: context,
      bottomSheetName: AppStrings.disclaimer,
      screen: FindYourCustomerOrFriendDisclaimer(),
    );
  }

//endregion

//region Format mobile number
  String formatMobileNumber({required String number}) {
    // Remove spaces and hyphens from the number
    number = number.replaceAll(' ', '').replaceAll('-', '');

    // Check if the number starts with a '+' sign
    if (!number.startsWith('+')) {
      // If it doesn't have a country code, add the default +91
      number = '+91$number';
    }

    return number;
  }

//endregion

//region Updated follow status
  Map<String, dynamic> updatedFollowStatus({
    required String action,
    bool isActiveUser = false,
    bool isInActiveUser = false,
    bool isStore = false,
  }) {
    Map<String, dynamic> result = {
      'text': action,
      'updatedStatus': action,
    };

    if (isActiveUser) {
      if (action == "Follow") {
        result['text'] = "Following";
        result['updatedStatus'] = "Following";
      } else if (action == "Follow back") {
        result['text'] = "Following";
        result['updatedStatus'] = "Following";
      } else if (action == "Following") {
        result['text'] = "Follow";
        result['updatedStatus'] = "Follow";
      }
    } else if (isInActiveUser) {
      if (action == "Follow") {
        result['text'] = "Pending";
        result['updatedStatus'] = "Pending";
      } else if (action == "Follow back") {
        result['text'] = "Pending";
        result['updatedStatus'] = "Pending back";
      } else if (action == "Pending") {
        result['text'] = "Follow";
        result['updatedStatus'] = "Follow";
      } else if (action == "Following") {
        result['text'] = "Follow";
        result['updatedStatus'] = "Follow";
      }
    } else if (isStore) {
      if (action == "Support") {
        result['text'] = "Supporting";
        result['updatedStatus'] = "Supporting";
      } else if (action == "Support back") {
        result['text'] = "Supporting";
        result['updatedStatus'] = "Supporting";
      } else if (action == "Supporting") {
        result['text'] = "Support";
        result['updatedStatus'] = "Support";
      }
    }

    return result;
  }

//endregion

//region Follow button color
  Map<String, Color> followButtonColor({required String status}) {
    Color backgroundColor = Colors.transparent;
    Color textColor = Colors.black;

    if (status == "Following" ||
        status == "Supporting" ||
        status == "Pending") {
      textColor = AppColors.writingBlack1;
      backgroundColor = AppColors.textFieldFill1;
    } else {
      textColor = AppColors.appWhite;
      backgroundColor = AppColors.brandBlack;
    }

    return {'textColor': textColor, 'backgroundColor': backgroundColor};
  }

//endregion

//region Dispose
  void dispose() {
    findYourCustomerStateCtrl.close();
  }
//endregion
}
