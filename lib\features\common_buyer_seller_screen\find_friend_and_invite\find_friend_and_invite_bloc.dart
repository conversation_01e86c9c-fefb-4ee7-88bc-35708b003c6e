// import 'dart:async';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:in_app_update/in_app_update.dart';
// import 'package:swadesic/features/buyers/buyer_home/banner/banner_action.dart';
// import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
// import 'package:swadesic/model/api_response_message.dart';
// import 'package:swadesic/model/banner_response/banner_response.dart';
// import 'package:swadesic/services/banner_services/banner_service.dart';
// import 'package:swadesic/util/app_constants.dart';
// import 'package:swadesic/util/app_strings.dart';
// import 'package:swadesic/util/common_methods.dart';
// import 'package:swadesic/util/store_user_product_navigation/store_user_product_navigation.dart';
//
// class FindFriendAndInviteBloc {
//   // region Common Variables
//   BuildContext context;
//
//   int currentPage = 0;
//   ///Banner service and response
//   late BannerResponse bannerResponse  = BannerResponse();
//   late Timer timer;
//
//   // endregion
//   //region Page View Controller
//   PageController imageSliderPageCtrl = PageController();
//   //endregion
//
//
//   //region Controller
//
//   final bannerCtrl = StreamController<List<BannerInfo>>.broadcast();
//   //endregion
//
//   // region | Constructor |
//   FindFriendAndInviteBloc(this.context);
//   // endregion
//
//   // region Init
//   void init() async{
//   }
// // endregion
//
//
//
//
//   //region Auto slide
//   void autoSlide()async{
//
//     timer = Timer.periodic(const Duration(seconds: 5), (Timer timer) {
//
//       if (currentPage < 2) {
//         currentPage++;
//       } else {
//         currentPage = 0;
//       }
//       imageSliderPageCtrl.animateToPage(
//         currentPage,
//         duration: const Duration(milliseconds: 800),
//         curve: Curves.ease,
//       );
//     });
//   }
//   //endregion
//
//
//   //region On tap banner
//   void onTapBanner({required BannerInfo bannerInfo})async{
//     BannerAction(context,bannerInfo);
//   }
//   //endregion
//
//   //region On Change Slider
//   void onChangeSlider(int index) {
//     currentPage = index;
//     bannerCtrl.sink.add(bannerList);
//   }
// //endregion
//
//
// //region Dispose
//   void dispose() {
//     imageCache.clear();
//     bannerCtrl.close();
//     timer.cancel();
//     imageSliderPageCtrl.dispose();
//   }
// //endregion
//
// }
