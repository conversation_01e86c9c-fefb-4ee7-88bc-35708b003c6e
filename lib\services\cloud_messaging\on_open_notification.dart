import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/store_user_product_navigation/store_user_product_navigation.dart';

class OnOpenAppNotification{
  // late RemoteMessage notification;

  //Constructor
  // OnOpenAppNotification(){
  //
  //   //print("Bottom navigation mounted check");
  //   bottomNavigationMountedCheck(notification: notification);
  //   //Check if the bottom navigation is mounted or not
  //   //If it is mounted then call the navigation method
  //
  // }



  ///1
  //region Bottom navigation mounted check
  void bottomNavigationMountedCheck({required RemoteMessage notification}){
    //print("On tap notification add listner is called");

    ///Todo un-commnet to test i did it because it was opening3 times
    // AppConstants.isBottomNavigationMounted.addListener(() async{
    //   // await Future.delayed(const Duration(seconds: 5));
    //   //Is bottom sheet mounted called
    //   checkUserOrStoreView(notification: notification);
    //   return;
    // });
    checkUserOrStoreView(notification: notification);
  }
  //endregion


  ///2
  //region Check user or store view
  //If notification is for user and view is in store then switch to user
  void checkUserOrStoreView({required RemoteMessage notification})async{

    // navigateToScreen(notification: notification);
    // return;
    //1. Notification is for user
    //2. Store view
    if(notification.data["notified_user_or_store"].toString().split("").first == 'U' && AppConstants.appData.isStoreView!){
      CommonMethods.switchToBuyer(context: AppConstants.userStoreCommonBottomNavigationContext, switchToNotificationTab: true);
      //Wait for account switch to complete before navigation
      await Future.delayed(const Duration(milliseconds: 800));
      //Navigation - immediate redirection after account switch
      navigateToScreen(notification: notification);
      return;
    }

    //1. Notification is for store
    //2. User view
    if(notification.data["notified_user_or_store"].toString().split("").first == 'S' && AppConstants.appData.isUserView!){
      // For store notifications, we need to get the store reference and ID
      // This should be handled by the notification blocs that have access to store list
      //Wait for account switch to complete before navigation
      await Future.delayed(const Duration(milliseconds: 800));
      //Navigation - immediate redirection after account switch
      navigateToScreen(notification: notification);
      return;
    }

    navigateToScreen(notification: notification);

  }
  //endregion

  ///3
  //region Navigation
  void navigateToScreen({required RemoteMessage notification, bool shouldSwitchToNotificationTab = false}) {

    ///NEW_MESSAGE - Navigate to new messaging chat screen
    if(notification.data["notification_type"] == "new_message"){
      // Navigate to new messaging chat screen
      NewMessagingChatScreen.navigateToChat(
        AppConstants.currentSelectedTabContext,
        connectingId: notification.data['sender_id'] ?? '',
        chatName: notification.data['chat_name'] ?? notification.data['sender_name'] ?? 'User',
        chatIcon: notification.data['chat_icon'] ?? notification.data['sender_icon'] ?? '',
        entityType: notification.data['entity_type'] ?? 'USER',
      );
      return;
    }

    //Notification about
    String notificationAbout = notification.data["notification_about"]!;

    //If not mounted
    if(!AppConstants.isBottomNavigationMounted.value){
      return;
    }

    ///MESSAGE_RECEIVED
    if(notification.data["notification_type"] == "MESSAGE_RECEIVED"){
      //Go to messaging screen
      var screen =  MessageDetailScreen(toEntityReference:notification.data['notification_about']);
      //notification_about
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(AppConstants.currentSelectedTabContext, route);
      return;
    }

    //If external_url value is not null
    if(notification.data["external_url"] != null){
      String url = notification.data["external_url"];
      // For web platform, ensure proper navigation instead of showing download app
      if (kIsWeb) {
        // Navigate to the intended content instead of opening external URL
        _navigateToNotificationContent(notificationAbout, shouldSwitchToNotificationTab);
      } else {
        CommonMethods.openAppWebView(webUrl: url, context: AppConstants.userStoreCommonBottomNavigationContext);
      }
      return;
    }

    //Navigate to the appropriate screen with proper tab context
    _navigateToNotificationContent(notificationAbout, shouldSwitchToNotificationTab);

  }
  //endregion

  ///4
  //region Navigate to notification content with proper tab context
  void _navigateToNotificationContent(String notificationAbout, bool shouldSwitchToNotificationTab) {
    // If we need to switch to notification tab (after account switching), do it first
    if (shouldSwitchToNotificationTab && !kIsWeb) {
      _switchToNotificationTab();
      // Add a small delay to ensure tab switch completes
      Future.delayed(const Duration(milliseconds: 300), () {
        StoreUserProductNavigation().navigateToStoreProductAndStore(references: notificationAbout);
      });
    } else {
      // Direct navigation for same account or web platform
      StoreUserProductNavigation().navigateToStoreProductAndStore(references: notificationAbout);
    }
  }
  //endregion

  ///5
  //region Switch to notification tab
  void _switchToNotificationTab() {
    try {
      if (AppConstants.appData.isUserView!) {
        // For buyer navigation, notification tab is at index 2 (Home, Search, Notifications, Add, Profile)
        AppConstants.userPersistentTabController.jumpToTab(2);
        // Clear any existing navigation stack in the notification tab
        if (AppConstants.currentSelectedTabContext.mounted) {
          Navigator.of(AppConstants.currentSelectedTabContext)
              .popUntil((route) => route.isFirst);
        }
      } else {
        // For seller navigation, notification tab is at index 3 (Home, Search, Orders, Notifications, Add, Profile)
        AppConstants.storePersistentTabController.index = 3;
        // Clear any existing navigation stack in the notification tab
        if (AppConstants.currentSelectedTabContext.mounted) {
          Navigator.of(AppConstants.currentSelectedTabContext)
              .popUntil((route) => route.isFirst);
        }
      }
    } catch (e) {
      debugPrint('Error switching to notification tab: $e');
    }
  }
  //endregion

}