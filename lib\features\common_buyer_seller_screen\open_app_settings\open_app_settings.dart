import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class OpenAppSettings extends StatelessWidget {
  final dynamic onTap;
  const OpenAppSettings({Key? key, required this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Row(
            children: [
              Expanded(
                  child: AppCommonWidgets.emptyResponseText(
                      emptyMessage: AppStrings.openSettingAndTurnOn)),
            ],
          ),
        ),
        SizedBox(
          height: 45,
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () async {
                    Navigator.pop(context);
                    onTap();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(80),
                      color: AppColors.brandBlack,
                    ),
                    child: Center(
                        child: appText(AppStrings.openAppSettings,
                            color: AppColors.appWhite,
                            fontSize: 15,
                            fontFamily: AppConstants.rRegular,
                            fontWeight: FontWeight.w700)),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
