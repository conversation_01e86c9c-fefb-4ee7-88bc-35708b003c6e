import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/util/app_strings.dart';
enum PoemState { Success, Loading }

class NoInternetBloc {
  // region Common Methods
  BuildContext context;
  int poemNumber = 0;
  String poem = AppStrings.poemList.first;
  bool isOptionVisible = false;
  bool isActiveHeart = false;
   bool isDoubleTapped = false;



  // endregion
  //region Controller
  final refreshNoInternetCtrl = StreamController<bool>.broadcast();
  final poemRefreshCtrl = StreamController<PoemState>.broadcast();

  //endregion

  // region | Constructor |
  NoInternetBloc(this.context);

  // endregion

  // region Init
  init() {

  }
  // endregion


//region On tap next
void onTapNext()async{
    //Heart to false
  isActiveHeart = false;
  //Refresh
  refreshNoInternetCtrl.sink.add(true);
    //Mark option to false
  isOptionVisible = false;
    //Increase one
  poemNumber = Random().nextInt(5);
  //Clear poem
  poem = "";
  //Add poem
  poem=AppStrings.poemList[poemNumber];
  //Refresh
  refreshNoInternetCtrl.sink.add(true);
  //Poem loading
  poemRefreshCtrl.sink.add(PoemState.Loading);
  await Future.delayed(const Duration(seconds: 1));
  //Poem Success
  poemRefreshCtrl.sink.add(PoemState.Success);

}
//endregion

  //region On tap back button
  Future<bool> onTapBackButton() async {
    if (isDoubleTapped) {
      SystemNavigator.pop();
      // Double-tap detected, close the app
      return true; // Allow the app to be closed
    } else {
      // First tap, set the flag and show a toast message
      isDoubleTapped = true;
      // ScaffoldMessenger.of(AppConstants.noInternetDialogContext!).showSnackBar(
      //   SnackBar(content: Text('Double tap back button to exit')),
      // );
      // Reset the flag after a short delay (for the second tap)
      await Future.delayed(const Duration(seconds: 2));
      isDoubleTapped = false;
      return false; // Prevent the app from being closed
    }
  }

//endregion


}
