import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_config_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/deactivate_and_delete_store/exit_survey/exit_survey_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/util/common_methods.dart';

import '../../../util/app_strings.dart';

enum DeactivateAndDeleteStoreState { Loading, Success, Failed }

class DeactivateAndDeleteStoreBloc{
  //region Common variable
  late BuildContext context;
  //Region store dashboard
  final String storeReference;

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final deActivateAndDeleteStateCtrl = StreamController<DeactivateAndDeleteStoreState>.broadcast();

//endregion
  //region Constructor
  DeactivateAndDeleteStoreBloc(this.context, this.storeReference);
  //endregion
//region Init
   init()async {
    await getStoreDashboardAndConfig();
  }
//endregion


  //region Get store dashboard and config
    Future<void> getStoreDashboardAndConfig()async{
      // Retrieve to the StoreDashboardDataModel
      StoreDashboardDataModel storeDashboardDataModel = Provider.of<StoreDashboardDataModel>(context, listen: false);

      // Get reference to the store conf
      var storeConfigDataModel = Provider.of<StoreConfigDataModel>(context, listen: false);
      try{
        //Get dashboard
        StoreDashboardResponse storeDashboardResponse = await StoreDashboardService().getStoreDashboard(storeReference:storeReference);
        //Set data into data model
        storeDashboardDataModel.addDashboard(data: storeDashboardResponse.data!);
        //Update ui
        storeDashboardDataModel.updateUi();
        //Api call for open/close for order
        var data = await StoreDashboardService().getStoreConfig(storeReference:storeReference);

        //Add data to data model
        storeConfigDataModel.addStoreConfig(data: data);
        //Update ui
        storeConfigDataModel.updateUi();
        //Success
        deActivateAndDeleteStateCtrl.sink.add(DeactivateAndDeleteStoreState.Success);
      }
      on ApiErrorResponseMessage {
        // CommonMethods.toastMessage(AppStrings.error, context);
        deActivateAndDeleteStateCtrl.sink.add(DeactivateAndDeleteStoreState.Failed);
      }
      catch(error){
        // CommonMethods.toastMessage(AppStrings.error, context);
        deActivateAndDeleteStateCtrl.sink.add(DeactivateAndDeleteStoreState.Failed);
      }
    }
  //endregion


  //region Go to exit survey
  void goToExitSurvey(){
    var screen = ExitSurveyScreen(reference: storeReference, leadingTitle: AppStrings.exitSurvey,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Open close store
  Future<void> storeOpenClose() async {
    // Retrieve the StoreDashboardDataModel
    StoreDashboardDataModel storeDashboardDataModel = Provider.of<StoreDashboardDataModel>(context, listen: false);
    // Get reference to the store config
    var storeConfigDataModel = Provider.of<StoreConfigDataModel>(context, listen: false);
    final bool newStatus = !storeDashboardDataModel.storeDashBoard.openForOrder!;

    try {
      // If store config has permission to open store then show message
      if (storeConfigDataModel.storeConfig.enableOrders!) {
        CommonMethods.toastMessage(AppStrings.youCanEnableOrdering, context, toastShowTimer: 5);
      }

      // Update local state optimistically
      storeDashboardDataModel.storeDashBoard.openForOrder = newStatus;
      storeDashboardDataModel.updateUi();

      // Show status message
      CommonMethods.toastMessage(
        newStatus ? AppStrings.yourStoreIsCurrentlyOpen : AppStrings.yourStoreIsCurrentlyClosed, 
        context
      );

      // Make API call to update status
      await StoreDashboardService().openCloseStore(storeReference: storeReference);

      // Refresh dashboard data to ensure UI is in sync with backend
      await getStoreDashboardAndConfig();
    } on ApiErrorResponseMessage catch(error) {
      // Revert on error
      storeDashboardDataModel.storeDashBoard.openForOrder = !newStatus;
      storeDashboardDataModel.updateUi();
      
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
    } catch(error) {
      // Revert on error
      storeDashboardDataModel.storeDashBoard.openForOrder = !newStatus;
      storeDashboardDataModel.updateUi();
      
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    }
  }
  //endregion



  //region Active and de-active api call
  Future<void>activeDeActiveApiCall()async{
    // Retrieve to the StoreDashboardDataModel
    StoreDashboardDataModel storeDashboardDataModel = Provider.of<StoreDashboardDataModel>(context, listen: false);
    final bool newStatus = !storeDashboardDataModel.storeDashBoard.isActive!;
  
    try{
      // Update local state optimistically
      storeDashboardDataModel.storeDashBoard.isActive = newStatus;
      storeDashboardDataModel.updateUi();
      
      // Make API call to update status
      await StoreDashboardService().activeAndDeActiveStore(
        storeReference: storeReference,
        storeActiveStatus: newStatus,
      );
      
      // Refresh dashboard data to ensure UI is in sync with backend
      await getStoreDashboardAndConfig();
    }
    on ApiErrorResponseMessage catch(error) {
      // Revert on error
      storeDashboardDataModel.storeDashBoard.isActive = !newStatus;
      storeDashboardDataModel.updateUi();
      
      if (context.mounted) {
        CommonMethods.toastMessage(error.message!, context);
      }
      deActivateAndDeleteStateCtrl.sink.add(DeactivateAndDeleteStoreState.Failed);
    }
    catch(error){
      // Revert on error
      storeDashboardDataModel.storeDashBoard.isActive = !newStatus;
      storeDashboardDataModel.updateUi();
      
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    }  
  }

//region Dispose
void dispose(){
    deActivateAndDeleteStateCtrl.close();
}
//endregion
}