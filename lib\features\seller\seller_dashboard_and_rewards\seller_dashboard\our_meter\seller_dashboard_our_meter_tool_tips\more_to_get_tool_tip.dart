import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';

class MoreToGetToolTip extends StatelessWidget {
  final String message;
  const MoreToGetToolTip({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
Widget body(){
    return Container(
      margin: const EdgeInsets.all(10),
      padding: const EdgeInsets.all(10),
      child: Wrap(
        alignment: WrapAlignment.start,
        children: [
          Text("${message}",style: AppTextStyle.contentText0(textColor: AppColors.appBlack).copyWith(height: 1.0),),
          Image.asset(AppImages.infinity,height: 10,),
          Text("balance",style: AppTextStyle.contentText0(textColor: AppColors.appBlack).copyWith(height: 1.0),),

        ],
      ),
    );
}
//endregion
}
