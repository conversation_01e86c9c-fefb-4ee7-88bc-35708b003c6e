import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/gst_and_pan/gst_and_pan_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_bloc.dart';
import 'package:swadesic/features/widgets/digital_store.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class GstAndPanCard extends StatefulWidget {
  final SingleStoreInfoResponse singleStoreInfoResponse;
  final SellerTrustCenterBloc sellerTrustCenterBloc;
  final bool isStoreOwnerView;
  final bool isSignatureVisible;

  const GstAndPanCard(
      {Key? key,
      required this.singleStoreInfoResponse,
      required this.isStoreOwnerView,
      required this.sellerTrustCenterBloc,
      this.isSignatureVisible = true})
      : super(key: key);

  @override
  State<GstAndPanCard> createState() => _GstAndPanCardState();
}

class _GstAndPanCardState extends State<GstAndPanCard> {
  //region Bloc
  late GstAndPanBloc gstAndPanBloc;
  //endregion
  //region Init
  @override
  void initState() {
    gstAndPanBloc = GstAndPanBloc(context);
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region body
  Widget body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Heading
        Row(
          children: [
            Text(AppStrings.idVerification,
                style:
                    AppTextStyle.sectionHeading(textColor: AppColors.appBlack)),
            Expanded(child: horizontalSizedBox(10)),
            Visibility(
              visible: widget.isStoreOwnerView,
              child: InkWell(
                onTap: () {
                  widget.sellerTrustCenterBloc.goToGstVerification();
                },
                child: Text("change",
                    style: AppTextStyle.access0(
                        textColor: AppColors.writingColor2)),
              ),
            )
          ],
        ),
        verticalSizedBox(20),

        //Title and title value
        Container(
          margin: const EdgeInsets.only(left: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.singleStoreInfoResponse.data!.verificationType ==
                        IdVerificationTypeEnum.INDIVIDUAL.name
                    ? "PAN"
                    : "GST",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 10),
              isVerified(),
              // const SizedBox(height: 10),

              //Signature
              Visibility(
                visible: widget.isSignatureVisible &&
                    (widget.singleStoreInfoResponse.data!.verificationType ==
                        IdVerificationTypeEnum.INDIVIDUAL.name) &&
                    (AppConstants.appData.storeReference ==
                        widget.singleStoreInfoResponse.data!.storeReference),
                child: AppTitleAndOptions(
                  title: "Signature",
                  option: ClipRRect(
                    // borderRadius:BorderRadius.all(Radius.circular(MediaQuery.of(context).size.width * 0.03)),
                    child: Container(
                        margin: const EdgeInsets.only(top: 10),
                        width: MediaQuery.of(context).size.width,
                        height: MediaQuery.of(context).size.width / 2,
                        decoration: BoxDecoration(
                            color: AppColors.textFieldFill2),
                        child: Image.network(
                          "${AppConstants.baseUrl}${widget.singleStoreInfoResponse.data!.storeSignature}",
                          fit: BoxFit.fitWidth,
                          loadingBuilder: (BuildContext context, Widget child,
                              ImageChunkEvent? loadingProgress) {
                            if (loadingProgress == null) {
                              return child;
                            } else {
                              return Center(
                                  child: Icon(
                                Icons.image,
                                color: AppColors.appBlack,
                                size: 50,
                              ));
                            }
                          },
                          errorBuilder: (BuildContext context, Object error,
                              StackTrace? stackTrace) {
                            return Center(
                              child: Icon(
                                Icons.image_not_supported,
                                color: AppColors.appBlack,
                                size: 50,
                              ),
                            );
                          },
                        )),
                  ),
                ),
              ),

              // verifiedAsBusiness()
            ],
          ),
        )
      ],
    );
  }
//endregion

//region Is verified
  Widget isVerified() {
    //If verification type is gst
    if (widget.singleStoreInfoResponse.data!.verificationType ==
        IdVerificationTypeEnum.BUSINESS.name) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(widget.singleStoreInfoResponse.data!.gstNumber ?? "",
              style:
                  AppTextStyle.heading2Medium(textColor: AppColors.appBlack)),
          horizontalSizedBox(5),
          AppToolTip(
            message: widget.singleStoreInfoResponse.data!.isGstVerified!
                ? AppStrings.verifiedBySwadesic
                : AppStrings.requestedVerification,
            toolTipWidget: SvgPicture.asset(
              widget.singleStoreInfoResponse.data!.isGstVerified!
                  ? AppImages.idVerified
                  : AppImages.idVerificationPending,
              height: CommonMethods.textHeight(
                  context: context,
                  textStyle:
                      AppTextStyle.heading2Medium(textColor: AppColors.appBlack)
                          .copyWith(height: 1.0)),
              width: CommonMethods.textHeight(
                  context: context,
                  textStyle:
                      AppTextStyle.heading2Medium(textColor: AppColors.appBlack)
                          .copyWith(height: 1.0)),
              color: widget.singleStoreInfoResponse.data!.isGstVerified!
                  ? AppColors.brandBlack
                  : AppColors.appBlack,
            ),
          ),
        ],
      );
    }

    //If PAN number is not null
    if (widget.singleStoreInfoResponse.data!.verificationType ==
        IdVerificationTypeEnum.INDIVIDUAL.name) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            "XXXXXXXXXX",
            style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack)
                .copyWith(height: 1.0),
          ),
          horizontalSizedBox(5),
          AppToolTip(
            message: widget.singleStoreInfoResponse.data!.isPanVerified!
                ? AppStrings.verifiedBySwadesic
                : AppStrings.requestedVerification,
            toolTipWidget: SvgPicture.asset(
              widget.singleStoreInfoResponse.data!.isPanVerified!
                  ? AppImages.idVerified
                  : AppImages.idVerificationPending,
              height: CommonMethods.textHeight(
                  context: context,
                  textStyle:
                      AppTextStyle.heading2Medium(textColor: AppColors.appBlack)
                          .copyWith(height: 1.0)),
              width: CommonMethods.textHeight(
                  context: context,
                  textStyle:
                      AppTextStyle.heading2Medium(textColor: AppColors.appBlack)
                          .copyWith(height: 1.0)),
              color: widget.singleStoreInfoResponse.data!.isPanVerified!
                  ? AppColors.brandBlack
                  : AppColors.appBlack,
            ),
          ),
        ],
      );
    }

    return const SizedBox();
  }
//endregion

//region Verified as store
  Widget verifiedAsBusiness() {
    //If verification type is gst
    if (widget.singleStoreInfoResponse.data!.isGstVerified!) {
      return Container(
          margin: const EdgeInsets.only(top: 15),
          child: const DigitalStoreWidget(
            isVisible: true,
            message: "Verified as Business",
          ));
    }

    //If PAN number is not null
    if (widget.singleStoreInfoResponse.data!.isPanVerified!) {
      return Container(
          margin: const EdgeInsets.only(top: 15),
          child: const DigitalStoreWidget(
            isVisible: true,
            message: "Verified as Individual",
          ));
    }

    return const SizedBox();
  }
//endregion
}
