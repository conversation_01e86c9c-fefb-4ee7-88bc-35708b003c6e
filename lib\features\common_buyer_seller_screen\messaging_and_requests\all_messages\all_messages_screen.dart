import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/all_messages_bloc.dart';
import 'package:swadesic/features/widgets/messaging_cards/all_messages_card.dart';
import 'package:swadesic/model/messaging_response/all_message_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AllMessagesScreen extends StatefulWidget {
  const AllMessagesScreen({Key? key}) : super(key: key);

  @override
  State<AllMessagesScreen> createState() => _AllMessagesScreenState();
}

class _AllMessagesScreenState extends State<AllMessagesScreen> {
  late AllMessagesBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = AllMessagesBloc(context);
    bloc.init();
  }

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      body: body(),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.messaging,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  //region Body
  Widget body() {
    return Column(
      children: [
        // searchField(),
        const SizedBox(height: 20),
        Expanded(child: messagesList()),
      ],
    );
  }

  //endregion

  //region Search field
  Widget searchField() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      child: AppSearchField(
        textEditingController: TextEditingController(),
        focusNode: FocusNode(),
        isActive: true,
        hintText: AppStrings.startChat,
        onChangeText: (value) {},
        onTapSuffix: () {},
        onSubmit: () {},
      ),
    );
  }

  //endregion

  //region Messages list
  Widget messagesList() {
    return StreamBuilder<AllMessagesState>(
      stream: bloc.allMessagesStateCtrl.stream,
      initialData: AllMessagesState.Loading,
      builder: (context, snapshot) {
        //Loading
        if (snapshot.data == AllMessagesState.Loading) {
          return AppCommonWidgets.appCircularProgress();
        }
        //Empty
        if (snapshot.data == AllMessagesState.Empty) {
          return AppCommonWidgets.errorWidget(
              onTap: () {}, errorMessage: "No messages yet.");
        }
        //Success
        if (snapshot.data == AllMessagesState.Success) {
          return RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              bloc.init();
            },
            child: ListView.builder(
                controller: bloc.scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                itemCount: bloc.allMessageResponse.data!.dmList!.length +
                    bloc.allMessageResponse.data!.recommendations!.length,
                itemBuilder: (context, index) {
                  //Total messages list
                  List<DirectMessageDetail> dmmMessages = [];

                  dmmMessages.addAll(bloc.allMessageResponse.data!.dmList!);
                  dmmMessages
                      .addAll(bloc.allMessageResponse.data!.recommendations!);
                  DirectMessageDetail directMessageDetail = dmmMessages[index];
                  return AllMessagesCard(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    onTap: () {
                      bloc.goToMessageDetail(
                          toEntityReference:
                              directMessageDetail.entityReference!);
                    },
                    time: directMessageDetail.lastMessage == null
                        ? ""
                        : bloc.formatDateTime(
                            directMessageDetail.lastMessage!.timestamp!),
                    reference: directMessageDetail.entityReference!,
                    subTitleTextStyle: directMessageDetail.lastMessage == null
                        ? AppTextStyle.subTitle0regular(
                            textColor: AppColors.appBlack)
                        : directMessageDetail.unreadCount! == 0
                            ? AppTextStyle.subTitle0regular(
                                textColor: AppColors.appBlack)
                            : AppTextStyle.subTitle0(
                                textColor: AppColors.brandBlack),
                    subTitle: directMessageDetail.lastMessage == null
                        ? "Send new message"
                        : directMessageDetail.unreadCount! == 0
                            ? directMessageDetail.lastMessage!.text!
                            : CommonMethods.singularPluralText(
                                item: directMessageDetail.unreadCount!,
                                singular: "unseen message",
                                plural: "unseen messages"),
                    title: directMessageDetail.handle!,
                    imageUrl: directMessageDetail.icon,
                  );
                }),
          );
        }
        //Failed
        return AppCommonWidgets.errorWidget(onTap: () {
          bloc.init();
        });
      },
    );
  }
//endregion
}
