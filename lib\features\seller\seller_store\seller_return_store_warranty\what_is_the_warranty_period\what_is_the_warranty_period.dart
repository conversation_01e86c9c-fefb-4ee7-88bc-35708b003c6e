// import 'package:flutter/material.dart';
// import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/do_your_product_have_warranty/do_your_product_have_warranty_bloc.dart';
// import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_common_widgets.dart';
// import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_bloc.dart';
// import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/what_is_the_warranty_period/what_is_the_warranty_period_bloc.dart';
// import 'package:swadesic/model/seller_return_warranty_response/return_warranty_data_model/return_warranty_data_model.dart';
// import 'package:swadesic/util/app_colors.dart';
// import 'package:swadesic/util/app_constants.dart';
// import 'package:swadesic/util/common_widgets.dart';
//
// class WhatIsTheWarrantyPeriod extends StatefulWidget {
//   final SellerReturnStoreWarrantyBloc sellerReturnStoreWarrantyBloc;
//   const WhatIsTheWarrantyPeriod({Key? key, required this.sellerReturnStoreWarrantyBloc}) : super(key: key);
//   @override
//   State<WhatIsTheWarrantyPeriod> createState() => _WhatIsTheWarrantyPeriodState();
// }
//
// class _WhatIsTheWarrantyPeriodState extends State<WhatIsTheWarrantyPeriod> {
//   //region Bloc
//   late WhatIsTheWarrantyPeriodBloc whatIsTheWarrantyPeriodBloc;
//   //endregion
//
//   //region init
//   @override
//   void initState() {
//     whatIsTheWarrantyPeriodBloc = WhatIsTheWarrantyPeriodBloc(context,widget.sellerReturnStoreWarrantyBloc);
//     whatIsTheWarrantyPeriodBloc.init();
//     super.initState();
//   }
//   //endregion
//
//
//   @override
//   Widget build(BuildContext context) {
//     return  warrantyPeriod();
//   }
//
// //region Warranty period
//   Widget warrantyPeriod(){
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       mainAxisAlignment: MainAxisAlignment.center,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         verticalSizedBox(20),
//         ReturnWarrantyCommonWidget.title(title:"What is the warranty period?"),
//         verticalSizedBox(10),
//         Row(
//           mainAxisSize: MainAxisSize.min,
//           mainAxisAlignment: MainAxisAlignment.center,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Expanded(
//               // child:TextFormField(
//               //   controller: whatIsTheWarrantyPeriodBloc.warrantyPeriodTextCtrl,
//               //   onChanged: (value){
//               //     whatIsTheWarrantyPeriodBloc.onChangeTextField();
//               //   },
//               // )
//               child: underLineTextField(
//                 context: context,
//                 textFieldCtrl:whatIsTheWarrantyPeriodBloc.warrantyPeriodTextCtrl,
//                 textFieldHint: "",
//                 textFieldMaxLine: 1,
//                 keyboardType: TextInputType.text,
//                 textInputAction: TextInputAction.done,
//
//                 onChangeText: (){
//                   whatIsTheWarrantyPeriodBloc.onChangeTextField();
//                 }
//
//               ),
//             ),
//             horizontalSizedBox(20),
//             Container(
//               padding: const EdgeInsets.symmetric(horizontal:8),
//               height: 43,
//               decoration: const BoxDecoration(
//                   color: AppColors.lightestGrey,
//                   borderRadius: BorderRadius.all(Radius.circular(5))
//               ),
//               child: DropdownButton<String>(
//                 isExpanded: false,
//                 underline: const SizedBox(),
//                 value: ReturnWarrantyDataModel.warrantyPeriod,
//                 items:whatIsTheWarrantyPeriodBloc.warrantyPeriodTypeList.map<DropdownMenuItem<String>>((String value){
//                   return DropdownMenuItem(
//                       value: value,
//                       child:Text(value));
//
//                 }).toList(),
//                 onChanged: (value){
//                   whatIsTheWarrantyPeriodBloc.onchangeWarrantyPeriodType(data: value!);
//                 },
//               ),
//             ),
//             const Expanded(child: SizedBox())
//
//           ],
//         ),
//       ],
//     );
//   }
// //endregion
//
// }
