import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/shipping_in_progress/shipping_in_progress_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class ShippingInProgressScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order ;
  const ShippingInProgressScreen({Key? key, required this.suborderList, required this.buyerSubOrderBloc, required this.order}) : super(key: key);

  @override
  State<ShippingInProgressScreen> createState() => _ShippingInProgressScreenState();
}

class _ShippingInProgressScreenState extends State<ShippingInProgressScreen> {


  // region Bloc
  late ShippingInProgressBloc shippingInProgressBloc;

  // endregion
  // region Init
  @override
  void initState() {
    shippingInProgressBloc = ShippingInProgressBloc(context,widget.buyerSubOrderBloc,widget.order,widget.suborderList);
    shippingInProgressBloc.init();
    super.initState();
  }
  // endregion
  //region Dis update
  @override
  void didUpdateWidget(covariant ShippingInProgressScreen oldWidget) {
    shippingInProgressBloc = ShippingInProgressBloc(context,widget.buyerSubOrderBloc,widget.order,widget.suborderList);
    shippingInProgressBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion


  //region Build

  //region Build
  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion


  //region Body
  Widget body(){
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount:shippingInProgressBloc.groupNameList.length,
        itemBuilder: (context,index){
          return shippingInProgress(groupName: shippingInProgressBloc.groupNameList[index]);
        });
  }
  //endregion



  //region Shipping in progress heading and all
  Widget shippingInProgress({required String groupName}){
    List<SubOrder> groupedSuborderList = [];
    ///Add all suborders to the suborder list as per the display package number
    groupedSuborderList = shippingInProgressBloc.subOrderList.where((element) =>element.displayPackageNumber==groupName).toList();
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(
              bottom: BorderSide(
                  color: AppColors.lightStroke
              )
          )
      ),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(headerOrderList:groupedSuborderList,groupName: groupName ),
        //endregion
        collapsed: trackAndCancel(groupedSubOrders:groupedSuborderList ),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            trackAndCancel(groupedSubOrders: groupedSuborderList),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: groupedSuborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Column(
                          children: [
                            productInfoCard(context: context, subOrder: groupedSuborderList[index]),

                            //Cancel
                            Row(
                              children: [
                                AppCommonWidgets.subOrderButton(
                                    buttonName: AppStrings.cancel,
                                    onTap: () {
                                      // //Mark only selected
                                      shippingInProgressBloc.subOrderList[index].isSelected = true;
                                      // //Open bottom sheet
                                      shippingInProgressBloc.onTapCancel(subOrders: shippingInProgressBloc.subOrderList);
                                      // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                                    },
                                    horizontalPadding: 25),
                              ],
                            ),
                            verticalSizedBox(10)

                          ],
                        ),
                        //Divider
                        Visibility(
                          visible: groupedSuborderList.length-1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );

  }

//endregion



  //region Header
  Widget header({required  List<SubOrder> headerOrderList,required String groupName}) {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.packageIcon,
      componentName: "${AppStrings.shippingInProgress} :$groupName",
      suborderList: headerOrderList,
      additionalWidgets: const SizedBox()

    );}

  //endregion

  //region Track and cancel
  Widget trackAndCancel({required List<SubOrder> groupedSubOrders}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: AppCommonWidgets.activeButton(
                buttonName: AppStrings.trackThePackage,
                onTap: () {
                  shippingInProgressBloc.onTapTrack(subOrders: groupedSubOrders);
                  // shippingInProgressBloc.onTapDeliveryStatus(groupedSubOrders: groupedSubOrders);
                  // CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.suborderList);
                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                })),
        horizontalSizedBox(10),
        // Expanded(
        //     child: AppCommonWidgets.inActiveButton(
        //         buttonName: AppStrings.cancelOrder,
        //         onTap: () {
        //           shippingInProgressBloc.onTapCancel(subOrders: groupedSubOrders);
        //           // shippingInProgressBloc.onTapTrackingDetail(groupedSubOrders: groupedSubOrders);
        //           // CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.suborderList);
        //           // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
        //         })),
      ],
    );
  }

  //endregion


}
