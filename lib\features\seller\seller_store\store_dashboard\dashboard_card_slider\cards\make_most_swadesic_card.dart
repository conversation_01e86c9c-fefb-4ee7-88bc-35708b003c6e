import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class MakeMostSwadesicCard {
  static Widget buildTitle() {
    return Text(
      "Make the Most of Swadesic",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }

  static Widget buildContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            "See all the ways Swadesic can work for you — explore every feature and benefit for your store.",
            style: AppTextStyle.smallTextRegular(textColor: AppColors.appBlack),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Container(
          padding: const EdgeInsets.all(5),
          child: Icon(Icons.arrow_forward, color: AppColors.appBlack),
        ),
      ],
    );
  }

  static void showBottomSheet(BuildContext context) {
    CommonMethods.appMinimumBottomSheets(
      bottomSheetName: "Make Most Out of Swadesic",
      screen: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),
              _buildNumberedItem(1,
                  "Complete store profile (logo, cover images, store description, accurate location in Trust center)."),
              _buildNumberedItem(2,
                  "List your products with high-quality images & benefit-driven descriptions. Listing at least 10–15 products is recommended. Add variants wherever possible. Make sure to add product codes for easy sharing with buyers."),
              _buildNumberedItem(3,
                  "Tag products in posts and post regular updates in store feed."),
              _buildNumberedItem(4,
                  "Invite your existing network to join your Store community and support your store."),
              _buildNumberedItem(5,
                  "Share your store link on all social media, WhatsApp, and offline signage. When sharing products, mention product codes and product links."),
              _buildNumberedItem(6,
                  "Grow community by adding more supporters inside Swadesic by inviting your existing network. Start unifying your efforts by using Swadesic storelink everywhere."),
              _buildNumberedItem(
                  7, "Use product codes for easy sharing with buyers."),
              _buildNumberedItem(8,
                  "Request external reviews from past buyers & gather internal reviews for Swadesic orders."),
              _buildNumberedItem(9,
                  "Respond to orders instantly and keep store active with fresh listings."),
              _buildNumberedItem(10,
                  "Qualify the criteria for public discovery and get discovered beyond your existing network. If you already have 10k+ community, apply for public discovery immediately."),
            ],
          ),
        ),
      ),
      context: context,
      heightPercentage: 0.80, // 95% of screen height
    );
  }

  // Helper widget for numbered list items within bottom sheet
  static Widget _buildNumberedItem(int number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "$number.",
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }
}
