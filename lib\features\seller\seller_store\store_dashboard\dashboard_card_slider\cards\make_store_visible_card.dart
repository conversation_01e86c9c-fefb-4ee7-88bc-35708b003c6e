import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class MakeStoreVisibleCard {
  static Widget buildTitle() {
    return Text(
      "Make Your Store Visible",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }

  static Widget buildContent({VoidCallback? onTap}) {
    return Row(
      children: [
        Expanded(
          child: Text(
            "Activate your store to make it visible to customers with your StoreLink.",
            style: AppTextStyle.smallTextRegular(textColor: AppColors.appBlack),
          ),
        ),
        // Go button
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.brandBlack,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              "Go",
              style: AppTextStyle.access1(textColor: AppColors.appWhite),
            ),
          ),
        ),
      ],
    );
  }
}
