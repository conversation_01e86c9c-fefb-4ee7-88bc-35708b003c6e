import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/seller_trust_center_response/get_seller_trust_center_response.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/services/seller_trust_center_service/seller_trust_center_service.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum IdVerificationState { Loading, Success, Failed, Empty }

enum IdVerificationType { Loading, Success, Failed, Empty }

class IdVerificationBloc {
  // region Common Variables
  BuildContext context;

  ///Single store info
  late SingleStoreInfoResponse singleStoreInfoResponse;
  late SingleStoreInfoServices singleStoreInfoServices;
  IdVerificationTypeEnum idVerificationType = IdVerificationTypeEnum.BUSINESS;
  late GetSellerTrustCenterDetailResponse getSellerTrustCenterDetailResponse  = GetSellerTrustCenterDetailResponse();

  bool isChanged = false;
  File? signature;

  // endregion

  //region Text controller

  //endregion

  //region Controller
  final getVerificationStateCtrl = StreamController<IdVerificationState>.broadcast();

  //endregion

  // region | Constructor |
  IdVerificationBloc(this.context);

  // endregion

  // region Init
  init() async {
    singleStoreInfoServices = SingleStoreInfoServices();
    getSingleStoreInfo();
  }

// endregion

  //region Get Store Info Api call
  Future<void> getSingleStoreInfo() async {
    try {
      //Loading
      getVerificationStateCtrl.sink.add(IdVerificationState.Loading);
      // storeDashboardCtrl.sink.add(StoreDashBoardState.Loading);
      singleStoreInfoResponse = await singleStoreInfoServices.getSingleStoreInfo(AppConstants.appData.storeReference!);
      getSellerTrustCenterDetailResponse = await SellerTrustCenterService().getTrustCenterDetails(AppConstants.appData.storeReference!);
      //Success
      getVerificationStateCtrl.sink.add(IdVerificationState.Success);
      //Add verification type
      idVerificationType = singleStoreInfoResponse.data!.verificationType! == IdVerificationTypeEnum.BUSINESS.name
          ? IdVerificationTypeEnum.BUSINESS
          : IdVerificationTypeEnum.INDIVIDUAL;
      //Refresh ui
      getVerificationStateCtrl.sink.add(IdVerificationState.Success);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion

  //region Update GST
  Future<void> updateGst({required String gstNumber, required String businessName}) async {
    //Close keyboard
    CommonMethods.closeKeyboard(context);
    //Check text field
    if (gstNumber.length != 15) {
      return CommonMethods.toastMessage(AppStrings.pleaseEnterValidGst, context);
    }
    try {
      //Loading
      // gstStateCtrl.sink.add(GstState.Loading);
      // storeDashboardCtrl.sink.add(StoreDashBoardState.Loading);
      //Body
      var body = {
        "gst_number": gstNumber.toUpperCase(),
        "gst_business_name": businessName,
        "verification_type": IdVerificationTypeEnum.BUSINESS.name,
      };

      await singleStoreInfoServices.addGstAdnPan(gstAndPanBody: body, storeRef: AppConstants.appData.storeReference!);
      await getSingleStoreInfo();
      //Message that gst number is saved
      CommonMethods.toastMessage(AppStrings.yourVerificationWillCompleteInNext, context, toastShowTimer: 4);
      // gstStateCtrl.sink.add(GstState.Success);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion

  //region Update PAN
  Future<void> updatePan({required String panNumber, required String nameOnPan}) async {
    //Close keyboard
    CommonMethods.closeKeyboard(context);
    //Check pan number  field
    if (panNumber.split("").length != 10) {
      return CommonMethods.toastMessage(AppStrings.pleaseEnterValidPan, context);
    }
    //Check name on pan
    if (nameOnPan.isEmpty) {
      return CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
    }
    //If no signature is selected from gallery or drawn
    if (signature == null) {
      return CommonMethods.toastMessage(AppStrings.pleaseAddSignature, context);
    }
    try {
      //Loading
      // gstStateCtrl.sink.add(GstState.Loading);
      // storeDashboardCtrl.sink.add(StoreDashBoardState.Loading);
      //Body
      var body = {
        "pan_number": panNumber.toUpperCase(),
        "pan_name": nameOnPan,
        "verification_type": IdVerificationTypeEnum.INDIVIDUAL.name,
      };

      await singleStoreInfoServices.addGstAdnPan(gstAndPanBody: body, storeRef: AppConstants.appData.storeReference!);
      await addAndEditSignature();
      await getSingleStoreInfo();
      //Message that gst number is saved
      CommonMethods.toastMessage(AppStrings.yourVerificationWillCompleteInNext, context, toastShowTimer: 4);
      // gstStateCtrl.sink.add(GstState.Success);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion

  //region Update verification type
  Future<void> updateVerificationType() async {
    try {
      //Loading
      // gstStateCtrl.sink.add(GstState.Loading);
      // storeDashboardCtrl.sink.add(StoreDashBoardState.Loading);
      //Body
      var body = {
        "verification_type": idVerificationType.name,
      };

      await singleStoreInfoServices.addGstAdnPan(gstAndPanBody: body, storeRef: AppConstants.appData.storeReference!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted ? CommonMethods.toastMessage(error.message!, context) : null;
    } catch (error) {
      //print(error);
      return;
    }
  }

//endregion

  //region Add and edit signature
  Future<void> addAndEditSignature() async {
    try {
      //If first time signature is being add then call add signature api
      if (singleStoreInfoResponse.data!.storeSignature == null) {
        await UploadFileService().addStoreSignature(filePath: signature!.path, fileNameWithExtension: signature!.path.split("/").last);
      }
      //Else call edit signature api
      else {
        await UploadFileService().editStoreSignature(filePath: signature!.path, fileNameWithExtension: signature!.path.split("/").last);
      }
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return; // ScaffoldMessenger.of(context).showSnackBar(snackBar);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return; // ScaffoldMessenger.of(context).showSnackBar(snackBar);
    }
  }

//endregion

//region On change verification type
  void onChangeVerificationType({required String type}) {
    //Update verification type
    idVerificationType = type == IdVerificationTypeEnum.BUSINESS.name ? IdVerificationTypeEnum.BUSINESS : IdVerificationTypeEnum.INDIVIDUAL;

    //if both gst and pan data re filled then make the verification type to auto update
    if (singleStoreInfoResponse.data!.gstNumber != null && singleStoreInfoResponse.data!.panNumber != null) {
      // updateVerificationType();
    }

    //Is changed
    isChanged = true;
    //Refresh ui
    getVerificationStateCtrl.sink.add(IdVerificationState.Success);
  }
//endregion


//region Request for save verification type
   requestForSaveVerificationType() {
    //Else if Business is selected then check gst files are fill or not
    if (idVerificationType == IdVerificationTypeEnum.BUSINESS) {
      if (singleStoreInfoResponse.data!.gstNumber != null) {
        updateVerificationType();
        Navigator.pop(context);

      } else {
        CommonMethods.toastMessage(AppStrings.pleaseEnterValidGst, context,toastShowTimer: 5);
      }
    }
    //Else if Individual is selected then check gst files are fill or not
    else if (idVerificationType == IdVerificationTypeEnum.INDIVIDUAL) {
      if (singleStoreInfoResponse.data!.panNumber != null && singleStoreInfoResponse.data!.panName != null) {
        updateVerificationType();
        Navigator.pop(context);
      } else {
        CommonMethods.toastMessage(AppStrings.pleaseEnterValidPan, context,toastShowTimer: 5);
      }
    }
  }
//endregion

//region On tap back
void onTapBack(){
  //Check is anything change
  if(isChanged){
    CommonMethods.appDialogBox(
        context: context,
        widget: SaveOrDiscard(
          isMessageVisible: true,
          message: AppStrings.yourChangesIsNotYetSaved,

          onTapSave:(v){
            requestForSaveVerificationType();
          },previousScreenContext: context,)
    );
  }
  else{
    Navigator.pop(context);
  }
}
//endregion
}
