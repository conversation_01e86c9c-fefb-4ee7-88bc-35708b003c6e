import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/widget/return_pickup_details/return_pickup_details_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/shipping_history/shipping_history_screen.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:url_launcher/url_launcher.dart';

class ReturnPickupDetail extends StatefulWidget {
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  final String packageNumber;

  const ReturnPickupDetail({
    Key? key,
    required this.suborderList,
    required this.buyerSubOrderBloc,
    required this.order,
    required this.packageNumber,
  }) : super(key: key);

  @override
  State<ReturnPickupDetail> createState() => _ReturnPickupDetailState();
}

class _ReturnPickupDetailState extends State<ReturnPickupDetail> {
  //region Bloc
  late ReturnPickupDetailsBloc returnPickupDetailsBloc;
  //endregion

  //region Init
  @override
  void initState() {
    returnPickupDetailsBloc = ReturnPickupDetailsBloc(
        context, widget.packageNumber, widget.buyerSubOrderBloc, widget.order);
    returnPickupDetailsBloc.init();
    super.initState();
  }
  //endregion

  @override
  void dispose() {
    returnPickupDetailsBloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ReturnPickupDetailState>(
      stream: returnPickupDetailsBloc.returnPickupDetailCtrl.stream,
      builder: (context, snapshot) {
        if (snapshot.data == ReturnPickupDetailState.Success) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: SingleChildScrollView(child: body()),
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  //region Body
  Widget body() {
    return Column(
      children: [
        ShippingHistoryScreen(
          dropDownTitle: "Return update history",
          pNumber: widget.packageNumber,
          orderNumber: widget.order.orderNumber!,
          isSeller: false,
        ),
        verticalSizedBox(20),
        pickupOptions(),
        AppCommonWidgets.bottomListSpace(context: context),
      ],
    );
  }
  //endregion

  //region Pickup options
  Widget pickupOptions() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Select product return method
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            appText("Return Pickup Details",
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: AppColors.writingColor2,
                maxLine: 3,
                fontFamily: AppConstants.rRegular),
            horizontalSizedBox(10),
          ],
        ),
        verticalSizedBox(20),
        //Logistic and self
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            groupName(),
            verticalSizedBox(20),
            additionalNotes(),
          ],
        )
      ],
    );
  }
  //endregion

  //region Group name
  Widget groupName() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.groupName,
          option: Text(
            returnPickupDetailsBloc.groupName,
            style: TextStyle(color: AppColors.appBlack),
          ),
        ),
        verticalSizedBox(13),

        // Return Method
        AppTitleAndOptions(
          title: "Return Method",
          option: Text(
            returnPickupDetailsBloc.isSelfReturn
                ? "Self Return by Store"
                : "Return by Logistics Partner",
            style: TextStyle(color: AppColors.appBlack),
          ),
        ),
        verticalSizedBox(13),

        // If self return, show return person details
        if (returnPickupDetailsBloc.isSelfReturn) ...[
          AppTitleAndOptions(
            title: "Return Person Name",
            option: Text(
              returnPickupDetailsBloc.returnPersonName.isNotEmpty
                  ? returnPickupDetailsBloc.returnPersonName
                  : "Not provided",
              style: TextStyle(color: AppColors.appBlack),
            ),
          ),
          verticalSizedBox(13),
          AppTitleAndOptions(
            title: "Return Person Contact",
            option: Row(
              children: [
                Expanded(
                  child: Text(
                    returnPickupDetailsBloc.returnPersonContact.isNotEmpty
                        ? "+91 ${returnPickupDetailsBloc.returnPersonContact}"
                        : "Not provided",
                    style: TextStyle(color: AppColors.appBlack),
                  ),
                ),
                if (returnPickupDetailsBloc.returnPersonContact.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      // Copy phone number to clipboard
                      Clipboard.setData(ClipboardData(
                          text:
                              "+91${returnPickupDetailsBloc.returnPersonContact}"));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('Phone number copied to clipboard')),
                      );
                    },
                    child: Text(
                      'Copy',
                      style: TextStyle(color: AppColors.brandBlack),
                    ),
                  ),
              ],
            ),
          ),
          verticalSizedBox(13),
        ],

        // If logistics partner, show logistics details
        if (!returnPickupDetailsBloc.isSelfReturn) ...[
          AppTitleAndOptions(
            title: "Logistics Partner",
            option: Text(
              returnPickupDetailsBloc.logisticPartner.isNotEmpty
                  ? returnPickupDetailsBloc.logisticPartner
                  : "Not assigned",
              style: TextStyle(color: AppColors.appBlack),
            ),
          ),
          verticalSizedBox(13),
          AppTitleAndOptions(
            title: "Tracking Number",
            option: Row(
              children: [
                Expanded(
                  child: Text(
                    returnPickupDetailsBloc.trackingNumber.isNotEmpty
                        ? returnPickupDetailsBloc.trackingNumber
                        : "Not assigned",
                    style: TextStyle(color: AppColors.appBlack),
                  ),
                ),
                if (returnPickupDetailsBloc.trackingNumber.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      // Copy tracking number to clipboard
                      Clipboard.setData(ClipboardData(
                          text: returnPickupDetailsBloc.trackingNumber));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content:
                                Text('Tracking number copied to clipboard')),
                      );
                    },
                    child: Text(
                      'Copy',
                      style: TextStyle(color: AppColors.brandBlack),
                    ),
                  ),
              ],
            ),
          ),
          verticalSizedBox(13),
          AppTitleAndOptions(
            title: "Tracking Link",
            option: InkWell(
              onTap: () async {
                if (returnPickupDetailsBloc.trackingLink.isNotEmpty) {
                  final url = Uri.parse(returnPickupDetailsBloc.trackingLink);
                  if (await canLaunchUrl(url)) {
                    await launchUrl(url, mode: LaunchMode.externalApplication);
                  }
                }
              },
              child: Text(
                returnPickupDetailsBloc.trackingLink.isNotEmpty
                    ? returnPickupDetailsBloc.trackingLink
                    : "Not available",
                style: TextStyle(
                  color: returnPickupDetailsBloc.trackingLink.isNotEmpty
                      ? AppColors.brandBlack
                      : AppColors.appBlack,
                  decoration: returnPickupDetailsBloc.trackingLink.isNotEmpty
                      ? TextDecoration.underline
                      : null,
                ),
              ),
            ),
          ),
          verticalSizedBox(13),
        ],
      ],
    );
  }
  //endregion

  //region Additional Notes
  Widget additionalNotes() {
    return Column(
      children: [
        AppTitleAndOptions(
          title: AppStrings.additionalNotes,
          titleOption: SvgPicture.asset(AppImages.exclamation),
          option: Text(
            returnPickupDetailsBloc.additionalNotes.isNotEmpty
                ? returnPickupDetailsBloc.additionalNotes
                : "No additional notes provided",
            style: TextStyle(color: AppColors.appBlack),
          ),
        ),
        verticalSizedBox(13),
      ],
    );
  }
  //endregion
}
