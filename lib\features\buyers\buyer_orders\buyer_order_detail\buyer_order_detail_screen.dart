import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_details/buyer_my_order_details_screen.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'dart:developer' as developer;

/// Screen to display order details
class BuyerOrderDetailScreen extends StatefulWidget {
  final String orderNumber;

  const BuyerOrderDetailScreen({
    Key? key,
    required this.orderNumber,
  }) : super(key: key);

  @override
  State<BuyerOrderDetailScreen> createState() => _BuyerOrderDetailScreenState();
}

class _BuyerOrderDetailScreenState extends State<BuyerOrderDetailScreen> {
  bool _isLoading = true;
  Order? _order;

  @override
  void initState() {
    super.initState();
    _loadOrderDetails();
  }

  Future<void> _loadOrderDetails() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // For now, we'll create a simple Order object
      // In a real implementation, you would fetch this from an API
      _order = Order(
        orderNumber: widget.orderNumber,
        orderStatus: 'PENDING',
        totalAmount: '0',
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] _loadOrderDetails(): Failed to load order details: ${e.toString()}',
        name: 'BuyerOrderDetailScreen',
        stackTrace: stackTrace
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load order details: ${e.toString()}')),
        );
      }
      
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.orderDetail),
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.appBlack),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return Center(
        child: AppCommonWidgets.appCircularProgress(),
      );
    }

    if (_order == null) {
      return Center(
        child: AppCommonWidgets.errorMessage(error: AppStrings.unableToLoadOrderDetails),
      );
    }

    // Show the order details using the BuyerMyOrderDetailsScreen
    return BuyerMyOrderDetailsScreen(
      orderNumber: widget.orderNumber,
      order: _order!,
    );
  }
}
