import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/scroll_detection_service.dart';

/// Service to manage auto-hiding navigation bars across the app
class AutoHideNavigationService {
  static final AutoHideNavigationService _instance =
      AutoHideNavigationService._internal();
  factory AutoHideNavigationService() => _instance;
  AutoHideNavigationService._internal();

  // Scroll detection service
  final ScrollDetectionService _scrollDetectionService =
      ScrollDetectionService();

  // State management
  bool _isAppBarVisible = true;
  bool _isBottomNavVisible = true;
  bool _isAutoHideEnabled = false;
  ScrollController? _currentScrollController;

  // Stream controllers for UI updates
  final StreamController<bool> _appBarVisibilityController =
      StreamController<bool>.broadcast();
  final StreamController<bool> _bottomNavVisibilityController =
      StreamController<bool>.broadcast();

  // Getters
  bool get isAppBarVisible => _isAppBarVisible;
  bool get isBottomNavVisible => _isBottomNavVisible;
  bool get isAutoHideEnabled => _isAutoHideEnabled;

  Stream<bool> get appBarVisibilityStream => _appBarVisibilityController.stream;
  Stream<bool> get bottomNavVisibilityStream =>
      _bottomNavVisibilityController.stream;

  /// Initialize the service
  void initialize() {
    _setupScrollDetection();
  }

  /// Setup scroll detection callbacks
  void _setupScrollDetection() {
    _scrollDetectionService.onScrollDirectionChanged = (direction, velocity) {
      if (!_isAutoHideEnabled) return;

      // Only hide/show if scrolling with sufficient velocity
      if (velocity.abs() > 100) {
        if (direction == ScrollDirection.down) {
          _hideNavigationBars();
        } else if (direction == ScrollDirection.up) {
          _showNavigationBars();
        }
      }
    };

    _scrollDetectionService.onScrollPositionChanged = (position, maxExtent) {
      if (!_isAutoHideEnabled || _currentScrollController == null) return;

      // Show navigation bars when near top or bottom
      if (_scrollDetectionService.isAtTop(_currentScrollController!) ||
          _scrollDetectionService.isAtBottom(_currentScrollController!)) {
        _showNavigationBars();
      }
    };
  }

  /// Enable auto-hide functionality
  void enableAutoHide() {
    _isAutoHideEnabled = true;
  }

  /// Disable auto-hide functionality
  void disableAutoHide() {
    _isAutoHideEnabled = false;
    _showNavigationBars();
  }

  /// Attach to a scroll controller
  void attachToScrollController(ScrollController scrollController) {
    _currentScrollController = scrollController;
    _scrollDetectionService.attachToScrollController(scrollController);
  }

  /// Detach from a scroll controller
  void detachFromScrollController(ScrollController scrollController) {
    if (_currentScrollController == scrollController) {
      _currentScrollController = null;
    }
    _scrollDetectionService.detachFromScrollController(scrollController);
  }

  /// Hide navigation bars
  void _hideNavigationBars() {
    if (!_isAppBarVisible && !_isBottomNavVisible) return;

    _isAppBarVisible = false;
    _isBottomNavVisible = false;

    _appBarVisibilityController.add(_isAppBarVisible);
    _bottomNavVisibilityController.add(_isBottomNavVisible);

    // Update bottom navigation visibility through existing stream
    AppConstants.bottomNavigationRefreshCtrl.sink.add(false);
  }

  /// Show navigation bars
  void _showNavigationBars() {
    if (_isAppBarVisible && _isBottomNavVisible) return;

    _isAppBarVisible = true;
    _isBottomNavVisible = true;

    _appBarVisibilityController.add(_isAppBarVisible);
    _bottomNavVisibilityController.add(_isBottomNavVisible);

    // Update bottom navigation visibility through existing stream
    AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  }

  /// Force show navigation bars
  void forceShowNavigationBars() {
    _showNavigationBars();
  }

  /// Force hide navigation bars
  void forceHideNavigationBars() {
    _hideNavigationBars();
  }

  /// Reset to initial state
  void reset() {
    _isAppBarVisible = true;
    _isBottomNavVisible = true;
    _currentScrollController = null;
    _scrollDetectionService.reset();

    _appBarVisibilityController.add(_isAppBarVisible);
    _bottomNavVisibilityController.add(_isBottomNavVisible);
    AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  }

  /// Check if scroll position allows hiding
  bool canHideNavigationBars(ScrollController scrollController) {
    if (!scrollController.hasClients) return false;

    return _scrollDetectionService.isInMiddleArea(scrollController);
  }

  /// Handle manual scroll events (for custom implementations)
  void handleScrollEvent(ScrollController scrollController) {
    if (!_isAutoHideEnabled || !scrollController.hasClients) return;

    final currentPosition = scrollController.position.pixels;
    final maxExtent = scrollController.position.maxScrollExtent;
    final minExtent = scrollController.position.minScrollExtent;

    // Don't hide if at the very top or bottom
    if (currentPosition <= minExtent + 50 ||
        currentPosition >= maxExtent - 50) {
      _showNavigationBars();
      return;
    }

    // Use scroll detection service for direction detection
    _scrollDetectionService.handleScrollEvent(scrollController);
  }

  /// Dispose the service
  void dispose() {
    _appBarVisibilityController.close();
    _bottomNavVisibilityController.close();
    _scrollDetectionService.dispose();
  }
}

/// Mixin to easily integrate auto-hide functionality into screens
mixin AutoHideNavigationMixin<T extends StatefulWidget> on State<T> {
  final AutoHideNavigationService _autoHideService =
      AutoHideNavigationService();

  /// Enable auto-hide for this screen
  void enableAutoHideNavigation() {
    _autoHideService.enableAutoHide();
  }

  /// Disable auto-hide for this screen
  void disableAutoHideNavigation() {
    _autoHideService.disableAutoHide();
  }

  /// Attach scroll controller to auto-hide service
  void attachScrollControllerToAutoHide(ScrollController scrollController) {
    _autoHideService.attachToScrollController(scrollController);
  }

  /// Detach scroll controller from auto-hide service
  void detachScrollControllerFromAutoHide(ScrollController scrollController) {
    _autoHideService.detachFromScrollController(scrollController);
  }

  /// Get app bar visibility stream
  Stream<bool> get appBarVisibilityStream =>
      _autoHideService.appBarVisibilityStream;

  /// Get bottom nav visibility stream
  Stream<bool> get bottomNavVisibilityStream =>
      _autoHideService.bottomNavVisibilityStream;

  /// Force show navigation bars
  void forceShowNavigationBars() {
    _autoHideService.forceShowNavigationBars();
  }

  /// Force hide navigation bars
  void forceHideNavigationBars() {
    _autoHideService.forceHideNavigationBars();
  }

  /// Reset auto-hide state
  void resetAutoHideNavigation() {
    _autoHideService.reset();
  }
}
