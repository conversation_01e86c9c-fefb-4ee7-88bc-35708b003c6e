import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:signature/signature.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/id_verification/id_verification_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/id_verification/pan/pan_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/id_verification/pan/signature/my_signature_full_screen.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:ui' as ui;

class SignatureBloc {
  // region Common Variables
  BuildContext context;

  final PanBloc panBloc;
  File? fileFromGalleryOrSignatureToImage;
  late SignatureController signatureController;
  final void Function(File? file) onChangeSignature;

  // endregion

  //region Controller

  final fileFromGalleryOrSignatureToImageCtrl =
      StreamController<File?>.broadcast();

  //endregion

  //region Text editing controller

  //endregion

  // region | Constructor |
  SignatureBloc(this.context, this.panBloc, this.onChangeSignature);

  // endregion

  // region Init
  init() async {
    signatureController = SignatureController(
        penStrokeWidth: 2,
        penColor: Colors.black,
        exportBackgroundColor: AppColors.textFieldFill2,
        onDrawEnd: () {
          //saveDrawnSignatureInLocal();
        });

    signatureFromNetwork();
  }

// endregion

  // SingleStoreInfoServices

  Future<void> signatureFromNetwork() async {
    try {
      if (panBloc.idVerificationBloc.singleStoreInfoResponse.data!
              .storeSignature ==
          null) {
        return;
      }
      String response = await SingleStoreInfoServices().getImageData(
          imageUrl: panBloc.idVerificationBloc.singleStoreInfoResponse.data!
              .storeSignature!);

      // Decode the JSON string to a List<int>
      List<int> byteArray = json.decode(response).cast<int>();

      // Convert the List<int> to Uint8List
      Uint8List uint8List = Uint8List.fromList(byteArray);

      // Get the application's document directory
      final directory = await getApplicationDocumentsDirectory();

      // Create a file path to save the image
      final filePath =
          '${directory.path}/${DateTime.now().toString()}signature.png';

      // Save the image to the file
      final file = File(filePath);
      await file.writeAsBytes(uint8List);
      //Save selectedImage
      fileFromGalleryOrSignatureToImage = file;
      //Return the selected image from gallery
      onChangeSignature(fileFromGalleryOrSignatureToImage);
      //Broadcast selected image
      fileFromGalleryOrSignatureToImageCtrl.sink
          .add(fileFromGalleryOrSignatureToImage);
    } catch (e) {}
  }

  //region Select signature from gallery
  Future<void> selectSignatureFromGallery() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      //Save selectedImage
      fileFromGalleryOrSignatureToImage = File(image.path);
      //Return the selected image from gallery
      onChangeSignature(fileFromGalleryOrSignatureToImage);
      //Broadcast selected image
      fileFromGalleryOrSignatureToImageCtrl.sink
          .add(fileFromGalleryOrSignatureToImage);
    }
  }

  //endregion

  //region On tap clear
  void onTapClear() async {
    //Clear drawing
    signatureController.clear();
    //Save selectedImage
    fileFromGalleryOrSignatureToImage = null;
    //Return the selected image from gallery
    onChangeSignature(fileFromGalleryOrSignatureToImage);
    //Broadcast selected image
    fileFromGalleryOrSignatureToImageCtrl.sink
        .add(fileFromGalleryOrSignatureToImage);
  }
  //endregion

  //region Save drawn signature in local
  Future<void> saveDrawnSignatureInLocal() async {
    //Remove signature from cache
    // Get the actual canvas dimensions to save the entire canvas, not just the drawn area
    final screenSize = MediaQuery.of(context).size;
    // The signature canvas is in landscape mode, so we need the larger dimension as width
    final canvasWidth = screenSize.longestSide.round();
    final canvasHeight = screenSize.shortestSide.round();

    final signature = await signatureController.toImage(
      width: canvasWidth,
      height: canvasHeight,
    );
    if (signature != null) {
      // Get application documents directory
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/${DateTime.now()}signature.png';
      final file = File(path);

      // Convert signature to byte data
      final byteData =
          await signature.toByteData(format: ui.ImageByteFormat.png);
      final buffer = byteData!.buffer.asUint8List();

      // Save the new image file
      await file.writeAsBytes(buffer);
      //Save selectedImage
      fileFromGalleryOrSignatureToImage = file;
      //Return the selected image from gallery
      onChangeSignature(fileFromGalleryOrSignatureToImage);
      //Broadcast selected image
      fileFromGalleryOrSignatureToImageCtrl.sink
          .add(fileFromGalleryOrSignatureToImage);
    }
  }
//endregion

  // region Go to gst verification
  void goToFullScreen() async {
    var screen = MySignatureFullScreen(
      signatureBloc: this,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route)
        .then((value) {
      //If value is not null then save signature
      if (value != null) {
        saveDrawnSignatureInLocal();
      }
      //else clean
      else {
        onTapClear();
      }
    });
  }
// endregion

//region Dispose
  void dispose() {}
//endregion
}
