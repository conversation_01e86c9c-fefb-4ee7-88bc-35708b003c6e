import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_availability/product_availability_locations.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_setting_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/seller_settings_services/seller_delivery_settings_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum ProductAvailabilityState { Loading, Success, Failed, Empty }

class ProductAvailabilityBloc {
  // region Common Variables
  BuildContext context;

  late SellerDeliveryStoreResponse productLevelDeliverySettings;
  final Product product;

  // endregion

  //region Controller
  final productAvailabilityStateCtrl =
      StreamController<ProductAvailabilityState>.broadcast();
  //endregion

  // region | Constructor |
  ProductAvailabilityBloc(this.context, this.product);

  // endregion

  // region Init
  void init() async {}
// endregion

  ///Product level delivery settings
  //region Get Product level Delivery  Settings API Call
  Future<void> getProductLevelDeliverySetting() async {
    //region Try
    try {
      //Loading
      productAvailabilityStateCtrl.sink.add(ProductAvailabilityState.Loading);
      //Api call to get data
      productLevelDeliverySettings = await SellerDeliverySettingsService()
          .getDeliveryStoreSettings(
              storeRef: product.storeReference!,
              productReference: product.productReference);
      //Success
      productAvailabilityStateCtrl.sink.add(ProductAvailabilityState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      productAvailabilityStateCtrl.sink.add(ProductAvailabilityState.Failed);
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      //getDeliveryPinCode();
      //print(error);
      productAvailabilityStateCtrl.sink.add(ProductAvailabilityState.Failed);
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
      return;
    }
  }
  //endregion

  //region Open dialog to show available locations
  Future availableInLocation() {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: AppColors.appWhite,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (_) =>
          ProductAvailabilityLocations(productAvailabilityBloc: this),
    );
  }
  //endregion

  //region Dispose
  void dispose() {
    productAvailabilityStateCtrl.close();
  }
//endregion
}
