import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/explorer/explorer_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';


// region Explorer Screen
class ExplorerScreen extends StatefulWidget {
  const ExplorerScreen({Key? key}) : super(key: key);

  @override
  _ExplorerScreenState createState() => _ExplorerScreenState();
}
// endregion

class _ExplorerScreenState extends State<ExplorerScreen> {
  // region Bloc
  late ExplorerBloc explorerBloc;

  // endregion

  // region Init
  @override
  void initState() {
    explorerBloc = ExplorerBloc(context);
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: Safe<PERSON>rea(child: Column(
        children: [
          searchBar(),
          Expanded(child: topPosts()),
        ],
      )),
    );
  }

  // endregion


  //region Searchbar
  Widget searchBar(){
    return Padding(
      padding:const EdgeInsets.symmetric(horizontal: 15,vertical: 10),
      child: TextFormField(
        onTap: (){
          explorerBloc.onTapSearchField();

        },


      //controller: buyerSearchBloc.searchTextEditingCtrl,



      maxLines: 1,
      //controller: addProductBloc.hashTagsTextCtrl,
       readOnly: true,

      style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: AppColors.appBlack),
      decoration: InputDecoration(
        prefixIcon: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 11.73),
          child: SvgPicture.asset(AppImages.searchBarIcon,fit: BoxFit.contain,color: AppColors.appBlack7,),
        ),
        filled: true,



        contentPadding: const EdgeInsets.all(0),


        fillColor: AppColors.textFieldFill1,


        isDense: true,

        hintText: "search products, stores & your friends",
        hintStyle: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: AppColors.appBlack.withOpacity(0.4)
        ),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(22),
            borderSide: BorderSide.none

        ),
        enabledBorder: OutlineInputBorder(

            borderRadius: BorderRadius.circular(22),
            borderSide: BorderSide.none
        ),



      ),

  ),
    );
  }
  //endregion

  //region Top Posts
  Widget topPosts(){
    return Center(child: StaggeredGridView.countBuilder(
        staggeredTileBuilder: (index){

          if(index % 7 == 0){
            return const StaggeredTile.count(2,2);
          }

          return const StaggeredTile.count(1,1);
        },
        padding: EdgeInsets.zero,
        itemCount: 100,
        mainAxisSpacing: 1,
        crossAxisSpacing: 1,
        crossAxisCount: 3,
        itemBuilder: (BuildContext,index){
          return InkWell(
            onTap: (){
              // homeBloc.currentPosition();

            },
            child: Container(
              //color: Colors.red,
              child: InteractiveViewer(
                  child: Image.network(
                    "https://source.unsplash.com/random?sig=$index",fit: BoxFit.cover,)),),
          );

        }));
  }
//endregion





}
