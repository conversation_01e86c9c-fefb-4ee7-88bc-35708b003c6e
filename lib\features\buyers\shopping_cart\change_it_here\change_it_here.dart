import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/shopping_cart/change_it_here/change_it_here_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Change it here
class ChangeItHere extends StatefulWidget {
  final SecureCheckoutBloc secureCheckoutBloc;
  const ChangeItHere({Key? key, required this.secureCheckoutBloc}) : super(key: key);

  @override
  State<ChangeItHere> createState() => _ChangeItHereState();
}
//endregion

class _ChangeItHereState extends State<ChangeItHere> {
  //region Bloc
  late ChangeItHereBloc changeItHereBloc;
  //endregion
  //region Init
  @override
  void initState() {
    changeItHereBloc = ChangeItHereBloc(context,widget.secureCheckoutBloc);
    changeItHereBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    changeItHereBloc.dispose();
    super.dispose();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: changeItHereBloc.refreshCtrl.stream,
      builder: (context, snapshot) {
        return body();
      }
    );
  }

  //region Body
  Widget body(){
    return youWillBeContactedON();
  }
  //endregion


  //region You will be contacted on
  Widget youWillBeContactedON() {
    return Container(
      alignment: Alignment.centerLeft,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      margin: const EdgeInsets.only(left: 17,right: 17,top: 17),
      decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          border: Border.all(color:AppColors.textFieldFill1)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.secureCheckoutBloc.selectedAddress.phoneNumber!,
            maxLines: 1,
            style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),
            // style: const TextStyle(fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600, fontSize: 14, color: AppColors.appBlack),
          ),

          verticalSizedBox(20),
          InkWell(
            onTap: (){
              changeItHereBloc.onTapChange();
              //secureCheckoutBloc.onTapAddViewAddress();


            },
            child:  Text("change",
              style:AppTextStyle.access0(textColor: AppColors.borderColor0),),
          )
        ],
      ),
    );
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "You will be contacted on ${widget.secureCheckoutBloc.selectedAddress.phoneNumber} if needed",
            style: TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 14, color: AppColors.appBlack),
          ),
          InkWell(
            onTap: () {
              changeItHereBloc.onTapChange();
              //secureCheckoutBloc.onTapAddViewAddress();
            },
            child: Text(
              "change it here",
              style: TextStyle(
                  decoration: TextDecoration.underline,
                  fontFamily: "LatoBold",
                  fontWeight: FontWeight.w700,
                  fontSize: 12,
                  color: AppColors.writingColor2),
            ),
          ),
          // Visibility(
          //     visible: changeItHereBloc.isChangeFieldVisible,
          //     child: changeNumberField())


        ],
      ),
    );
  }

//endregion


//region Change number field
//   Widget changeNumberField() {
//     return Container(
//       width: double.infinity,
//       margin: const EdgeInsets.only(top: 10),
//       padding: const EdgeInsets.all(10),
//       decoration: const BoxDecoration(color: AppColors.lightWhite2, borderRadius: BorderRadius.all(Radius.circular(20))),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Expanded(
//             child: TextFormField(
//               maxLines: 1,
//               minLines: 1,
//               controller:changeItHereBloc.changeNumberTextCtrl,
//               // onChanged: (value) {
//               //   secureCheckoutBloc.cartDetailsResponse.deliveryNotes = value;
//               // },
//               focusNode: ,
//               keyboardType: TextInputType.number,
//               style: TextStyle(fontFamily: "LatoRegular", fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
//               decoration: InputDecoration(
//                   contentPadding: EdgeInsets.zero,
//                   isDense: true,
//                   border: InputBorder.none,
//                   hintText: AppStrings.mobileNumber,
//                   hintStyle: TextStyle(fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600, fontSize: 14, color: AppColors.writingColor2)),
//               inputFormatters: <TextInputFormatter>[
//                 FilteringTextInputFormatter.allow(RegExp(AppConstants.onlyInt)),
//                 LengthLimitingTextInputFormatter(10),
//
//
//               ],
//             ),
//           ),
//           SvgPicture.asset(
//             AppImages.editIcon,
//             fit: BoxFit.fill,
//             color: AppColors.writingColor2,
//           )
//         ],
//       ),
//     );
//   }

//endregion

}
