import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region App web view
class AppWebView extends StatefulWidget {
  final String url;
  final bool showAppBar;

  const AppWebView({Key? key, required this.url, this.showAppBar = true})
      : super(key: key);

  @override
  State<AppWebView> createState() => _AppWebViewState();
}
//endregion

class _AppWebViewState extends State<AppWebView> {
  //region Bloc
  late AppWebViewBloc appWebViewBloc;

  //endregion
  //region Init
  @override
  void initState() {
    //print("Web url is ${widget.url}");
    appWebViewBloc = AppWebViewBloc(context, widget.url);
    appWebViewBloc.init();
    super.initState();
  }

  //endregion
  //region Dispose
  @override
  void dispose() {
    appWebViewBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: widget.showAppBar ? appBar() : null,
        body: SafeArea(child: body()));
  }

  //region Appbar
  AppBar appBar() {
    return AppBar(
      leading: InkWell(
        onTap: () {
          Navigator.pop(context);
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          child: SvgPicture.asset(
            AppImages.close,
            height: 12,
            width: 12,
            color: AppColors.appBlack,
          ),
        ),
      ),
      elevation: 0,
      backgroundColor: AppColors.appWhite,
      actions: [
        myMenuButton(),
      ],
    );
  }
  //endregion

  //region Menu button
  Widget myMenuButton() {
    return PopupMenuButton<int>(
      padding: EdgeInsets.zero,
      icon: SvgPicture.asset(AppImages.drawerIcon, color: AppColors.appBlack),
      itemBuilder: (context) {
        return [
          PopupMenuItem<int>(
            value: 0,
            onTap: () async {
              CommonMethods.openUrl(url: widget.url);
            },
            child: SizedBox(
              width: 150,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(0),
                    child: AppCommonWidgets.menuText(
                      text: AppStrings.openInBrowser,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ];
      },
    );
  }
  //endregion

  //region Body
  Widget body() {
    return Column(
      children: [
        StreamBuilder<int>(
            stream: appWebViewBloc.pageLoadingCtrl.stream,
            initialData: 0,
            builder: (context, snapshot) {
              // //print(snapshot.data!.toDouble());
              return Visibility(
                visible: snapshot.data != 100,
                child: LinearPercentIndicator(
                  barRadius: const Radius.circular(10),
                  padding: EdgeInsets.zero,
                  lineHeight: 5.0,
                  percent: snapshot.data!.toDouble() / 100,
                  backgroundColor: AppColors.tertiaryGreen,
                  progressColor: AppColors.activeGreen,
                ),
              );
            }),
        Expanded(
          child: InAppWebView(
            initialUrlRequest: URLRequest(url: Uri.parse(widget.url)),
            onReceivedServerTrustAuthRequest: (controller, challenge) async {
              //Do some checks here to decide if CANCELS or PROCEEDS
              return ServerTrustAuthResponse(
                  action: ServerTrustAuthResponseAction.PROCEED);
            },
            //Progress
            onProgressChanged: (webViewController, progress) {
              appWebViewBloc.pageLoadingCtrl.sink.add(progress);
            },
          ),
        )
        // webView(),
        //forwardAndBack(),
      ],
    );
  }
//endregion

  //region Web view
  // Widget webView(){
  //   //print(widget.url);
  //   ///If url does not contains https then add
  //   // String webUrl = "";
  //   // // if(!widget.url.contains("https://")){
  //   // //   webUrl = "https://${widget.url}";
  //   // // }
  //   // // else{
  //   // //   webUrl = widget.url;
  //   // // }
  //   // webUrl = widget.url;
  //   // //print(webUrl);
  //   return  Expanded(
  //     child: Stack(
  //       alignment: Alignment.center,
  //       children: [
  //         WebView(
  //           initialUrl: widget.url,
  //           // initialUrl: "https://www.africau.edu/images/default/sample.pdf",
  //           // initialUrl: "https://storage.googleapis.com/gtv-videos-bucket/sample/images/TearsOfSteel.jpg",
  //           // initialUrl: "${AppConstants.baseUrl}/media/feed_back_image/Screenshot_20230918_135533.jpg",
  //           javascriptMode: JavascriptMode.unrestricted,
  //           gestureNavigationEnabled: true,
  //           onPageStarted: (value) {
  //             appWebViewBloc.appWebViewCtrl.sink.add(AppWebViewState.Loading);
  //           },
  //           onPageFinished: (value) async{
  //             var data = await appWebViewBloc.webViewController.getTitle();
  //             //print(data);
  //
  //           },
  //           onWebResourceError: (value) {
  //             appWebViewBloc.appWebViewCtrl.sink.add(AppWebViewState.Failed);
  //             // appWebViewBloc.appWebViewCtrl.sink.add(AppWebViewState.Success);
  //
  //           },
  //           onProgress: (value) {
  //             appWebViewBloc.pageLoadingCtrl.sink.add(value);
  //             // //print(value);
  //           },
  //           onWebViewCreated: (controller){
  //             appWebViewBloc.webViewController = controller;
  //             // appWebViewBloc.webViewController.loadUrl(Uri.dataFromString(paymentWaitingBloc.bankWeb(),mimeType: 'text/html').toString());
  //           },
  //
  //
  //
  //         ),
  //         ///Error
  //         // StreamBuilder<AppWebViewState>(
  //         //   stream: appWebViewBloc.appWebViewCtrl.stream,
  //         //   initialData: AppWebViewState.Loading,
  //         //   builder: (context, snapshot) {
  //         //     //Loading
  //         //     if(snapshot.data == AppWebViewState.Loading){
  //         //       // return Center(child: AppCommonWidgets.appCircularProgress());
  //         //     }
  //         //     //Failed
  //         //     if(snapshot.data == AppWebViewState.Failed){
  //         //       return Container(
  //         //         height: double.infinity,
  //         //         width: double.infinity,
  //         //         color: AppColors.appWhite,
  //         //           child: Center(child: AppCommonWidgets.errorMessage(error: AppStrings.unableToLoadThis)));
  //         //     }
  //         //     //Success
  //         //     if(snapshot.data == AppWebViewState.Success){
  //         //       return const SizedBox();
  //         //     }
  //         //     return const SizedBox();
  //         //   }
  //         // ),
  //       ],
  //     ),
  //   );
  // }
  //endregion

  ///Not in use
//region Forward and back
  Widget forwardAndBack() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      color: AppColors.appWhite,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CupertinoButton(
              onPressed: () {
                appWebViewBloc.webViewController.goBack();
                appWebViewBloc.appWebViewCtrl.sink.add(AppWebViewState.Success);
              },
              child: SvgPicture.asset(AppImages.backButton)),
          CupertinoButton(
              onPressed: () async {
                appWebViewBloc.webViewController.reload();
              },
              child: Icon(
                Icons.refresh,
                color: AppColors.appBlack,
              )),
          CupertinoButton(
              onPressed: () {
                appWebViewBloc.webViewController.goForward();
              },
              child: RotatedBox(
                  quarterTurns: 2,
                  child: SvgPicture.asset(AppImages.backButton))),
        ],
      ),
    );
  }
//endregion
}
