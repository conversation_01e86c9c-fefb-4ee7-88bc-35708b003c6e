import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/pickup_location/pickup_location_bloc.dart';
import 'package:swadesic/model/pickup_location/pickup_location.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class PickupLocationScreen extends StatefulWidget {
  final List<PickupLocation> pickupLocations;
  final Function(List<PickupLocation>) onLocationsUpdated;

  const PickupLocationScreen({
    Key? key,
    required this.pickupLocations,
    required this.onLocationsUpdated,
  }) : super(key: key);

  @override
  _PickupLocationScreenState createState() => _PickupLocationScreenState();
}

class _PickupLocationScreenState extends State<PickupLocationScreen> {
  late PickupLocationBloc pickupLocationBloc;

  @override
  void initState() {
    super.initState();
    pickupLocationBloc = PickupLocationBloc(
        context, widget.pickupLocations, widget.onLocationsUpdated);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        title: "Pickup Preferences",
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                "Store locations for Pickup",
                style: AppTextStyle.heading1Bold(textColor: AppColors.appBlack),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                "Locations will be shared to customers while viewing products",
                style: AppTextStyle.contentText0(
                    textColor: AppColors.writingColor3),
              ),
            ),
            const SizedBox(height: 16),
            StreamBuilder<List<PickupLocation>>(
              stream: pickupLocationBloc.locationsController.stream,
              initialData: widget.pickupLocations,
              builder: (context, snapshot) {
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        "No pickup locations added yet",
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.writingColor3),
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: snapshot.data!.length,
                  itemBuilder: (context, index) {
                    final location = snapshot.data![index];
                    return Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.darkStroke),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  location.locationName,
                                  style: AppTextStyle.heading2Bold(
                                      textColor: AppColors.appBlack),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  "${location.address}, ${location.city}, ${location.state}, Pincode-${location.pincode}",
                                  style: AppTextStyle.contentText0(
                                      textColor: AppColors.writingColor3),
                                ),
                              ],
                            ),
                          ),
                          CupertinoSwitch(
                            value: location.isActive,
                            onChanged: (value) {
                              pickupLocationBloc.toggleLocationStatus(
                                  index, value);
                            },
                            activeColor: AppColors.brandBlack,
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: InkWell(
                onTap: () {
                  pickupLocationBloc.addNewLocation();
                },
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.darkStroke),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add, color: AppColors.brandBlack),
                      const SizedBox(width: 8),
                      Text(
                        "Add a new Pickup location",
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.brandBlack),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    pickupLocationBloc.dispose();
    super.dispose();
  }
}
