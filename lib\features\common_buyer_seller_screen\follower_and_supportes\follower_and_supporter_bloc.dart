import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/no_internet_handle/no_internet_handle.dart';
import 'package:swadesic/util/universal_link/universal_link.dart';


// enum SplashState { Loading, Success, Failed, Empty }

class FollowerAndSupporterBloc {
  // region Common Methods
  BuildContext context;
  final TabController followersAndSupportersTabCtrl;
  final TabController peopleAndStoreTabCtrl;
  //region Controller
  final tabRefreshCtrl = StreamController<bool>.broadcast();
  final refreshTabCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  FollowerAndSupporterBloc(this.context, this.followersAndSupportersTabCtrl, this.peopleAndStoreTabCtrl);
  // endregion

  // region Init
  init() {

    //Tab change listener
    followersAndSupportersTabCtrl.addListener(() {
      refreshTabCtrl.sink.add(true);
    });
    peopleAndStoreTabCtrl.addListener(() {

      refreshTabCtrl.sink.add(true);
      // childTabChange();
    });

  }
  // endregion


  // void childTabChange(){
  //   if (peopleAndStoreTabCtrl.animation!.value >= 0.9) {
  //     // Child tab view has completed sliding, switch to parent tab view
  //     peopleAndStoreTabCtrl.animateTo(followersAndSupportersTabCtrl.index + 1);
  //   }
  // }



  //region On Tap Find your friends
  goToFindYourCustomer() {
    var screen =  FindYourCustomersScreen(
        visibleNext: false,
      title: AppStrings.findYourFriendsOnSwadesic
    );
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion


//region Dispose
  void dispose(){
    // splashStateCtrl.close();

  }
//endregion




}
