import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/return_in_progress/tracking_return_status/track_return_status_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class TrackReturnStatus extends StatefulWidget {
  final List<SubOrder> subOrderList;
  const TrackReturnStatus({
    Key? key,
    required this.subOrderList,
  }) : super(key: key);

  @override
  State<TrackReturnStatus> createState() => _TrackReturnStatusState();
}

class _TrackReturnStatusState extends State<TrackReturnStatus> {
  // region Bloc
  late TrackReturnStatusBloc trackReturnStatusBloc;

  // endregion

  // region Init
  @override
  void initState() {
    trackReturnStatusBloc = TrackReturnStatusBloc(context);
    trackReturnStatusBloc.init();
    super.initState();
  }

  // endregion

  @override
  Widget build(BuildContext context) {
    return Container(
        color: AppColors.appWhite,
        child: StreamBuilder<bool>(
            stream: trackReturnStatusBloc.bottomSheetRefresh.stream,
            builder: (context, snapshot) {
              return ListView(
                children: [
                  Container(
                      margin: const EdgeInsets.all(10),
                      padding: const EdgeInsets.all(10),
                      child: appText("Return update history",
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.writingColor2,
                          maxLine: 5,
                          fontFamily: AppConstants.rRegular)),
                  deliveryStatusBoxList(),
                  verticalSizedBox(40),
                  productDropDown(),
                  moreDetail(),
                  verticalSizedBox(20),
                  inCaseOfDelay(),
                  getAnUpdate(),
                ],
              );
            }));
  }

  //region Delivery status box list
  Widget deliveryStatusBoxList() {
    List<int> dataList = [1, 23, 32, 23];
    return ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: dataList.length,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              deliveryStatusBox(),
              index == dataList.length - 1
                  ? const SizedBox()
                  : Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 2),
                          child: RotatedBox(
                            quarterTurns: 1,
                            child: Container(
                              height: 2,
                              width: 10,
                              color: AppColors.appBlack,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 2),
                          child: RotatedBox(
                            quarterTurns: 1,
                            child: Container(
                              height: 2,
                              width: 10,
                              color: AppColors.appBlack,
                            ),
                          ),
                        )
                      ],
                    )
            ],
          );
        });
  }
  //endregion

//region Delivery status Box
  Widget deliveryStatusBox() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
          borderRadius: BorderRadius.all(Radius.circular(10))),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: SvgPicture.asset(
              AppImages.dot,
              width: 10,
              height: 10,
              color: AppColors.appBlack10,
            ),
          ),
          horizontalSizedBox(5),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                appText("Shipping started",
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appBlack,
                    maxLine: 5,
                    fontFamily: AppConstants.rRegular),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  child: appText(
                      "Update description & details, that goes two lines or so",
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.appBlack,
                      maxLine: 5,
                      fontFamily: AppConstants.rRegular),
                ),
                appText("23-09-2022 11:45 AM",
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.appBlack,
                    maxLine: 1,
                    fontFamily: AppConstants.rRegular),
              ],
            ),
          ),
          //Expanded(child: horizontalSizedBox(10)),
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
              child: SvgPicture.asset(
                AppImages.drawerIcon,
                color: AppColors.appBlack,
              ))
        ],
      ),
    );
  }
//endregion

  //region Product dropdown
  Widget productDropDown() {
    return StreamBuilder<bool>(
        stream: trackReturnStatusBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              ///Drop down
              InkWell(
                onTap: () {
                  trackReturnStatusBloc.onTapProductList();
                },
                child: Container(
                  color: AppColors.lightWhite3,
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      //Grand total
                      appText("Product list (in this group) - ",
                          color: AppColors.writingColor2,
                          fontWeight: FontWeight.w400,
                          fontFamily: AppConstants.rRegular,
                          fontSize: 14,
                          style: FontStyle.italic),
                      Expanded(child: horizontalSizedBox(10)),
                      trackReturnStatusBloc.isProductListDropDownVisible
                          ? RotatedBox(
                              quarterTurns: 2,
                              child: SvgPicture.asset(AppImages.downArrow),
                            )
                          : SvgPicture.asset(AppImages.downArrow)
                    ],
                  ),
                ),
              ),
              verticalSizedBox(10),

              ///List
              Visibility(
                  visible: trackReturnStatusBloc.isProductListDropDownVisible,
                  child: subOrderList()),
            ],
          );
        });
  }
//endregion

//region Sub orders list
  Widget subOrderList() {
    return Container(
      margin: const EdgeInsets.all(5),
      decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.lightGray2,
            width: 1,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(10))),
      child: ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          itemCount: widget.subOrderList.length,
          shrinkWrap: true,
          itemBuilder: (buildContext, index) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                buyerBottomSheetSubOrderDetails(
                  context: context,
                  subOrder: widget.subOrderList[index],
                  subOrderStatus: "confirmed, not yet shipped",
                  isPriceQuantityVisible: false,
                ),
                index == widget.subOrderList.length - 1
                    ? const SizedBox()
                    : divider()
              ],
            );
          }),
    );
  }
//endregion

//region More detail
  Widget moreDetail() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(10),
          margin: const EdgeInsets.all(10),
          child: appText("More details",
              color: AppColors.writingColor2,
              fontWeight: FontWeight.w600,
              fontFamily: AppConstants.rRegular,
              fontSize: 16),
        ),
        moreDetailTitles("Delivery by"),
        verticalSizedBox(10),
        moreDetailValue("24-01-2021"),
        verticalSizedBox(20),
        moreDetailTitles("Delivery service by"),
        verticalSizedBox(10),
        moreDetailValue("Seller"),
        verticalSizedBox(20),
        moreDetailTitles("Delivery personnel details"),
        verticalSizedBox(10),
        moreDetailValue("Kavan Caudari"),
        verticalSizedBox(20),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            moreDetailValue("+91-6363661111"),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
              margin: const EdgeInsets.symmetric(horizontal: 25),
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  borderRadius: BorderRadius.all(Radius.circular(30))),
              child: appText("Call",
                  color: AppColors.appBlack,
                  fontWeight: FontWeight.w400,
                  fontFamily: AppConstants.rRegular,
                  fontSize: 12,
                  maxLine: 1),
            )
          ],
        ),
        verticalSizedBox(20),

        ///Additional note
        moreDetailTitles("Additional notes"),
        verticalSizedBox(10),
        additionalNotes()
      ],
    );
  }
//endregion

//region More detail titles
  Widget moreDetailTitles(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          appText(title,
              color: AppColors.writingColor3,
              fontWeight: FontWeight.w600,
              fontFamily: AppConstants.rRegular,
              fontSize: 16),
          SvgPicture.asset(AppImages.exclamation)
        ],
      ),
    );
  }

//endregion

//region More detail value
  Widget moreDetailValue(String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25),
      child: appText(value,
          color: AppColors.writingColor2,
          fontWeight: FontWeight.w600,
          fontFamily: AppConstants.rRegular,
          fontSize: 15),
    );
  }
//endregion

//region Additional notes
  Widget additionalNotes() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25),
      child: appText(AppStrings.aDeliveryNote,
          color: AppColors.writingColor2,
          fontWeight: FontWeight.w400,
          fontFamily: AppConstants.rRegular,
          fontSize: 14,
          maxLine: 100),
    );
  }
//endregion

//region In case of delay box
  Widget inCaseOfDelay() {
    return InkWell(
      onTap: () {
        trackReturnStatusBloc.onTapDelay();
      },
      child: Container(
        margin: const EdgeInsets.all(10),
        padding: const EdgeInsets.all(10),
        decoration:
            BoxDecoration(border: Border.all(color: AppColors.appBlack)),
        child: Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(80)),
              color: AppColors.lightGray),
          child: appText("In case of delay, tap here",
              color: AppColors.writingColor2,
              fontWeight: FontWeight.w600,
              fontFamily: AppConstants.rRegular,
              fontSize: 14,
              maxLine: 100),
        ),
      ),
    );
  }
//endregion

//region Get an update
  Widget getAnUpdate() {
    return StreamBuilder<bool>(
        stream: trackReturnStatusBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return trackReturnStatusBloc.isUpdateFromSellerVisible
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: appText(
                          "Get an update from your seller if refund is delayed",
                          color: AppColors.writingColor2,
                          fontWeight: FontWeight.w600,
                          fontFamily: AppConstants.rRegular,
                          fontSize: 16,
                          maxLine: 100),
                    ),

                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: appText(AppStrings.possibleReasonForDelay,
                          color: AppColors.writingColor2,
                          fontWeight: FontWeight.w400,
                          fontFamily: AppConstants.rRegular,
                          fontSize: 14,
                          maxLine: 100),
                    ),

                    ///Speak to seller
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      alignment: Alignment.center,
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: BoxDecoration(
                          color: AppColors.brandBlack,
                          borderRadius: BorderRadius.all(Radius.circular(80))),
                      child: appText("Speak with your seller",
                          color: AppColors.appWhite,
                          fontWeight: FontWeight.w700,
                          fontFamily: AppConstants.rRegular,
                          fontSize: 15,
                          maxLine: 100),
                    ),

                    verticalSizedBox(25),

                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: appText(AppStrings.ifYouFeelAfter,
                          color: AppColors.writingColor2,
                          fontWeight: FontWeight.w400,
                          fontFamily: AppConstants.rRegular,
                          fontSize: 14,
                          maxLine: 100),
                    ),

                    ///Additional notes on this
                    Container(
                        margin: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 20),
                        child: appText("Additional notes on this",
                            color: AppColors.appBlack,
                            fontWeight: FontWeight.w600,
                            fontFamily: AppConstants.rRegular,
                            fontSize: 14,
                            maxLine: 100,
                            opacity: 0.7)),

                    ///Text field
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 20, right: 20, bottom: 10),
                      child: colorFilledTextField(
                        context: context,
                        textFieldCtrl: TextEditingController(),
                        hintText: "write..",
                        hintFontSize: 14,
                        textFieldMaxLine: 4,
                        keyboardType: TextInputType.name,
                        textInputAction: TextInputAction.done,
                        // onChangeText: sellerOnBoardingBloc.onTextChange,
                        regExp: AppConstants.onlyStringWithSpace,
                        fieldTextCapitalization: TextCapitalization.words,
                        maxCharacter: 50,
                      ),
                    ),

                    ///Need resolution. Raise ticket for refund
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      alignment: Alignment.center,
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: BoxDecoration(
                          color: AppColors.lightGray,
                          borderRadius: BorderRadius.all(Radius.circular(80))),
                      child: appText("Need resolution. Raise ticket for refund",
                          color: AppColors.writingColor2,
                          fontWeight: FontWeight.w600,
                          fontFamily: AppConstants.rRegular,
                          fontSize: 14,
                          maxLine: 100),
                    ),
                  ],
                )
              : const SizedBox();
        });
  }
//endregion

//region
}
