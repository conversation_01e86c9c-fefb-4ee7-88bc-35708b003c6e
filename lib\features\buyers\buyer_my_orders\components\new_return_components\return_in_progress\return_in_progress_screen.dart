import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/return_in_progress_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ReturnInProgressScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const ReturnInProgressScreen(
      {Key? key,
      required this.suborderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<ReturnInProgressScreen> createState() => _ReturnInProgressScreenState();
}

class _ReturnInProgressScreenState extends State<ReturnInProgressScreen> {
  // region Bloc
  late ReturnInProgressBloc returnInProgressBloc;

  // endregion
  // region Init
  @override
  void initState() {
    returnInProgressBloc = ReturnInProgressBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.suborderList);
    returnInProgressBloc.init();
    super.initState();
  }

  // endregion
  //region Dis update
  @override
  void didUpdateWidget(covariant ReturnInProgressScreen oldWidget) {
    returnInProgressBloc = ReturnInProgressBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.suborderList);
    returnInProgressBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Build

  //region Build
  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

  //region Body
  Widget body() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: returnInProgressBloc.groupNameList.length,
        itemBuilder: (context, index) {
          return returnInProgress(
              groupName: returnInProgressBloc.groupNameList[index]);
        });
  }
  //endregion

  //region Return in progress heading and all
  Widget returnInProgress({required String groupName}) {
    List<SubOrder> groupedSuborderList = [];

    ///Add all suborders to the suborder list as per the display return package number
    groupedSuborderList = returnInProgressBloc.subOrderList
        .where((element) => element.displayReturnPackageNumber == groupName)
        .toList();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header:
            header(headerOrderList: groupedSuborderList, groupName: groupName),
        //endregion
        collapsed: const SizedBox(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: groupedSuborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Column(
                          children: [
                            productInfoCard(
                                context: context,
                                subOrder: groupedSuborderList[index]),
                            verticalSizedBox(10),
                            // Add tracking details button
                            Row(
                              children: [
                                AppCommonWidgets.subOrderButton(
                                    buttonName: "View Return Tracking Details",
                                    onTap: () {
                                      returnInProgressBloc
                                          .onTapReturnTrackingDetail(
                                              groupedSubOrders: [
                                            groupedSuborderList[index]
                                          ]);
                                    },
                                    horizontalPadding: 25),
                                const Expanded(child: SizedBox())
                              ],
                            ),
                            verticalSizedBox(10)
                          ],
                        ),
                        //Divider
                        Visibility(
                          visible: groupedSuborderList.length - 1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }

//endregion

  //region Header
  Widget header(
      {required List<SubOrder> headerOrderList, required String groupName}) {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
        icon: AppImages.packageIcon,
        componentName: "${AppStrings.returnInProgress} :$groupName",
        suborderList: headerOrderList,
        additionalWidgets: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                AppStrings.appreciateYour,
                style: AppTextStyle.contentHeading0(
                    textColor: AppColors.brandBlack),
              ),
            ),
            verticalSizedBox(5),
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                "Return is in progress. You can view the tracking details by clicking the button below.",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ),
          ],
        ),
        isSellerSideEstimatedReturnDeliveryDateShow: true,
        isEstimateDeliveryShow: false);
  }
  //endregion

  //region Product Info Card
  Widget productInfoCard(
      {required BuildContext context, required SubOrder subOrder}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Product name
                  ///Brand and product name
                  RichText(
                    textScaler: MediaQuery.textScalerOf(
                        AppConstants.globalNavigator.currentContext!),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: subOrder.productBrand,
                          style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        TextSpan(
                            text: " ${subOrder.productName}",
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack,
                            )),
                      ],
                    ),
                  ),
                  verticalSizedBox(5),
                  //Price
                  Text(
                    "₹${subOrder.sellingPrice} X ${subOrder.productQuantity} = ₹${subOrder.sellingPrice! * subOrder.productQuantity!}"
                    "${subOrder.suborderFeeDetails!.productLevelDeliveryFee! == 0 ? "" : "    Delivery: ₹${subOrder.suborderFeeDetails!.productLevelDeliveryFee!}"}",
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                  // Return reason if available
                  if (subOrder.returnReason != null &&
                      subOrder.returnReason!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(
                        "Return reason: ${subOrder.returnReason}",
                        style: AppTextStyle.heading3Regular(
                          textColor: AppColors.orange,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
            horizontalSizedBox(20),
            //Product image
            ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                child: Container(
                    color: AppColors.textFieldFill1,
                    height: 60,
                    width: 60,
                    child: extendedImage(
                      "${subOrder.productImage}",
                      customPlaceHolder: AppImages.productPlaceHolder,
                      context,
                      100,
                      100,
                      cache: true,
                    )))
          ],
        ),
      ),
    );
  }
  //endregion

  //region Divider
  Widget divider() {
    return Divider(
      color: AppColors.lightGray,
      height: 1,
      thickness: 1,
    );
  }
  //endregion
}
