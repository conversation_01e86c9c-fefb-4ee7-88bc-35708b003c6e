import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/seller_cancelled_confirmed_order/seller_cancelled_confirmed_order_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class SellerCancelledConfirmedOrder extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const SellerCancelledConfirmedOrder(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<SellerCancelledConfirmedOrder> createState() =>
      _SellerCancelledConfirmedOrderState();
}

class _SellerCancelledConfirmedOrderState
    extends State<SellerCancelledConfirmedOrder> {
  // region Bloc
  late SellerCancelledConfirmedOrderBloc sellerCancelledConfirmedOrderBloc;

  // endregion

  // region Init
  @override
  void initState() {
    sellerCancelledConfirmedOrderBloc = SellerCancelledConfirmedOrderBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.subOrderList);
    sellerCancelledConfirmedOrderBloc.init();
    super.initState();
  }

  // endregion

  //region Dis update
  @override
  void didUpdateWidget(covariant SellerCancelledConfirmedOrder oldWidget) {
    sellerCancelledConfirmedOrderBloc = SellerCancelledConfirmedOrderBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.subOrderList);
    sellerCancelledConfirmedOrderBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Build

  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

  //region Body
  Widget body() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed: onTapHowRefundAmountCalculated(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            onTapHowRefundAmountCalculated(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount:
                      sellerCancelledConfirmedOrderBloc.subOrderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        verticalSizedBox(10),
                        Text(
                            "Cancelled on: ${sellerCancelledConfirmedOrderBloc.subOrderList[index].cancelledDate == null ? '' : CommonMethods.convertStringDateTimeSlashFormat(sellerCancelledConfirmedOrderBloc.subOrderList[index].cancelledDate!)}",
                            maxLines: 2,
                            overflow: TextOverflow.visible,
                            textAlign: TextAlign.left,
                            style: AppTextStyle.heading3Medium(
                              textColor: AppColors.appBlack,
                            )),
                        verticalSizedBox(10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                  "You will receive ₹ ${sellerCancelledConfirmedOrderBloc.subOrderList[index].sellingPrice! * sellerCancelledConfirmedOrderBloc.subOrderList[index].productQuantity!} as the refund amount",
                                  maxLines: 2,
                                  overflow: TextOverflow.visible,
                                  textAlign: TextAlign.left,
                                  style: AppTextStyle.contentHeading0(
                                      textColor: AppColors.appBlack)),
                            ),
                            SvgPicture.asset(
                              AppImages.exclamation,
                              height: 20,
                              color: AppColors.darkGray,
                            )
                          ],
                        ),

                        productInfoCard(
                          context: context,
                          subOrder: sellerCancelledConfirmedOrderBloc
                              .subOrderList[index],
                        ),
                        //Cancel reason
                        //Reason
                        Text(
                          "${AppStrings.reason}: ${sellerCancelledConfirmedOrderBloc.subOrderList[index].cancellationReason ?? " "}",
                          textAlign: TextAlign.left,
                          style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        verticalSizedBox(10),
                        //Divider
                        Visibility(
                          visible: sellerCancelledConfirmedOrderBloc
                                      .subOrderList.length -
                                  1 !=
                              index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.thumbUpIcon,
      componentName: AppStrings.sellerCancelledConfirmedOrder,
      suborderList: sellerCancelledConfirmedOrderBloc.subOrderList,
      isEstimateDeliveryShow: false,
      additionalWidgets: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.sellerCancelledThisOrderAfterConfirmation,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
          verticalSizedBox(5),
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.refundProcessWillStartOurApology,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region On tap how refund calculated
  Widget onTapHowRefundAmountCalculated() {
    return BuyerMyOrderCommonWidgets.howRefundAmountCalculated(
        subOrderList: sellerCancelledConfirmedOrderBloc.subOrderList,
        buyerSubOrderBloc: sellerCancelledConfirmedOrderBloc.buyerSubOrderBloc);
  }

//endregion
}
