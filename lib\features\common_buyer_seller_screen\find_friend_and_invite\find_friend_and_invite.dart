import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class FindFriendAndInvite extends StatefulWidget {
  const FindFriendAndInvite({super.key});

  @override
  _FindFriendAndInviteState createState() => _FindFriendAndInviteState();
}

class _FindFriendAndInviteState extends State<FindFriendAndInvite> {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _startAutoSlide();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoSlide() {
    Timer.periodic(Duration(seconds: 3), (timer) {
      //If context is not mounted ten return from function
      if (!mounted) {
        timer.cancel();
        return;
      }
      //If current page is less than 1 then increment the page else set the page to 0
      if (_currentPage < 1) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }
      // Slowing down the animation by increasing the duration to 800 milliseconds
      _pageController.animateToPage(
        _currentPage,
        duration: Duration(milliseconds: 800), // Adjust this to make it slower
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10),
      // padding: const EdgeInsets.symmetric(horizontal: 15),
      height: CommonMethods.textHeight(
              context: context,
              textStyle: AppTextStyle.access1(textColor: AppColors.appWhite)) +
          16,
      child: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentPage = index;
          });
        },
        children: [
          // Container(
          //   margin: const EdgeInsets.only(right: 10),
          //   child: _buildCard(
          //     title: AppConstants.appData.isUserView!
          //         ? AppStrings.findFriendsAndTheirStores
          //         : "${AppStrings.findYourCustomersOnSwadesic}",
          //     screen: FindYourCustomersScreen(
          //       visibleNext: false,
          //       title: AppStrings.findYourFriendsOnSwadesic,
          //     ),
          //   ),
          // ),
          _buildCard(
              title: AppStrings.inviteForAnAtmaNirvar,
              screen: const CommonReferralPage()),
        ],
      ),
    );
  }

  Widget _buildCard({required String title, required StatefulWidget screen}) {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
        decoration: AppCommonWidgets()
            .borderRoundGrayContainer(context: context)
            .copyWith(color: AppColors.brandBlack),
        child: InkWell(
          onTap: () {
            // var screen = const CommonReferralPage();
            var route = CupertinoPageRoute(builder: (context) => screen);
            Navigator.push(
                AppConstants.userStoreCommonBottomNavigationContext, route);
          },
          child: Row(
            children: [
              SvgPicture.asset(
                AppImages.plusWithUserIcon,
                height: 24,
                color: AppColors.appWhite,
              ),
              const SizedBox(
                width: 10,
              ),
              Flexible(
                child: Text(
                  title,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: AppTextStyle.access1(textColor: AppColors.appWhite),
                ),
              )
            ],
          ),
        ));
  }
}
