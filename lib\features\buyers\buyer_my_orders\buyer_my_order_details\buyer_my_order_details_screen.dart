import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_details/buyer_my_order_details_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_details/product_ordered/product_ordered.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_details/widget/grandTotal.dart';
import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/account_balance/account_balance_transaction/account_balance_order_details/widget/total_amount_to_be_received/total_amount_to_be_received.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class BuyerMyOrderDetailsScreen extends StatefulWidget {
  final String orderNumber;
  final Order order;

  const BuyerMyOrderDetailsScreen({
    Key? key,
    required this.orderNumber,
    required this.order,
  }) : super(key: key);

  @override
  State<BuyerMyOrderDetailsScreen> createState() => _BuyerMyOrderDetailsScreenState();
}

class _BuyerMyOrderDetailsScreenState extends State<BuyerMyOrderDetailsScreen> {
  // region Bloc
  late BuyerMyOrderDetailBloc buyerMyOrderDetailBloc;

  // endregion

  // region Init
  @override
  void initState() {
    //print("visible");
    buyerMyOrderDetailBloc = BuyerMyOrderDetailBloc(context, widget.orderNumber);
    buyerMyOrderDetailBloc.init();
    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    //print("dispose");
    // TODO: implement dispose
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    //
    // return ListView.builder(
    //
    //     itemBuilder:(buldContext,index){
    //       return Container();
    //
    // });

    return StreamBuilder<BuyerMyOrderDetailsState>(
        stream: buyerMyOrderDetailBloc.allOrderDetailCtrl.stream,
        initialData: BuyerMyOrderDetailsState.Loading,
        builder: (context, snapshot) {
          //Success
          if (snapshot.data == BuyerMyOrderDetailsState.Success) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GrandTotal(
                    sellerOrdersDetailsResponse: buyerMyOrderDetailBloc.sellerOrdersDetailsResponse,
                    grandTotalCtrl: StreamController<bool>.broadcast(),
                  ),
                  userInfo(),
                  orderDetail(),
                ],
              ),
            );
          }

          //Loading
          if (snapshot.data == BuyerMyOrderDetailsState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          //Failed
          return AppCommonWidgets.errorWidget(onTap: () {
            buyerMyOrderDetailBloc.init();
          });
        });
  }

  //region User info
  Widget userInfo() {
    return StreamBuilder<UserDetailState>(
        stream: buyerMyOrderDetailBloc.userDetailStateCtrl.stream,
        initialData: UserDetailState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == UserDetailState.Success) {
            return Column(
              children: [
                verticalSizedBox(20),
                //Grand total
                //GrandTotal(sellerAllOrderDetailBloc: sellerAllOrderDetailBloc,sellerOrdersDetailsResponse: sellerAllOrderDetailBloc.sellerOrdersDetailsResponse),
                deliveryAndContact(),
                verticalSizedBox(20),

                notes(),
                verticalSizedBox(20),

                billingInfo(),
                verticalSizedBox(20),
              ],
            );
          }
          if (snapshot.data == UserDetailState.Loading) {
            return Center(
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          return AppCommonWidgets.errorMessage(error: AppStrings.unableToLoadUserInfo);
        });
  }

  //endregion

  //region Order detail
  Widget orderDetail() {
    return Column(
      children: [
        //Grand total
        // GrandTotal(sellerOrdersDetailsResponse:buyerMyOrderDetailBloc.sellerOrdersDetailsResponse,grandTotalCtrl: buyerMyOrderDetailBloc.totalPaidCtrl, ),
        // TotalAmountToBeReceived(sellerOrdersDetailsResponse: buyerMyOrderDetailBloc.sellerOrdersDetailsResponse,),

        ProductOrderedScreen(
          groups: buyerMyOrderDetailBloc.groups,
          // cancelledReturnOrders: sellerAllOrderDetailBloc.sellerOrdersDetailsResponse.data!.cancelledOrReturnedOrder!,
          cancelledReturnOrders: buyerMyOrderDetailBloc.sellerOrdersDetailsResponse.cancelledOrReturnedOrder!,
        ),
        verticalSizedBox(50),
      ],
    );
  }

  //endregion

//region Delivery and contact
  Widget deliveryAndContact() {
    // var deliveryContact = ;
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppDropDown(
            dropDownWidget: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  verticalSizedBox(10),
                  //Name
                  title(buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredPersonName!),
                  verticalSizedBox(10),
                  //Address
                  Visibility(
                    visible: buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredAddress != null,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 5),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                              child: subTitle("${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredAddress},"
                                  "${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredState},"
                                  "${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredCity},"
                                  "${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredPincode}")),
                          copy("${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredAddress},"
                              "${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredState},"
                              "${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredCity},"
                              "${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredPincode}"),
                        ],
                      ),
                    ),
                  ),
                  verticalSizedBox(10),
                  //Number
                  Visibility(
                    visible: buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredContactNumber != null,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 5),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(child: subTitle(buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredContactNumber!)),
                          copy("${buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!.deliveredContactNumber}"),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            initialExpand: true,
            dropDownName: "Delivery & Contact",
            collapsedWidget: const SizedBox()),
      ],
    );
  }

//endregion

  //region Notes
  Widget notes() {
    return StreamBuilder<bool>(
        stream: buyerMyOrderDetailBloc.dropDownCtrl.stream,
        builder: (context, snapshot) {
          var notes = buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!;
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppDropDown(
                  dropDownWidget: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        verticalSizedBox(10),

                        //Seller and delivery notes
                        Visibility(
                          visible: notes.sellerNote!.isNotEmpty || notes.deliveryNote!.isNotEmpty,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              //Seller Notes
                              Visibility(
                                visible: notes.sellerNote!.isNotEmpty,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    //Seller Notes title
                                    title("Seller note"),
                                    verticalSizedBox(10),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 5),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Expanded(child: subTitle(notes.sellerNote ?? "")),
                                          copy(notes.sellerNote ?? ""),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              verticalSizedBox(10),
                              //Delivery note
                              Visibility(
                                visible: notes.deliveryNote!.isNotEmpty,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    //Delivery note title
                                    title("Delivery note"),
                                    verticalSizedBox(10),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 5),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Expanded(child: subTitle(notes.deliveryNote!)),
                                          copy(notes.deliveryNote!),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              verticalSizedBox(10),
                            ],
                          ),
                        ),

                        //No notes provided by customer
                        Visibility(
                            visible: notes.sellerNote!.isEmpty && notes.deliveryNote!.isEmpty,
                            child: title(AppStrings.noOrderProvidedByCustomer))
                      ],
                    ),
                  ),
                  initialExpand: true,
                  dropDownName: "Notes",
                  collapsedWidget: const SizedBox()),

              // //Header
              // InkWell(
              //   onTap: (){
              //     buyerMyOrderDetailBloc.onTapNotes();
              //   },
              //   child: Container(
              //     color: AppColors.lightWhite3,
              //     padding: const EdgeInsets.all(10),
              //     child: Row(
              //       children: [
              //         dropDownTitleCommon("Notes"),
              //         Expanded(child: horizontalSizedBox(10)),
              //         buyerMyOrderDetailBloc.isNotesVisible?const RotatedBox(quarterTurns: 2,child:Icon(Icons.keyboard_arrow_down,weight: 2,),):
              //         const Icon(Icons.keyboard_arrow_down,weight: 2,)
              //       ],
              //     ),
              //
              //   ),
              // ),
              // //Notes
              // Visibility(
              //     visible:buyerMyOrderDetailBloc.isNotesVisible,
              //     child:Padding(
              //       padding: const EdgeInsets.symmetric(horizontal: 15),
              //       child: Column(
              //         mainAxisSize: MainAxisSize.min,
              //         mainAxisAlignment: MainAxisAlignment.center,
              //         crossAxisAlignment: CrossAxisAlignment.start,
              //         children: [
              //           verticalSizedBox(10),
              //
              //           //Seller and delivery notes
              //           Visibility(
              //             visible: notes.sellerNote!=null || notes.deliveryNote!.isNotEmpty ,
              //             child: Column(
              //               mainAxisSize: MainAxisSize.min,
              //               mainAxisAlignment: MainAxisAlignment.center,
              //               crossAxisAlignment: CrossAxisAlignment.start,
              //               children: [
              //                 //Seller Notes
              //                 Visibility(
              //                   visible:notes.sellerNote!=null ,
              //                   child: Padding(
              //                     padding: const EdgeInsets.only(left: 5),
              //                     child: Column(
              //                       mainAxisSize: MainAxisSize.min,
              //                       mainAxisAlignment: MainAxisAlignment.center,
              //                       crossAxisAlignment: CrossAxisAlignment.start,
              //                       children: [
              //                         //Seller Notes title
              //                         title("Seller note"),
              //                         verticalSizedBox(10),
              //                         Row(
              //                           mainAxisSize: MainAxisSize.min,
              //                           mainAxisAlignment: MainAxisAlignment.center,
              //                           crossAxisAlignment: CrossAxisAlignment.center,
              //                           children: [
              //                             Expanded(child: subTitle(notes.sellerNote??"")),
              //                             copy(notes.sellerNote??""),
              //                           ],
              //                         ),
              //                       ],
              //                     ),
              //                   ),
              //                 ),
              //                 verticalSizedBox(10),
              //                 //Delivery note
              //                 Visibility(
              //                   visible:notes.deliveryNote!.isNotEmpty ,
              //                   child: Column(
              //                     mainAxisSize: MainAxisSize.min,
              //                     mainAxisAlignment: MainAxisAlignment.center,
              //                     crossAxisAlignment: CrossAxisAlignment.start,
              //                     children: [
              //                       //Delivery note title
              //                       title("Delivery note"),
              //                       verticalSizedBox(10),
              //                       Padding(
              //                         padding: const EdgeInsets.only(left: 5),
              //                         child: Row(
              //                           mainAxisSize: MainAxisSize.min,
              //                           mainAxisAlignment: MainAxisAlignment.center,
              //                           crossAxisAlignment: CrossAxisAlignment.center,
              //                           children: [
              //                             Expanded(child: subTitle(notes.deliveryNote!)),
              //                             copy(notes.deliveryNote!),
              //
              //                           ],
              //                         ),
              //                       ),
              //                     ],
              //                   ),
              //                 ),
              //                 verticalSizedBox(10),
              //               ],
              //             ),
              //           ),
              //
              //           //No notes provided by customer
              //           Visibility(
              //               visible: notes.sellerNote==null && notes.deliveryNote!.isEmpty ,
              //               child: title(AppStrings.noOrderProvidedByCustomer))
              //         ],
              //       ),
              //     )
              //
              // ),
              // //
            ],
          );
        });
  }

//endregion

  //region Billing info
  Widget billingInfo() {
    return StreamBuilder<bool>(
        stream: buyerMyOrderDetailBloc.dropDownCtrl.stream,
        builder: (context, snapshot) {
          var billingAddressInfo = buyerMyOrderDetailBloc.sellerAllOrderUserDetailResponse.data!;

          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppDropDown(
                  dropDownWidget: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        verticalSizedBox(10),
                        //Name
                        title(billingAddressInfo.billingPersonName!),
                        verticalSizedBox(10),
                        //Address
                        Padding(
                          padding: const EdgeInsets.only(left: 5),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                  child: subTitle(
                                      "${billingAddressInfo.billingAddress},${billingAddressInfo.billingState},${billingAddressInfo.billingCity},${billingAddressInfo.billingPincode}")),
                              copy(
                                  "${billingAddressInfo.billingAddress},${billingAddressInfo.billingState},${billingAddressInfo.billingCity},${billingAddressInfo.billingPincode}"),
                            ],
                          ),
                        ),
                        verticalSizedBox(10),
                        //GSTIN
                        ///Todo un-comment
                        // addressNotesCommon("GSTIN"),
                        // verticalSizedBox(10),
                        //SGT in Data
                        // Padding(
                        //   padding: const EdgeInsets.only(left: 5),
                        //   child: Row(
                        //     mainAxisSize: MainAxisSize.min,
                        //     mainAxisAlignment: MainAxisAlignment.center,
                        //     crossAxisAlignment: CrossAxisAlignment.center,
                        //     children: [
                        //       Expanded(child: addressNotesCommon("55-**********-H-Z-H")),
                        //       copy("55-**********-H-Z-H"),
                        //     ],
                        //   ),
                        // ),
                        // verticalSizedBox(10),
                      ],
                    ),
                  ),
                  initialExpand: true,
                  dropDownName: "Billing address & details",
                  collapsedWidget: const SizedBox()),
            ],
          );
        });
  }

//endregion

  ///Common

  //region Customer activity common
  Widget customerActivityCommon(String value) {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Container(
        decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
            border: Border.all(color: AppColors.lightStroke, width: 1),
            borderRadius: const BorderRadius.all(Radius.circular(20))),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        child: Text(
          value,
          style: TextStyle(fontFamily: "LatoRegular", fontWeight: FontWeight.w400, fontSize: 12, color: AppColors.appBlack),
        ),
      ),
    );
  }

  //endregion

  //region Follow and message common
  Widget followAndMessageCommon({required String text, required Color textColor, required Color backGroundColor, required dynamic onTap}) {
    return Expanded(
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          onTap();
        },
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(color: backGroundColor, borderRadius: const BorderRadius.all(Radius.circular(50))),
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            text,
            style: TextStyle(fontFamily: "LatoBold", fontWeight: FontWeight.w700, fontSize: 15, color: textColor),
          ),
        ),
      ),
    );
  }

  //endregion

//region Title
  Widget title(String text) {
    return Text(text, style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack));
  }

//endregion

  //region Sub title
  Widget subTitle(String text) {
    return Text(text, style: AppTextStyle.contentText0(textColor: AppColors.appBlack));
  } //endregion

//region Copy
  Widget copy(String text) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          CommonMethods.copyText(context, text);
        },
        child: Container(
          decoration: BoxDecoration(color: AppColors.textFieldFill1, borderRadius: BorderRadius.all(Radius.circular(20))),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
          child: Text(
            "copy",
            style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
          ),
        ),
      ),
    );
  }
//endregion
}
