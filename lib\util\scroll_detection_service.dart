import 'dart:async';
import 'package:flutter/material.dart';

/// Enum for scroll direction
enum ScrollDirection {
  up,
  down,
  idle,
}

/// Service for detecting scroll behavior and providing callbacks
class ScrollDetectionService {
  // Scroll state variables
  double _lastScrollPosition = 0.0;
  double _scrollVelocity = 0.0;
  ScrollDirection _currentDirection = ScrollDirection.idle;
  DateTime _lastScrollTime = DateTime.now();
  
  // Configuration
  static const double _velocityThreshold = 50.0; // pixels per second
  static const double _positionThreshold = 5.0; // minimum pixels to consider as scroll
  static const Duration _idleTimeout = Duration(milliseconds: 150);
  
  Timer? _idleTimer;
  
  // Callbacks
  Function(ScrollDirection direction, double velocity)? onScrollDirectionChanged;
  Function(double position, double maxExtent)? onScrollPositionChanged;
  Function()? onScrollStarted;
  Function()? onScrollEnded;
  
  // Getters
  ScrollDirection get currentDirection => _currentDirection;
  double get scrollVelocity => _scrollVelocity;
  double get lastScrollPosition => _lastScrollPosition;
  
  /// Attach the service to a scroll controller
  void attachToScrollController(ScrollController scrollController) {
    scrollController.addListener(() => handleScrollEvent(scrollController));
  }

  /// Detach the service from a scroll controller
  void detachFromScrollController(ScrollController scrollController) {
    scrollController.removeListener(() => handleScrollEvent(scrollController));
  }
  
  /// Handle scroll events and calculate direction/velocity
  void handleScrollEvent(ScrollController scrollController) {
    if (!scrollController.hasClients) return;
    
    final currentPosition = scrollController.position.pixels;
    final maxExtent = scrollController.position.maxScrollExtent;
    final minExtent = scrollController.position.minScrollExtent;
    final currentTime = DateTime.now();
    
    // Calculate position delta and time delta
    final positionDelta = currentPosition - _lastScrollPosition;
    final timeDelta = currentTime.difference(_lastScrollTime).inMilliseconds;
    
    // Calculate velocity (pixels per second)
    if (timeDelta > 0) {
      _scrollVelocity = (positionDelta / timeDelta) * 1000;
    }
    
    // Determine scroll direction
    ScrollDirection newDirection = ScrollDirection.idle;
    if (positionDelta.abs() > _positionThreshold) {
      if (positionDelta > 0) {
        newDirection = ScrollDirection.down;
      } else {
        newDirection = ScrollDirection.up;
      }
      
      // Trigger scroll started callback if transitioning from idle
      if (_currentDirection == ScrollDirection.idle) {
        onScrollStarted?.call();
      }
    }
    
    // Check if direction changed significantly
    if (newDirection != _currentDirection && 
        newDirection != ScrollDirection.idle &&
        _scrollVelocity.abs() > _velocityThreshold) {
      _currentDirection = newDirection;
      onScrollDirectionChanged?.call(_currentDirection, _scrollVelocity);
    }
    
    // Update position callback
    onScrollPositionChanged?.call(currentPosition, maxExtent);
    
    // Update state
    _lastScrollPosition = currentPosition;
    _lastScrollTime = currentTime;
    
    // Reset idle timer
    _resetIdleTimer();
  }
  
  /// Reset the idle timer
  void _resetIdleTimer() {
    _idleTimer?.cancel();
    _idleTimer = Timer(_idleTimeout, () {
      if (_currentDirection != ScrollDirection.idle) {
        _currentDirection = ScrollDirection.idle;
        _scrollVelocity = 0.0;
        onScrollEnded?.call();
      }
    });
  }
  
  /// Check if scroll position is at the top
  bool isAtTop(ScrollController scrollController, {double threshold = 50.0}) {
    if (!scrollController.hasClients) return true;
    return scrollController.position.pixels <= 
           scrollController.position.minScrollExtent + threshold;
  }
  
  /// Check if scroll position is at the bottom
  bool isAtBottom(ScrollController scrollController, {double threshold = 50.0}) {
    if (!scrollController.hasClients) return false;
    return scrollController.position.pixels >= 
           scrollController.position.maxScrollExtent - threshold;
  }
  
  /// Check if scroll position is in the middle area (not near top or bottom)
  bool isInMiddleArea(ScrollController scrollController, {double threshold = 100.0}) {
    return !isAtTop(scrollController, threshold: threshold) && 
           !isAtBottom(scrollController, threshold: threshold);
  }
  
  /// Get scroll progress as a percentage (0.0 to 1.0)
  double getScrollProgress(ScrollController scrollController) {
    if (!scrollController.hasClients) return 0.0;
    
    final position = scrollController.position;
    final totalScrollable = position.maxScrollExtent - position.minScrollExtent;
    
    if (totalScrollable <= 0) return 0.0;
    
    final currentProgress = (position.pixels - position.minScrollExtent) / totalScrollable;
    return currentProgress.clamp(0.0, 1.0);
  }
  
  /// Check if the user is scrolling fast
  bool isFastScrolling({double threshold = 200.0}) {
    return _scrollVelocity.abs() > threshold;
  }
  
  /// Check if the user is scrolling slowly
  bool isSlowScrolling({double threshold = 50.0}) {
    return _scrollVelocity.abs() > 0 && _scrollVelocity.abs() <= threshold;
  }
  
  /// Reset the service state
  void reset() {
    _lastScrollPosition = 0.0;
    _scrollVelocity = 0.0;
    _currentDirection = ScrollDirection.idle;
    _lastScrollTime = DateTime.now();
    _idleTimer?.cancel();
  }
  
  /// Dispose of the service
  void dispose() {
    _idleTimer?.cancel();
  }
}
