import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_payment/payment_waiting/payment_waiting_screen.dart';
import 'package:swadesic/features/buyers/buyer_payment/payment_waiting/upi_payment_wating/upi_payment_waiting_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_payment_options_responses/card_info_response.dart';
import 'package:swadesic/model/buyer_payment_options_responses/card_transaction_response.dart';
import 'package:swadesic/model/buyer_payment_options_responses/net_banking_transaction_response.dart';
import 'package:swadesic/model/buyer_payment_options_responses/upi_transaction_response.dart';
import 'package:swadesic/model/buyer_payment_options_responses/upi_validate_response.dart';
import 'package:swadesic/model/payment_model/upi_initiate_response.dart';
import 'package:swadesic/model/shopping_cart_responses/bank_list_response.dart'
    as bank_list;
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';
import 'package:swadesic/services/buyer_payment_services/buyer_payment_services.dart';
import 'package:swadesic/services/buyer_payment_services/upi_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import 'package:url_launcher/url_launcher_string.dart';

class BuyerPaymentBloc {
  // region Common Methods
  BuildContext context;
  late BuyerPaymentServices buyerPaymentServices;
  final GetCartPriceResponse getCartPriceResponse;
  Timer? _typingTimer;
  final _debounceDuration = const Duration(seconds: 2);
  //region Net Bank Model
  late NetBankingTransactionResponse netBankingTransactionResponse;

  //endregion
  //region Upi Handle Search Variables
  bool visibleUpiList = false;
  List<String> upiHandleList = <String>[
    'rbl',
    'idbi',
    'upi',
    'aubank',
    'axisbank',
    'bandhan',
    'dlb',
    'indus',
    'kbl',
    'federal',
    'sbi',
    'uco',
    'yesbank',
    'citi',
    'citigold',
    'dlb',
    'dbs',
    'freecharge',
    'okhdfcbank',
    'okaxis',
    'oksbi',
    'okicici',
    'hsbc',
    'idbi',
    'icici',
    'indianbank',
    'allbank',
    'kotak',
    'ikwik',
    'unionbankofindia',
    'uboi',
    'unionbank',
    'paytm',
    'ybl',
    'sib'
  ];
  List<String> searchedUpiHandleList = <String>[];
  String? selectedUpiHandle;

  //endregion
  //region Bank List Model,Service and Variable
  late bank_list.BankListResponse bankListResponse;
  late List<bank_list.PayChannelOptions> payChannelOptions = [];
  String selectedBank = "select your bank";
  String selectedBankChannel = "";
  bool totalAmountIsExpand = false;
  final String orderNumber;

  //endregion

  //region Card Model
  late CardTransactionResponse cardTransactionResponse;
  late GetCardInfoResponse getCardInfoResponse;

  //endregion

  //region UPI Model
  late UpiTransactionResponse upiTransactionResponse;

  // late UpiValidateResponse upiValidateResponse;

  //endregion

  // endregion

  //region Controller
  final totalAmountCtrl = StreamController<bool>.broadcast();
  final upiCtrl = StreamController<bool>.broadcast();
  final bankCtrl = StreamController<bool>.broadcast();
  final creditCardCtrl = StreamController<int>.broadcast();
  final bottomSheetVisible = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  final TextEditingController cardNumberTextCtrl = TextEditingController();
  final TextEditingController upiUserName = TextEditingController();
  final TextEditingController cardExpireCtrl = TextEditingController();
  final TextEditingController cardHolderName = TextEditingController();
  final TextEditingController cvvTextCtrl = TextEditingController();
  final TextEditingController upiHandleSearchTextCtrl = TextEditingController();
  final TextEditingController bankSearchTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  BuyerPaymentBloc(this.context, this.getCartPriceResponse, this.orderNumber);

  // endregion

  // region Init
  void init() {
    buyerPaymentServices = BuyerPaymentServices();
    //getBankList();
    //openUpiHandleSearchDialog();

    // //print(bankListResponse.data!.body!.bank!.bankListDetail!.first.channelName);
  }

  // endregion

  //region On Tap total amount
  onTapTotalAmount() {
    totalAmountIsExpand = !totalAmountIsExpand;
    //Refresh
    totalAmountCtrl.sink.add(true);
  }

  //endregion

  //region Get bank list
  getBankList() async {
    //region Try
    try {
      bankListResponse = await buyerPaymentServices.getBankList();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //print(error.message);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

  //endregion

  //region Net Banking Transaction
  netBankingTransaction() async {
    //region Try
    try {
      netBankingTransactionResponse =
          await buyerPaymentServices.netBankingTransaction(selectedBankChannel);
      //
      // AppConstants.htmlForm ="<!DOCTYPE html><html><head><title>'Net_Bankng_Payment'</title></head><body><center><h1>Please do not refresh this page...</h1></center>"
      //     " <form action='${netBankingTransactionResponse.data!.body!.bankForm!.redirectForm!.actionUrl!}' method='POST'>"
      //     " <input name='MD' value='P'> <input name='SBMTTYPE' value=POST> "
      //     "<input name='PID' value='${netBankingTransactionResponse.data!.body!.bankForm!.redirectForm!.content!.pID!}'>"
      //     " <input name='ES' value='${netBankingTransactionResponse.data!.body!.bankForm!.redirectForm!.content!.eS!}'> "
      //     "<input type='submit'></form></body></html>";

      AppConstants.htmlForm =
          "<html> <body onload='document.f.submit();'> <form id='f' name='f' method='post' action='${netBankingTransactionResponse.data!.body!.bankForm!.redirectForm!.actionUrl!}'><input type='hidden' name='MD' value='${netBankingTransactionResponse.data!.body!.bankForm!.redirectForm!.content!.mD!}'/><input type='hidden' name='PID' value='${netBankingTransactionResponse.data!.body!.bankForm!.redirectForm!.content!.pID!}' /><input type='hidden' name='ES' value='${netBankingTransactionResponse.data!.body!.bankForm!.redirectForm!.content!.eS!}' /></form> </body> </html>";
      //Go to payment waiting screen
      goToPaymentWaitingScreen("bank");
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //print(error.message);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

  //endregion

  //region On Change Expire Date
  onChangeExpireDate() {
    //print("hello");
    if (cardExpireCtrl.text.length == 2 &&
        int.parse(cardExpireCtrl.text) > 12) {
      cardExpireCtrl.clear();
      cardExpireCtrl.text = '12';
      CommonMethods.closeKeyboard(context);
    }
    // if(cardExpireCtrl.text.length == 2 && int.parse(cardExpireCtrl.text)< 10){
    //   cardExpireCtrl.clear();
    //   cardExpireCtrl.text = "0${}";
    // }
  }

  //endregion

  //region Card Transaction
  cardTransaction() async {
    //region Try
    try {
      if (cardNumberTextCtrl.text.isEmpty ||
          cvvTextCtrl.text.isEmpty ||
          cardExpireCtrl.text.isEmpty) {
        CommonMethods.toastMessage("Field can't be empty", context);
      }
      String card = cardNumberTextCtrl.text.replaceAll(" ", "");

      List<String> expDate = cardExpireCtrl.text.split('/');
      expDate.insert(1, '20');
      String cardExpireDate = '';

      for (var data in expDate) {
        cardExpireDate = cardExpireDate + data;
      }
      //print(cardExpireDate);
      String cardInfo = "|$card|${cvvTextCtrl.text}|$cardExpireDate";
      //print(cardInfo);
      cardTransactionResponse =
          await buyerPaymentServices.cardTransaction(cardInfo);
      AppConstants.htmlForm =
          "<html> <body onload='document.f.submit();'> <form id='f' name='f' method='post' action='${cardTransactionResponse.data!.body!.bankForm!.redirectForm!.actionUrl!}'><input type='hidden' name='MD' value='${cardTransactionResponse.data!.body!.bankForm!.redirectForm!.content!.mD!}'/><input type='hidden' name='PaReq' value='${cardTransactionResponse.data!.body!.bankForm!.redirectForm!.content!.paReq!}'/><input type='hidden' name='TermUrl' value='${cardTransactionResponse.data!.body!.bankForm!.redirectForm!.content!.termUrl!}'/></form> </body> </html>";
      //Go to payment waiting screen
      goToPaymentWaitingScreen("card");
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage("Enter correct card info", context);
      return;
    } catch (error) {
      CommonMethods.toastMessage("Enter correct card info", context);
      return;
    }
  }

  //endregion

  //region On Change Card Number
  void onChangeCardNumber(String value) {
    String card = value.replaceAll(" ", "");
    if (card.length == 6) {
      checkCardInfo(card);
    }
  }

  //endregion

  //region Check Card Info
  void checkCardInfo(String cardNumber) async {
    //region Try
    try {
      getCardInfoResponse = await buyerPaymentServices.cardInfo(cardNumber);
      if (getCardInfoResponse.data!.body!.resultInfo!.resultStatus == 'S') {
        CommonMethods.toastMessage("Valid  card", context);
      }
      CommonMethods.toastMessage("Invalid  card", context);
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(
          "This card is not supported. Please use another card.", context);
      return;
    } catch (error) {
      CommonMethods.toastMessage("Enter correct card info", context);
      return;
    }
  }

  //endregion

  upiValidate() async {
    //region Try
    try {
      if (upiUserName.text.isEmpty) {
        CommonMethods.toastMessage("Field can't be empty", context);
      }

      ///Un comment below after validate api is working.

      //
      String upiId = "${upiUserName.text}@${selectedUpiHandle}";

      bool vpaValidationStatus = await buyerPaymentServices.upiValidate(upiId);
      //Valid
      if (vpaValidationStatus) {
        //print("Success");
        return upiInitiateIntent(upiId: upiId);
      } else {
        return context.mounted
            ? CommonMethods.toastMessage(
                AppStrings.pleaseEnterValidUpi, context)
            : null;
      }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //print(error.message);
      return CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      return CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }

//region UPI Intent initiate
  upiInitiateIntent({required String upiId}) async {
    //region Try
    try {
      UpiInitiateResponse upiInitiateResponse = await UpiService().getUpiIntent(
          orderNumber: orderNumber,
          deliveryFee: getCartPriceResponse.cartFees!
              .firstWhere(
                  (element) => element.orderBreakupItemText == "Delivery fees")
              .orderBreakupItemValue
              .toString(),
          cartTotal: getCartPriceResponse.cartFees!
              .firstWhere(
                  (element) => element.orderBreakupItemText == "Product total")
              .orderBreakupItemValue
              .toString(),
          totalAmount: getCartPriceResponse.cartFees!
              .firstWhere(
                  (element) => element.orderBreakupItemText == "Grand total")
              .orderBreakupItemValue
              .toString(),
          upiIntent: upiId);

      var screen = UpiPaymentWaitingScreen(
          amount: getCartPriceResponse.cartFees!
              .firstWhere(
                  (element) => element.orderBreakupItemText == "Grand total")
              .orderBreakupItemValue
              .toString()
              .replaceAll("₹", ""),
          transactionId: upiInitiateResponse.txnToken,
          orderNumber: upiInitiateResponse.orderNumber,
          paymentChannel: upiId,
          isUpiIntent: false);
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.pushReplacement(context, route).then((value) {});
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
      return;
    } catch (error) {
      //print(error);
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
      return;
    }
  }

//endregion

  //region On Select Credit card
  void onSelectCreditCard(int index) {
    creditCardCtrl.sink.add(index);
  }

  //endregion

  ///Upi Search
  //region Upi Handle
  //region Open UPI Handle Search Dialog
  void openUpiHandleSearchDialog() {
    //Clear old added list onto Searched upi handle list
    searchedUpiHandleList.clear();
    //Add all the list of data to the Searched Upi list
    searchedUpiHandleList.addAll(upiHandleList);
    //Clear Text Field
    upiHandleSearchTextCtrl.clear();
    // searchUpiHandleDialogBox();
  }

  //endregion

  //region Search UPI Handle Dialog Box
  void searchUpiHandleDialogBox() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StreamBuilder<bool>(
              stream: upiCtrl.stream,
              builder: (context, snapshot) {
                return AlertDialog(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  // title: const Center(child: Text("Update pincode")),
                  // titleTextStyle:const TextStyle(
                  //     color: AppColors.appBlack2,
                  //     fontSize: 16,
                  //     fontFamily: "LatoSemiBold",
                  //     fontWeight: FontWeight.w600
                  // ),
                  content: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ///SearchField
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          decoration: BoxDecoration(
                              color: AppColors.textFieldFill1,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              //Prefix icon
                              Padding(
                                padding:
                                    const EdgeInsets.only(left: 10, right: 5),
                                child: Icon(
                                  Icons.search_rounded,
                                  color: AppColors.appBlack7,
                                ),
                              ),
                              //Text Field
                              Expanded(
                                child: TextFormField(
                                  // onEditingComplete: (){
                                  //   onEditingComplete();
                                  // },
                                  textAlign: TextAlign.start,
                                  scrollPadding: EdgeInsets.only(
                                      bottom:
                                          MediaQuery.of(context).size.height /
                                              5),

                                  onEditingComplete: () {
                                    CommonMethods.closeKeyboard(context);
                                  },
                                  onTap: () {},
                                  onChanged: (value) {
                                    onUpiHandleSearch(value);
                                  },
                                  textInputAction: TextInputAction.done,
                                  keyboardType: TextInputType.text,
                                  // controller: textFieldCtrl,
                                  maxLines: 1,
                                  controller: upiHandleSearchTextCtrl,
                                  style: TextStyle(
                                      fontFamily: "LatoRegular",
                                      fontSize: 15,
                                      fontWeight: FontWeight.w400,
                                      color: AppColors.appBlack),
                                  decoration: InputDecoration(
                                    filled: true,
                                    contentPadding: const EdgeInsets.symmetric(
                                        vertical: 10),
                                    fillColor: AppColors.textFieldFill1,
                                    isDense: true,
                                    hintText:
                                        "search and select the upi handle",
                                    hintStyle: TextStyle(
                                        fontSize: 14,
                                        fontFamily: "LatoSemibold",
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.writingColor3),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                          color: AppColors.textFieldFill1,
                                          width: 1.5),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                          color: AppColors.textFieldFill1,
                                          width: 1.5),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        ///List
                        Expanded(
                            child: ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          shrinkWrap: true,
                          itemCount: searchedUpiHandleList.length,
                          itemBuilder: (BuildContext context, int index) {
                            return InkWell(
                              onTap: () {
                                //Add selected UPI handle to the variable
                                selectedUpiHandle =
                                    searchedUpiHandleList[index];
                                //Refresh ui
                                upiCtrl.sink.add(true);
                                //Close dialog Box
                                Navigator.of(context, rootNavigator: true)
                                    .pop();
                              },
                              // padding: EdgeInsets.zero,
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10),
                                    child: Text(
                                      searchedUpiHandleList[index],
                                      textAlign: TextAlign.left,
                                      style: TextStyle(
                                        fontFamily: "LatoRegular",
                                        fontSize: 15,
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.appBlack,
                                      ),
                                    ),
                                  ),
                                  verticalSizedBox(10),
                                  divider(),
                                  verticalSizedBox(10),
                                ],
                              ),
                            );
                          },
                        ))

                        // Expanded(
                        //   child: StreamBuilder<bool>(
                        //       // stream: sellerOnBoardingBloc.categorySearchCtrl.stream,
                        //       initialData: true,
                        //       builder: (context, snapshot) {
                        //         return Padding(
                        //           padding: const EdgeInsets.only(bottom:50),
                        //           child:Container(
                        //             height: 150,
                        //             padding: EdgeInsets.all(10),
                        //             decoration: BoxDecoration(
                        //                 color: AppColors.white,
                        //                 borderRadius: const BorderRadius.all(Radius.circular(5)),
                        //                 border: Border.all(color: AppColors.lightWhite2)),
                        //             child:,
                        //           ),
                        //         );
                        //       }
                        //   ),
                        // )
                      ],
                    ),
                  ),
                );
              });
        });
  }

//endregion

  //region On Upi Handle search
  void onUpiHandleSearch(String text) {
    //Search the upi handle name and add to the searched list
    searchedUpiHandleList = upiHandleList
        .where((element) => element.toLowerCase().contains(text.toLowerCase()))
        .toList();
    //Refresh ui
    upiCtrl.sink.add(true);
  }

//endregion
//endregion

  ///Bank Search
  //region Bank
  //region Open Bank Search Dialog
  void openBankSearchDialog() {
    //Clear old added list onto SearchedBankList
    payChannelOptions.clear();
    // searchedBankList.clear();
    //Add all the list of data to the searchedBankList Upi list
    payChannelOptions
        .addAll(bankListResponse.data!.body!.nbPayOption!.payChannelOptions!);
    //Clear Text Field
    bankSearchTextCtrl.clear();
    searchBankDialogBox();
  }

  //endregion

  //region Search Bank Dialog Box
  void searchBankDialogBox() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StreamBuilder<bool>(
              stream: bankCtrl.stream,
              builder: (context, snapshot) {
                return AlertDialog(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  // title: const Center(child: Text("Update pincode")),
                  // titleTextStyle:const TextStyle(
                  //     color: AppColors.appBlack2,
                  //     fontSize: 16,
                  //     fontFamily: "LatoSemiBold",
                  //     fontWeight: FontWeight.w600
                  // ),
                  content: SizedBox(
                    // height: MediaQuery.of(context).size.height/3,
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ///SearchField
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          decoration: BoxDecoration(
                              color: AppColors.textFieldFill1,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              //Prefix icon
                              Padding(
                                padding:
                                    const EdgeInsets.only(left: 10, right: 5),
                                child: Icon(
                                  Icons.search_rounded,
                                  color: AppColors.appBlack7,
                                ),
                              ),
                              //Text Field
                              Expanded(
                                child: TextFormField(
                                  // onEditingComplete: (){
                                  //   onEditingComplete();
                                  // },
                                  textAlign: TextAlign.start,
                                  scrollPadding: EdgeInsets.only(
                                      bottom:
                                          MediaQuery.of(context).size.height /
                                              5),

                                  onEditingComplete: () {
                                    CommonMethods.closeKeyboard(context);
                                  },
                                  onTap: () {},
                                  onChanged: (value) {
                                    onBankSearch(value);
                                  },
                                  textInputAction: TextInputAction.done,
                                  keyboardType: TextInputType.text,
                                  // controller: textFieldCtrl,
                                  maxLines: 1,
                                  controller: bankSearchTextCtrl,
                                  style: TextStyle(
                                      fontFamily: "LatoRegular",
                                      fontSize: 15,
                                      fontWeight: FontWeight.w400,
                                      color: AppColors.appBlack),
                                  decoration: InputDecoration(
                                    filled: true,
                                    contentPadding: const EdgeInsets.symmetric(
                                        vertical: 10),
                                    fillColor: AppColors.textFieldFill1,
                                    isDense: true,
                                    hintText: "search and select the bank",
                                    hintStyle: TextStyle(
                                        fontSize: 14,
                                        fontFamily: "LatoSemibold",
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.writingColor3),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                          color: AppColors.textFieldFill1,
                                          width: 1.5),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                          color: AppColors.textFieldFill1,
                                          width: 1.5),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        ///List
                        Expanded(
                            child: ScrollConfiguration(
                          behavior: const ScrollBehavior(),
                          child: GlowingOverscrollIndicator(
                            axisDirection: AxisDirection.down,
                            showLeading: false,
                            showTrailing: false,
                            color: Colors.transparent,
                            child: ListView.builder(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 10),
                              // physics: BouncingScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: payChannelOptions.length,
                              itemBuilder: (BuildContext context, int index) {
                                return InkWell(
                                  onTap: () {
                                    //Add selected Bank
                                    selectedBank =
                                        payChannelOptions[index].channelName!;
                                    //Add Selected bank channel code
                                    selectedBankChannel =
                                        payChannelOptions[index].channelCode!;
                                    //Refresh ui
                                    bankCtrl.sink.add(true);
                                    //Close dialog Box
                                    Navigator.of(context, rootNavigator: true)
                                        .pop();
                                  },
                                  // padding: EdgeInsets.zero,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        child: Text(
                                          payChannelOptions[index].channelName!,
                                          textAlign: TextAlign.left,
                                          style: TextStyle(
                                            fontFamily: "LatoRegular",
                                            fontSize: 15,
                                            fontWeight: FontWeight.w400,
                                            color: AppColors.appBlack,
                                          ),
                                        ),
                                      ),
                                      verticalSizedBox(10),
                                      divider(),
                                      verticalSizedBox(10),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ))

                        // Expanded(
                        //   child: StreamBuilder<bool>(
                        //       // stream: sellerOnBoardingBloc.categorySearchCtrl.stream,
                        //       initialData: true,
                        //       builder: (context, snapshot) {
                        //         return Padding(
                        //           padding: const EdgeInsets.only(bottom:50),
                        //           child:Container(
                        //             height: 150,
                        //             padding: EdgeInsets.all(10),
                        //             decoration: BoxDecoration(
                        //                 color: AppColors.white,
                        //                 borderRadius: const BorderRadius.all(Radius.circular(5)),
                        //                 border: Border.all(color: AppColors.lightWhite2)),
                        //             child:,
                        //           ),
                        //         );
                        //       }
                        //   ),
                        // )
                      ],
                    ),
                  ),
                );
              });
        });
  }

//endregion

  //region On Bank Search
  void onBankSearch(String text) {
    //Search the bank name and add to the searched list

    payChannelOptions.clear();

    for (var data
        in bankListResponse.data!.body!.nbPayOption!.payChannelOptions!) {
      // if(data.storeName!.toLowerCase().contains( value.toLowerCase())) storeList.add(data);
      if (data.channelName!.toLowerCase().contains(text.toLowerCase()))
        payChannelOptions.add(data);
    }

    // for(var data in storeListResponse.data!){
    //   if(data.storeName!.toLowerCase().contains( value.toLowerCase()) || data.storehandle!.toLowerCase().contains( value.toLowerCase())) storeList.add(data);
    // }

    // searchedBankList = bankList.where((element)=>element.toLowerCase().contains(text.toLowerCase())).toList();
    //Refresh ui
    bankCtrl.sink.add(true);
  }

//endregion
//endregion

  //region Go to Buyer payment waiting screen
  void goToPaymentWaitingScreen(String paymentType) {
    var screen = PaymentWaitingScreen(
      paymentType: paymentType,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route).then((value) {
    //   // Navigator.pop(context);
    // });
    Navigator.pushReplacement(context, route).then((value) {});
  }

  //endregion

  //region Open UPI App
  void launchURL(String upiUrl) async {
    var screen = const PaymentWaitingScreen(
      paymentType: "upi",
      isUpiPay: true,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route).then((value) {
    //   // Navigator.pop(context);
    // });
    Navigator.pushReplacement(context, route).then((value) {});
    String url = upiUrl;
    var result = await launchUrlString(url);
    debugPrint(result.toString());
    if (result == true) {
      //print("Done");
    } else if (result == false) {
      //print("Fail");
    }
  }

  //endregion

  //region On tap select VPA option
  void onTapSelectVpa() {
    var screen = SearchScreen(
      dataList: upiHandleList,
      searchTitle: AppStrings.selectVpa,
      isAddFeatureEnable: true,
    );

    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      if (value == null) {
        return;
      }
      selectedUpiHandle = value;
      upiCtrl.sink.add(true);
    });
  }

  //endregion

//region Dispose
  void dispose() {
    upiCtrl.close();
    bankCtrl.close();
    creditCardCtrl.close();
    bottomSheetVisible.close();
  }
//endregion
}
