import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_confirmed/start_return/start_return_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReturnByLogistics extends StatefulWidget {
  final StartReturnBloc startReturnBloc;
  const ReturnByLogistics({Key? key, required this.startReturnBloc})
      : super(key: key);

  @override
  State<ReturnByLogistics> createState() => _ReturnByLogisticsState();
}

class _ReturnByLogisticsState extends State<ReturnByLogistics> {
  //region Init
  @override
  void initState() {
    clearTextFields();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    clearTextFields();
    super.dispose();
  }
  //endregion

  //region Clear text fields
  void clearTextFields() {
    StartReturnBloc.trackingLinkTextCtrl.clear();
    StartReturnBloc.trackingNumberTextCtrl.clear();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: widget.startReturnBloc.trackingLinkCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///Logistics partner
              AppTitleAndOptions(
                title: "Return Logistics Partner",
                titleOption: SvgPicture.asset(AppImages.exclamation),
                option: AppCommonWidgets.dropDownOptions(
                    onTap: () {
                      widget.startReturnBloc.onTapLogisticsPartners();
                    },
                    context: context,
                    hintText: "Select the logistics partner",
                    value: widget.startReturnBloc.selectedLogisticsPartner),
              ),
              verticalSizedBox(13),

              ///Tracking link
              AppTitleAndOptions(
                title: "Return Tracking Link",
                titleOption: SvgPicture.asset(AppImages.exclamation),
                option: AppTextFields.websiteTextField(
                    context: context,
                    textEditingController: StartReturnBloc.trackingLinkTextCtrl,
                    hintText: "Return Tracking Link",
                    onChanged: () {
                      widget.startReturnBloc.checkUrlValidation();
                    }),
              ),

              ///If url is in-valid
              widget.startReturnBloc.isUrlValid != null &&
                      !widget.startReturnBloc.isUrlValid!
                  ? AppCommonWidgets.validAndInvalid(
                      buttonText: AppStrings.invalidUrl,
                      textColor: AppColors.red,
                    )
                  : const SizedBox(),

              ///If url is valid
              Visibility(
                visible: widget.startReturnBloc.isUrlValid != null &&
                    widget.startReturnBloc.isUrlValid!,
                child: AppCommonWidgets.validAndInvalid(
                    buttonText: AppStrings.viewTheLink,
                    textColor: AppColors.brandBlack,
                    isUnderLine: true,
                    onTap: () {
                      CommonMethods.openAppWebView(
                          context: context,
                          webUrl: StartReturnBloc.trackingLinkTextCtrl.text);
                    }),
              ),
              verticalSizedBox(20),

              ///Tracking Number
              AppTitleAndOptions(
                title: "Return Tracking Number",
                titleOption: SvgPicture.asset(AppImages.exclamation),
                option: AppTextFields.allTextField(
                  context: context,
                  maxEntry: 50,
                  textEditingController: StartReturnBloc.trackingNumberTextCtrl,
                  hintText: "Return Tracking Number",
                ),
              ),
            ],
          );
        });
  }
}
