import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/completed_order/completed_order_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Completed Order Screen
class CompletedOrderScreen extends StatefulWidget {
  const CompletedOrderScreen({Key? key}) : super(key: key);

  @override
  _CompletedOrderScreenState createState() => _CompletedOrderScreenState();
}
// endregion

class _CompletedOrderScreenState extends State<CompletedOrderScreen> {
  // region Bloc
  late CompletedOrderBloc completedOrderBloc;
  // endregion

  // region Init
  @override
  void initState() {
    completedOrderBloc = CompletedOrderBloc(context);
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      resizeToAvoidBottomInset: true,
      body: SafeArea(child: body()),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return buyerAppBar(
      leadingIcon: SvgPicture.asset(AppImages.backButton,
          color: AppColors.appBlack, fit: BoxFit.fill),
      context: context,
      drawerIconEnable: true,
      titleText: AppStrings.shoppingCart,
    );
  }
  //endregion

  // region Body
  Widget body() {
    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      shrinkWrap: true,
      children: [
        orderPlaced(),
        verticalSizedBox(10),
        weWillInform(),
        verticalSizedBox(30),
        thankYou(),
        verticalSizedBox(15),
        appreciate(),
        verticalSizedBox(15),
        orderWith(),
        verticalSizedBox(30),
        storeInfoDate(),
        verticalSizedBox(15),
        divider(),
        //verticalSizedBox(15),
        StreamBuilder<bool>(
            stream: completedOrderBloc.viewDetailCtrl.stream,
            initialData: completedOrderBloc.viewDetailActiveInActive,
            builder: (context, snapshot) {
              if (!snapshot.data!) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    viewDetailInProgress(),
                    //verticalSizedBox(15),
                  ],
                );
              }
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  viewDetailInProgress(),
                  // verticalSizedBox(15),
                  listOfOrderedProduct(),
                ],
              );
            }),
        divider(),
        verticalSizedBox(15),
        haveQuestion(),
        verticalSizedBox(15),
        Divider(
          color: AppColors.inActiveGreen,
          height: 1,
          thickness: 1,
        ),
        verticalSizedBox(20),
        storeName(),
        verticalSizedBox(15),
        newUpdates(),
        verticalSizedBox(15),
        followViewStore(),
        verticalSizedBox(40),
        goToHome(),
      ],
    );
  }
  // endregion

  //region Yay Order Placed
  Widget orderPlaced() {
    return Text(
      AppStrings.orderPlacedSuccessfully,
      style: TextStyle(
          fontWeight: FontWeight.w700,
          fontSize: 18,
          fontFamily: "LatoBold",
          color: AppColors.discountGreen),
    );
  }
  //endregion

  //region We Will Inform Seller
  Widget weWillInform() {
    return Text(
      AppStrings.weWillInformSeller,
      textAlign: TextAlign.left,
      style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 14,
          fontFamily: "LatoRegular",
          color: AppColors.appBlack),
    );
  }
  //endregion

  //region Thank You For Supporting
  Widget thankYou() {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: AppStrings.thankYouForSupporting,
            style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                fontFamily: "LatoSemiBold",
                color: AppColors.brandBlack),
          ),
          TextSpan(
            text: AppStrings.bharat,
            style: TextStyle(
                //fontStyle: FontStyle.italic,
                fontWeight: FontWeight.w700,
                fontSize: 16,
                fontFamily: "LatoBoldItalic",
                color: AppColors.brandBlack),
          ),
          TextSpan(
            text: AppStrings.threeHand,
          ),
        ],
      ),
    );
  }
  //endregion

  //region Appreciate
  Widget appreciate() {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: AppStrings.appreciateYourTrust,
            style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                fontFamily: "LatoSemiBold",
                color: AppColors.brandBlack),
          ),
          TextSpan(
            text: AppStrings.whitelabel,
            style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 16,
                fontFamily: "LatoBold",
                color: AppColors.brandBlack),
          ),
        ],
      ),
    );
  }
  //endregion

  //region Your Order With
  Widget orderWith() {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: "Your order with ",
            style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 14,
                fontFamily: "LatoRegular",
                color: AppColors.appBlack),
          ),
          TextSpan(
            text: "Nykaa_online ",
            style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 16,
                fontFamily: "LatoBold",
                color: AppColors.appBlack),
          ),
          TextSpan(
            text: "can be found in ",
            style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 14,
                fontFamily: "LatoRegular",
                color: AppColors.appBlack),
          ),
          TextSpan(
            text: "My orders",
            style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 16,
                fontFamily: "LatoBold",
                color: AppColors.appBlack),
          ),
        ],
      ),
    );
  }
  //endregion

  //region Store Name, Id, Order Date and Delivery date
  Widget storeInfoDate() {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            storeImage(),
            horizontalSizedBox(15),
            Expanded(child: storeInfo()),
          ],
        ),
        SvgPicture.asset(
          AppImages.exclamation,
          fit: BoxFit.cover,
        ),
      ],
    );
  }
  //endregion

  //region Store Logo
  Widget storeImage() {
    return SizedBox(
        height: 50,
        width: 50,
        child: Image.asset(
          AppImages.nykaa,
          fit: BoxFit.fill,
        ));
  }
  //endregion

  //region Info
  Widget storeInfo() {
    return Column(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ///Store Name
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(
              width: 100,
              child: Text(
                "StoreName",
                textAlign: TextAlign.left,
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    fontFamily: "LatoSemiBold",
                    color: AppColors.darkGray),
              ),
            ),
            Text(
              ": StoreName",
              textAlign: TextAlign.left,
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  fontFamily: "LatoSemiBold",
                  color: AppColors.darkGray),
            )
          ],
        ),
        verticalSizedBox(5),

        ///Store Order ID
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(
              width: 100,
              child: Text(
                AppStrings.orderID,
                textAlign: TextAlign.left,
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    fontFamily: "LatoSemiBold",
                    color: AppColors.darkGray),
              ),
            ),
            Text(
              ": 123456789012",
              textAlign: TextAlign.left,
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  fontFamily: "LatoSemiBold",
                  color: AppColors.darkGray),
            )
          ],
        ),
        verticalSizedBox(5),

        ///Store Order Date
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(
              width: 100,
              child: Text(
                AppStrings.orderID,
                textAlign: TextAlign.left,
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    fontFamily: "LatoSemiBold",
                    color: AppColors.darkGray),
              ),
            ),
            Text(
              ": 20-07-2022",
              textAlign: TextAlign.left,
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  fontFamily: "LatoSemiBold",
                  color: AppColors.darkGray),
            )
          ],
        ),
        verticalSizedBox(5),

        ///Store Delivery Date
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(
              width: 100,
              child: Text(
                AppStrings.deliveryDate,
                textAlign: TextAlign.left,
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    fontFamily: "LatoSemiBold",
                    color: AppColors.darkGray),
              ),
            ),
            Text(
              ": 20-07-2022",
              textAlign: TextAlign.left,
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  fontFamily: "LatoSemiBold",
                  color: AppColors.darkGray),
            ),
          ],
        ),
      ],
    );
  }
  //endregion

  //region View detail and InProgress
  Widget viewDetailInProgress() {
    return StreamBuilder<bool>(
        stream: completedOrderBloc.viewDetailCtrl.stream,
        initialData: completedOrderBloc.viewDetailActiveInActive,
        builder: (context, snapshot) {
          return CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              completedOrderBloc.onTapViewDetail();
            },
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      AppStrings.viewDetails,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          fontFamily: "LatoRegular",
                          color: AppColors.darkGray),
                    ),
                    horizontalSizedBox(10),
                    snapshot.data!
                        ? RotatedBox(
                            quarterTurns: 2,
                            child: SvgPicture.asset(
                              AppImages.downArrow,
                              fit: BoxFit.cover,
                              color: AppColors.darkGray,
                            ))
                        : SvgPicture.asset(
                            AppImages.downArrow,
                            fit: BoxFit.cover,
                            color: AppColors.darkGray,
                          )
                  ],
                ),
                Text(
                  AppStrings.inProgress,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 15,
                      fontFamily: "LatoSemiBold",
                      color: AppColors.brandBlack),
                ),
              ],
            ),
          );
        });
  }
  //endregion

  //region List Of Ordered Products
  Widget listOfOrderedProduct() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: 10,
        itemBuilder: (BuildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              divider(),
              verticalSizedBox(10),
              deliveryEstimation(),
              verticalSizedBox(15),
              orderedProductDetail(),
              verticalSizedBox(10),
              cancelEditViewOrderDetail(),
              verticalSizedBox(10),
            ],
          );
        });
  }
  //endregion

  //region Delivery Estimate and Waiting For the Seller Confirmation
  Widget deliveryEstimation() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "Delivery estimate \n24-01-2021",
          textAlign: TextAlign.left,
          style: TextStyle(
            fontFamily: "LatoSemiBold",
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.appBlack,
          ),
        ),
        Text(
          "Waiting for \nseller’s confirmatoin",
          textAlign: TextAlign.right,
          style: TextStyle(
            fontFamily: "LatoRegular",
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: AppColors.appBlack,
          ),
        )
      ],
    );
  }
  //endregion

  //region Ordered Product Name, Quantity, Price and Image
  Widget orderedProductDetail() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Nykaa’s Matte Nail Lacquer & Nail Enamel 2 in 1 colors with...",
                maxLines: 2,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontFamily: "LatoSemiBold",
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGray,
                ),
              ),
              verticalSizedBox(5),
              Text(
                "quantity : 1",
                maxLines: 1,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontFamily: "LatoSemiBold",
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlack,
                ),
              ),
              Text(
                "₹ 400",
                maxLines: 1,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontFamily: "LatoSemiBold",
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlack,
                ),
              ),
            ],
          ),
        ),
        ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            child: Image.asset(
              AppImages.nykaa,
              fit: BoxFit.cover,
              height: 70,
              width: 70,
            ))
      ],
    );
  }
  //endregion

  //region Cancel Edit And View OrderDetail
  Widget cancelEditViewOrderDetail() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              border: Border.all(color: AppColors.inActiveGreen)),
          child: Center(
            child: Text(
              AppStrings.cancelEdit,
              style: TextStyle(
                  fontFamily: "LatoRegular",
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColors.appBlack),
            ),
          ),
        ),
        horizontalSizedBox(10),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              border: Border.all(color: AppColors.inActiveGreen)),
          child: Center(
            child: Text(
              AppStrings.viewOrderDetails,
              style: TextStyle(
                  fontFamily: "LatoRegular",
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColors.appBlack),
            ),
          ),
        ),
      ],
    );
  }
  //endregion

  //region Have a question Ask Seller
  Widget haveQuestion() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          AppStrings.haveQuestions,
          textAlign: TextAlign.left,
          style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              fontFamily: "LatoRegular",
              color: AppColors.darkGray),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              border: Border.all(color: AppColors.lightStroke, width: 1.5)),
          child: Text("Ask seller",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                fontFamily: "LatoBold",
                color: AppColors.writingColor2,
              )),
        )
      ],
    );
  }
  //endregion

  //region Store Name
  Widget storeName() {
    return Text(
      "@Nykaa_online",
      textAlign: TextAlign.left,
      style: TextStyle(
          fontWeight: FontWeight.w700,
          fontSize: 16,
          fontFamily: "LatoBold",
          color: AppColors.appBlack),
    );
  }
  //endregion

  //region Receive New Product Updates
  Widget newUpdates() {
    return Text(
      AppStrings.receiveDiscount,
      textAlign: TextAlign.left,
      style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 14,
          fontFamily: "LatoRegular",
          color: AppColors.appBlack),
    );
  }
  //endregion

  //region Follow and View Store
  Widget followViewStore() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(10))),
            child: CupertinoButton(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                color: AppColors.brandBlack,
                padding: EdgeInsets.zero,
                child: Center(
                  child: Text(
                    AppStrings.support,
                    style: TextStyle(
                        fontFamily: "LatoBold",
                        fontWeight: FontWeight.w700,
                        fontSize: 15,
                        color: AppColors.appWhite),
                  ),
                ),
                onPressed: () {
                  //buyerViewProductBloc.gotoShoppingCart();
                }),
          ),
        ),
        horizontalSizedBox(10),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                border: Border.all(color: AppColors.darkGray)),
            child: CupertinoButton(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                color: AppColors.appWhite,
                padding: EdgeInsets.zero,
                child: Center(
                  child: Text(
                    AppStrings.viewStore,
                    style: TextStyle(
                        fontFamily: "LatoBold",
                        fontWeight: FontWeight.w700,
                        fontSize: 15,
                        color: AppColors.writingColor2),
                  ),
                ),
                onPressed: () {}),
          ),
        ),
      ],
    );
  }
  //endregion

  //region Go To Home
  Widget goToHome() {
    return Container(
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          border: Border.all(color: AppColors.brandBlack)),
      child: CupertinoButton(
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          color: AppColors.appWhite,
          padding: EdgeInsets.zero,
          child: Center(
            child: Text(
              AppStrings.goToHome,
              style: TextStyle(
                  fontFamily: "LatoSemiBold",
                  fontWeight: FontWeight.w600,
                  fontSize: 17,
                  color: AppColors.brandBlack),
            ),
          ),
          onPressed: () {}),
    );
  }
  //endregion
}
