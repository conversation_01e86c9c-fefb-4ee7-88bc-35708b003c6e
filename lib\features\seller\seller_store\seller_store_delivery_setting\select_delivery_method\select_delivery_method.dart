import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/select_delivery_method/select_delivery_method_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivery_setting_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';

class SelectDeliveryMethod extends StatefulWidget {
  final SellerStoreDeliverySettingBloc sellerStoreDeliverySettingBloc;
  const SelectDeliveryMethod({Key? key, required this.sellerStoreDeliverySettingBloc}) : super(key: key);

  @override
  State<SelectDeliveryMethod> createState() => _SelectDeliveryMethodState();
}

class _SelectDeliveryMethodState extends State<SelectDeliveryMethod> {
  //region Bloc
  late SelectDeliveryMethodBloc selectDeliveryMethodBloc;
  //endregion

  //region Init
  @override
  void initState() {
    selectDeliveryMethodBloc = SelectDeliveryMethodBloc(context,widget.sellerStoreDeliverySettingBloc);
    selectDeliveryMethodBloc.init();
    // TODO: implement initState
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return productDeliveryMethod();
  }



  //region Select Product Delivery Method
  Widget productDeliveryMethod() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ///Delivery preferences
        Container(
            margin: const EdgeInsets.only(bottom: 23),
            child: Text(AppStrings.deliveryPreferences,style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),)),

        ///Select delivery Method
        AppTitleAndOptions(
          title: AppStrings.productDeliveryMethod,
          option:Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              appRadioCheckBox(text:AppStrings.selfDeliveryByStore,isRadio: true,isActive:SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliverymethodSelf!,isExpand: true,onTap: (){
                selectDeliveryMethodBloc.onSelectSelf();
              }),
              appRadioCheckBox(text:AppStrings.deliveryByLogistics,isRadio: true,isActive:SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.deliverymethodLogistics!,isExpand: true,onTap: (){
                selectDeliveryMethodBloc.onSelectLogistic();
              }),
            ],
          ),
        ),


        verticalSizedBox(30)
      ],
    );
  }

//endregion

}
