import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/refund_amount_calculation/refund_amount_calculation.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/speak_with_seller/speak_with_seller.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/return_escalate/product_return_request_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/return_escalate/return_escalate_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReturnEscalate extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final Order order;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  const ReturnEscalate(
      {Key? key,
      required this.subOrderList,
      required this.order,
      required this.buyerSubOrderBloc})
      : super(key: key);

  @override
  State<ReturnEscalate> createState() => _ReturnEscalateState();
}

class _ReturnEscalateState extends State<ReturnEscalate> {
  //region Bloc
  late ReturnEscalateBloc returnEscalateBloc;
  //endregion
  //region Init
  @override
  void initState() {
    returnEscalateBloc = ReturnEscalateBloc(
        context, widget.subOrderList, widget.order, widget.buyerSubOrderBloc);
    returnEscalateBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    // Dispose the main text controller
    returnEscalateBloc.returnReasonTextCtrl.dispose();

    // Dispose all individual text controllers
    for (var controller in returnEscalateBloc.returnReasonControllers.values) {
      controller.dispose();
    }

    // Dispose the stream controller
    returnEscalateBloc.bottomSheetRefresh.close();

    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: returnEscalateBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                  child: SpeakWithSeller(
                    subOrderList: returnEscalateBloc.subOrderList,
                    buyerSubOrderBloc: returnEscalateBloc.buyerSubOrderBloc,
                    order: returnEscalateBloc.order,
                    title: AppStrings.toReturnProduct,
                    subTitle: AppStrings.speakWithSellerShareTheReason,
                  ),
                ),

                verticalSizedBox(20),

                ///Drop down
                InkWell(
                  onTap: () {
                    returnEscalateBloc.onTapProductList();
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 6.5),
                    decoration: BoxDecoration(
                        color: AppColors.lightestGrey2,
                        borderRadius: BorderRadius.circular(7)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ///Drop down
                        Container(
                          padding: const EdgeInsets.all(10),
                          child: Row(
                            children: [
                              //Grand total
                              Text(
                                AppStrings.productList,
                                style: AppTextStyle.settingHeading1(
                                    textColor: AppColors.appBlack),
                              ),
                              Expanded(child: horizontalSizedBox(10)),
                              returnEscalateBloc.isProductListVisible
                                  ? RotatedBox(
                                      quarterTurns: 3,
                                      child: RotatedBox(
                                          quarterTurns: 4,
                                          child: SvgPicture.asset(
                                              AppImages.arrow3,
                                              color: AppColors.appBlack)),
                                    )
                                  : RotatedBox(
                                      quarterTurns: 1,
                                      child: SvgPicture.asset(AppImages.arrow3,
                                          color: AppColors.appBlack))
                            ],
                          ),
                        ),

                        ///List of product
                        Visibility(
                            visible: returnEscalateBloc.isProductListVisible,
                            child: subOrderList()),

                        ///Selected product count
                        Visibility(
                          visible: !returnEscalateBloc.isProductListVisible,
                          child: Container(
                              alignment: Alignment.centerLeft,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 10),
                              child: Text(
                                "${CommonMethods.returnHowManySubOrdersSelected(subOrderList: returnEscalateBloc.subOrderList) == 0 ? "No" : CommonMethods.returnHowManySubOrdersSelected(subOrderList: returnEscalateBloc.subOrderList)} ${CommonMethods.returnHowManySubOrdersSelected(subOrderList: returnEscalateBloc.subOrderList) == 1 ? "suborder" : "suborders"} selected",
                                style: AppTextStyle.settingText(
                                    textColor: AppColors.appBlack),
                              )),
                        )
                      ],
                    ),
                  ),
                ),

                ///Refund amount calculation
                Visibility(
                    visible: returnEscalateBloc.isShowRefundClicked,
                    child: Container(
                        margin: const EdgeInsets.only(top: 20),
                        child: RefundAmountCalculation(
                          subOrderList: returnEscalateBloc.subOrderList
                              .where((element) => element.isSelected)
                              .toList(),
                          order: returnEscalateBloc.order,
                          backgroundColor: AppColors.textFieldFill1,
                        ))),

                verticalSizedBox(20),

                ///Buttons and field reason
                requestAndShowRefundButtonAndReason(),
                //Bottom space
                AppCommonWidgets.bottomListSpace(context: context)
              ],
            ),
          );
        });
  }
//endregion

  //region Sub orders list
  Widget subOrderList() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: returnEscalateBloc.subOrderList.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppCommonWidgets.subOrderInfo(
                  subOrder: returnEscalateBloc.subOrderList[index],
                  onTap: () {
                    returnEscalateBloc
                        .onSelectSubOrder(widget.subOrderList[index]);
                    returnEscalateBloc.hideRefund();
                  },
                  context: context),
              index == returnEscalateBloc.subOrderList.length - 1
                  ? const SizedBox()
                  : Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 5, horizontal: 20),
                      child: Divider(
                        color: AppColors.lightGray,
                        height: 1,
                        thickness: 1,
                      ),
                    )
            ],
          );
        });
  }

//endregion

//region Request and show refund button and reason
  Widget requestAndShowRefundButtonAndReason() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: [
          //Buttons
          Visibility(
            visible: !returnEscalateBloc.isReturnRequestClicked,
            child: Column(
              children: [
                // Big primary button for Add Reason & Request return
                Container(
                  height: 56, // Make the button taller
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    color: AppColors.brandBlack,
                    // boxShadow: [
                    //   BoxShadow(
                    //     color: AppColors.brandBlack.withOpacity(0.3),
                    //     blurRadius: 8,
                    //     offset: const Offset(0, 4),
                    //   ),
                    // ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(100),
                      onTap: () {
                        returnEscalateBloc.onTapRequestReturn();
                      },
                      child: Center(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "Add Reason & Request return",
                              style: AppTextStyle.button2Bold(
                                  textColor: AppColors.appWhite),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Text button for Show refund amount
                Visibility(
                  visible: !returnEscalateBloc.isShowRefundClicked,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 0.0),
                    child: TextButton(
                      onPressed: () {
                        returnEscalateBloc.onTapShowRefund();
                      },
                      child: Text(
                        "Show refund amount",
                        style: AppTextStyle.button2Bold(
                                textColor: AppColors.brandBlack)
                            .copyWith(
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          Visibility(
              visible: returnEscalateBloc.isReturnRequestClicked,
              child: Column(
                children: [
                  // Show return request widgets for each selected suborder
                  ...returnEscalateBloc.subOrderList
                      .where((subOrder) => subOrder.isSelected)
                      .map((subOrder) {
                    // Get the list of selected suborders
                    final selectedSuborders = returnEscalateBloc.subOrderList
                        .where((so) => so.isSelected)
                        .toList();
                    // Check if this is the last item in the list
                    final isLastItem = selectedSuborders.last == subOrder;

                    // Get conditions for this suborder
                    final conditions = returnEscalateBloc.returnConditionsMap
                            .containsKey(subOrder.suborderNumber!)
                        ? returnEscalateBloc
                            .returnConditionsMap[subOrder.suborderNumber!]!
                        : <String>[];

                    // Get checked state for conditions
                    final checkedState = returnEscalateBloc
                            .returnConditionsCheckedMap
                            .containsKey(subOrder.suborderNumber!)
                        ? returnEscalateBloc.returnConditionsCheckedMap[
                            subOrder.suborderNumber!]!
                        : <bool>[];

                    // Return the new product return request widget
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: ProductReturnRequestWidget(
                        subOrder: subOrder,
                        textController: returnEscalateBloc
                            .returnReasonControllers[subOrder.suborderNumber!]!,
                        returnConditions: conditions,
                        conditionsCheckedState: checkedState,
                        onConditionsChanged: (checkedState) {
                          returnEscalateBloc.updateReturnConditionsChecked(
                              subOrder.suborderNumber!, checkedState);
                        },
                        isLastItem: isLastItem,
                      ),
                    );
                  }).toList(),

                  verticalSizedBox(24),
                  // Big primary button for Initiate Return
                  Container(
                    height: 56, // Make the button taller
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: AppColors.brandBlack,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.brandBlack.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(100),
                        onTap: () {
                          returnEscalateBloc.returnProductApiCall();
                        },
                        child: Center(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                "Initiate Return",
                                style: AppTextStyle.button2Bold(
                                    textColor: AppColors.appWhite),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Show refund amount text button (visible even in return request mode)
                  Visibility(
                    visible: !returnEscalateBloc.isShowRefundClicked,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Center(
                        child: TextButton(
                          onPressed: () {
                            returnEscalateBloc.onTapShowRefund();
                          },
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.calculate_outlined,
                                color: AppColors.brandBlack,
                                size: 16,
                              ),
                              horizontalSizedBox(8),
                              Text(
                                "Show refund amount",
                                style: AppTextStyle.button2Bold(
                                        textColor: AppColors.brandBlack)
                                    .copyWith(
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ))
        ],
      ),
    );
  }
//endregion

//region Grey button
  Widget buyerMyOrderCancelButton(
      {onPress, String buttonName = "", Color? buttonColor, Color? textColor}) {
    buttonColor = buttonColor ?? AppColors.lightGray;
    textColor = textColor ?? AppColors.appBlack;
    return InkWell(
      onTap: () {
        onPress();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 20),
        decoration: BoxDecoration(
          color: buttonColor,
          borderRadius: const BorderRadius.all(Radius.circular(200)),
        ),
        child: Center(
          child: Text(
            buttonName,
            style: AppTextStyle.button2Bold(textColor: textColor),
            // style: TextStyle(
            //     fontFamily:AppConstants.rRegular,
            //     fontWeight: FontWeight.w600,
            //     fontSize: 14,
            //     color: AppColors.writingColor3),
          ),
        ),
      ),
    );
  }
//endregion
}
