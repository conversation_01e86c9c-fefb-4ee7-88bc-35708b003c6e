import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';

/// Custom bottom navigation widget that matches the reference design
/// with 4 grouped tabs in a pill background + 1 separate profile tab
class CustomBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTabSelected;
  final Function(int) onDoubleTap;
  final Function(double) onHorizontalSwipe;
  final Function(int) onLongPress;
  final Function(int) onIconSwipe;
  final List<String> tabIcons;
  final List<String>? tabIconsFilled; // Optional filled icons
  final List<String> tabNames;
  final Widget profileWidget;
  final int groupedTabCount; // Number of tabs in the grouped section

  const CustomBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTabSelected,
    required this.onDoubleTap,
    required this.onHorizontalSwipe,
    required this.onLongPress,
    required this.onIconSwipe,
    required this.tabIcons,
    this.tabIconsFilled,
    required this.tabNames,
    required this.profileWidget,
    this.groupedTabCount = 4, // Default to 4 tabs for backward compatibility
  })  : assert(tabIcons.length >= groupedTabCount,
            'tabIcons must have at least $groupedTabCount items'),
        assert(
            tabIconsFilled == null || tabIconsFilled.length >= groupedTabCount,
            'tabIconsFilled must be null or have at least $groupedTabCount items');

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 10, bottom: 6),
      height: 65,
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        // boxShadow: [
        //   BoxShadow(
        //     color: AppColors.appBlack,
        //     blurRadius: 4,
        //     offset: Offset(0, -2),
        //   )
        // ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Group of 4 tabs inside pill background
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
              decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.circular(40),
              ),
              child: GestureDetector(
                onHorizontalDragEnd: (details) {
                  if (details.primaryVelocity != null) {
                    onHorizontalSwipe(details.primaryVelocity!);
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(groupedTabCount, (i) {
                    return Expanded(
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => onTabSelected(i),
                          onDoubleTap: () => onDoubleTap(i),
                          onLongPress: () => onLongPress(i),
                          // onVerticalDragEnd: (_) => onIconSwipe(i),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                            alignment: Alignment.center,
                            child: SvgPicture.asset(
                              (tabIconsFilled != null && currentIndex == i)
                                  ? tabIconsFilled![i]
                                  : tabIcons[i],
                              height: 28,
                              width: 28,
                              color: (currentIndex == i)
                                  ? null
                                  : AppColors.writingBlack1,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ),
            ),
          ),

          const SizedBox(width: 10),

          // Profile widget with gestures
          GestureDetector(
            onTap: () => onTabSelected(
                groupedTabCount), // Profile is after all grouped tabs
            onDoubleTap: () => onDoubleTap(groupedTabCount),
            onLongPress: () => onLongPress(groupedTabCount),
            onVerticalDragEnd: (_) => onIconSwipe(groupedTabCount),
            child: profileWidget,
          ),
        ],
      ),
    );
  }
}

/// Profile avatar widget for the navigation bar
class ProfileAvatar extends StatelessWidget {
  final bool isSelected;
  final String? imageUrl;
  final bool isStore;

  const ProfileAvatar({
    super.key,
    required this.isSelected,
    required this.isStore,
    this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 0, right: 14),
      child: CustomImageContainer(
        width: 36,
        height: 36,
        imageUrl: imageUrl,
        imageType: isStore
            ? CustomImageContainerType.store
            : CustomImageContainerType.user,
        showShadow: false,
      ),
    );
  }
}
