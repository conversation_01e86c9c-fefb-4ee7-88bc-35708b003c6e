import 'package:expandable/expandable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/buyer_and_seller_order_card/buyer_and_seller_order_card.dart';
import 'package:swadesic/features/common_buyer_seller_screen/contact_info/contact_info.dart';
import 'package:swadesic/features/widgets/no_orders_yet/no_orders_yet.dart';
import 'package:swadesic/model/order_response/store_detail_in_orders.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import '../../../model/order_response/get_order_response.dart';

// region Buyer My Order Screen
class BuyerMyOrderScreen extends StatefulWidget {
  final bool? isFromBottomSheet;
  final String? orderNumberFromNotification;

  const BuyerMyOrderScreen(
      {Key? key,
      this.isFromBottomSheet = false,
      this.orderNumberFromNotification})
      : super(key: key);

  @override
  _BuyerMyOrderScreenState createState() => _BuyerMyOrderScreenState();
}
// endregion

class _BuyerMyOrderScreenState extends State<BuyerMyOrderScreen> with AutoHideNavigationMixin<BuyerMyOrderScreen> {
  // region Bloc
  late BuyerMyOrdersBloc buyerMyOrdersBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerMyOrdersBloc =
        BuyerMyOrdersBloc(context, widget.orderNumberFromNotification);
    buyerMyOrdersBloc.init();

    // Enable auto-hide navigation and attach scroll controller
    enableAutoHideNavigation();
    attachScrollControllerToAutoHide(buyerMyOrdersBloc.scrollCtrl);

    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,
      body: SafeArea(child: body()),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      onTapLeading: () {
        widget.isFromBottomSheet!
            ? buyerMyOrdersBloc.goToFirstBottomNavigation()
            : Navigator.pop(context);
      },
      title: AppStrings.myOrders,
      isDefaultMenuVisible: true,
      isMembershipVisible: true,
      isCartVisible: true,
    );
  }

  //endregion

  // region Body
  Widget body() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(10),
          child: searchFilter(),
        ),
        Expanded(
          child: StreamBuilder<BuyerMyOrderState>(
              stream: buyerMyOrdersBloc.buyerMyOrderState.stream,
              initialData: BuyerMyOrderState.Loading,
              builder: (context, snapshot) {
                if (snapshot.data == BuyerMyOrderState.Loading) {
                  return Center(child: AppCommonWidgets.appCircularProgress());
                }
                if (snapshot.data == BuyerMyOrderState.Empty) {
                  return RefreshIndicator(
                    color: AppColors.brandBlack,
                    onRefresh: () async {
                      await buyerMyOrdersBloc.getBuyerMyOrders();
                    },
                    child: const NoOrdersYet(),
                  );
                }
                if (snapshot.data == BuyerMyOrderState.Success) {
                  return RefreshIndicator(
                      color: AppColors.brandBlack,
                      onRefresh: () async {
                        await buyerMyOrdersBloc.getBuyerMyOrders();
                      },
                      child: SingleChildScrollView(child: store()));
                }
                return RefreshIndicator(
                  color: AppColors.brandBlack,
                  onRefresh: () async {
                    await buyerMyOrdersBloc.getBuyerMyOrders();
                  },
                  child: ListView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    children: [
                      AppCommonWidgets.errorWidget(
                          errorMessage: AppStrings.unableToLoadOrders,
                          onTap: () {
                            buyerMyOrdersBloc.getBuyerMyOrders();
                          }),
                    ],
                  ),
                );
              }),
        ),
      ],
    );
  }

// endregion

  //region Search Order Id, Product and Shop Name etc.
  Widget searchFilter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Expanded(
            child: AppSearchField(
          textEditingController: buyerMyOrdersBloc.searchTextCtrl,
          isActive: true,
          hintText: AppStrings.searchOrderProduct,
          onChangeText: (value) {
            buyerMyOrdersBloc.onChangeSearchText();
            // buyerSearchBloc.onChangeTextField();
          },
          onTapSuffix: () {
            buyerMyOrdersBloc.searchTextCtrl.clear();
            CommonMethods.closeKeyboard(context);
          },
        )),
        horizontalSizedBox(10),
        SvgPicture.asset(
          AppImages.filter,
          color: AppColors.writingColor2,
          fit: BoxFit.contain,
        )
      ],
    );
  }
//endregion

  //region Store
  Widget store() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: buyerMyOrdersBloc.searchedStore.length,
        shrinkWrap: true,
        itemBuilder: (context, storeIndex) {
          var data = buyerMyOrdersBloc.searchedStore;
          return InkWell(
              onTap: () {
                buyerMyOrdersBloc.onTapOrderCard(order: data[storeIndex]);
              },
              child: BuyerAndSellerOrderCard(
                order: data[storeIndex],
                isSeller: false,
                buyerSellerOrderScreenContext: context,
                storeAndProfilePlaceHolder: AppImages.storePlaceHolder,
              ));
          // return Column(
          //   mainAxisSize: MainAxisSize.min,
          //   children: [
          //     Container(
          //       decoration: BoxDecoration(
          //         border: Border.all(color: AppColors.activeGreen,width: 2),
          //       ),
          //       child: Column(
          //         children: [
          //           Padding(
          //             padding: EdgeInsets.only(bottom: 10),
          //             child: Column(
          //               mainAxisSize: MainAxisSize.min,
          //               mainAxisAlignment: MainAxisAlignment.center,
          //               crossAxisAlignment: CrossAxisAlignment.start,
          //               children: [
          //                 //Store
          //                 InkWell(
          //                   onTap: (){
          //                     buyerMyOrdersBloc.goToStore(storeReference:data[storeIndex].subOrderList![0].storeContactInfo!.storeReference! );
          //                     //buyerMyOrdersBloc.getBuyerMyOrders(false);
          //                     //print(data[storeIndex].orderRequestNumber);
          //                   },
          //                   child: Container(
          //                     color: AppColors.activeGreen,
          //                     padding: EdgeInsets.all(10),
          //
          //                     child: Row(
          //                       mainAxisAlignment: MainAxisAlignment.center,
          //                       crossAxisAlignment: CrossAxisAlignment.center,
          //                       mainAxisSize: MainAxisSize.max,
          //                       children: [
          //                         storeImage(storeReference:data[storeIndex].subOrderList!.isEmpty?"":data[storeIndex].subOrderList!.first.storeContactInfo!.storeReference!, storeIcon: data[storeIndex].storeDetails!.storeImage),
          //                         horizontalSizedBox(15),
          //                         Expanded(
          //                             child: storeInfo(
          //                                 data[storeIndex])),
          //                       ],
          //                     ),
          //                   ),
          //                 ),
          //                 //verticalSizedBox(10),
          //               ],
          //             ),
          //           ),
          //           StreamBuilder<bool>(
          //               stream: buyerMyOrdersBloc.dropDownCtrl.stream,
          //               builder: (context, snapshot) {
          //                 return data[storeIndex].dropdownStatus!
          //                     ? StreamBuilder<bool>(
          //                     stream: buyerMyOrdersBloc.subOrderRefresh.stream,
          //                     builder: (context, snapshot) {
          //                       ///If store drop down active
          //                         return Column(
          //                           mainAxisSize: MainAxisSize.min,
          //                           children: [
          //                             viewNeedHelp(store: data[storeIndex]),
          //                             numberOfProductStatus(data[storeIndex].subOrderList!),
          //                             subOrder(store:data[storeIndex],subOrderList:data[storeIndex].subOrderList!,orderStatus:data[storeIndex].orderStatus!, orderRequestNumber:data[storeIndex].orderRequestNumber! ),
          //                           ],
          //                         );
          //                       }
          //                     )
          //                 ///Store drop down in-active
          //                     : Column(
          //                   mainAxisSize: MainAxisSize.min,
          //                       children: [
          //                         viewNeedHelp(store: data[storeIndex]),
          //                         //Number of product status
          //                        numberOfProductStatus(data[storeIndex].subOrderList!),
          //                         // verticalSizedBox(10),
          //                         ///If empty then return
          //                         // haveQuestion(subOrder:data[storeIndex].subOrderList!.isEmpty? SubOrder():data[storeIndex].subOrderList!.first ),
          //                         haveQuestion(storeReference: data[storeIndex].subOrderList!.isEmpty?"":data[storeIndex].subOrderList!.first.storeContactInfo!.storeReference!,
          //                           storeContactInfo: data[storeIndex].subOrderList!.isEmpty?StoreContactInfo():data[storeIndex].subOrderList!.first.storeContactInfo!,
          //
          //                         ),
          //
          //                       ],
          //                     );
          //               })
          //         ],
          //       ),
          //     ),
          //     verticalSizedBox(20),
          //   ],
          // );
        });
  }

  //endregion

  //region Store Image
  Widget storeImage(
      {required String storeReference, required String? storeIcon}) {
    return InkWell(
      onTap: () {
        buyerMyOrdersBloc.goToStore(storeReference: storeReference);
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: storeIcon == null
            ? Container(
                height: 50,
                width: 50,
                color: Colors.green,
              )
            : SizedBox(
                height: 50,
                width: 50,
                child: extendedImage(storeIcon, context, 100, 100)),
      ),
    );
  }

  //endregion

  //region Store info
  Widget storeInfo(Order store) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        //User info and order
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///Store Name
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(
                    width: 100,
                    child: Text(
                      "Store Name",
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          fontFamily: "LatoSemiBold",
                          color: AppColors.darkGray),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      ": ${store.storeDetails!.storeName!}",
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          fontFamily: "LatoSemiBold",
                          color: AppColors.darkGray),
                    ),
                  )
                ],
              ),
              verticalSizedBox(5),

              ///Order Number
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(
                    width: 100,
                    child: Text(
                      "Order number",
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          fontFamily: "LatoSemiBold",
                          color: AppColors.darkGray),
                    ),
                  ),

                  //Colon
                  Text(
                    ": ",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        fontFamily: "LatoSemiBold",
                        color: AppColors.darkGray),
                  ),
                  //Order number
                  Expanded(
                    child: SelectableText(
                      store.orderNumber!,
                      //overflow: TextOverflow.fade,
                      maxLines: 1,
                      //softWrap: false,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          fontFamily: "LatoSemiBold",
                          color: AppColors.darkGray),
                    ),
                  )
                ],
              ),
              verticalSizedBox(5),

              ///Order Date time
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(
                    width: 100,
                    child: Text(
                      AppStrings.orderdateTime,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          fontFamily: "LatoSemiBold",
                          color: AppColors.darkGray),
                    ),
                  ),
                  Text(
                    ": ${store.orderedDate}",
                    overflow: TextOverflow.visible,
                    maxLines: 1,
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        fontFamily: "LatoSemiBold",
                        color: AppColors.darkGray),
                  )
                ],
              ),
              verticalSizedBox(5),
            ],
          ),
        ),
        //Dropdown Icon
        StreamBuilder<bool>(
            stream: buyerMyOrdersBloc.dropDownCtrl.stream,
            builder: (context, snapshot) {
              return InkWell(
                onTap: () {
                  buyerMyOrdersBloc.dropDownChange(store);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
                  child: store.dropdownStatus!
                      ? RotatedBox(
                          quarterTurns: 2,
                          child: SvgPicture.asset(AppImages.downArrow),
                        )
                      : RotatedBox(
                          quarterTurns: 0,
                          child: SvgPicture.asset(AppImages.downArrow),
                        ),
                ),
              );
            })
      ],
    );
  }

  //endregion

  //endregion

  //region

  ///Components
  //region Components

  //region CANCELLED_BY_SELLER_BEFORE_CONFIRMED (Not confirmed and cancelled)
  Widget cancelledBySellerBeforeConfirmed(List<SubOrder> orders) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          color: AppColors.textFieldFill1,
          child: ExpandablePanel(
            theme: const ExpandableThemeData(
              animationDuration: Duration(microseconds: 100),
              iconPlacement: ExpandablePanelIconPlacement.right,
              // alignment: Alignment.bottomRight
              tapBodyToCollapse: false,
              tapHeaderToExpand: true,
              tapBodyToExpand: false,

              //useInkWell: false,
              iconPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              //iconColor: Colors.green
            ),
            //Waiting for confirmation
            ///Header
            header: Container(
              padding: const EdgeInsets.all(10),
              color: AppColors.textFieldFill1,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(AppImages.thumbUpIcon),
                  horizontalSizedBox(10),
                  Text(
                    "Not confirmed and cancelled",
                    style: TextStyle(
                      fontFamily: "LatoSemibold",
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.darkGray,
                    ),
                  ),
                  Expanded(child: horizontalSizedBox(10)),
                ],
              ),
            ),

            ///Collapsed
            collapsed: Container(
              color: AppColors.appWhite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Sorry for the disappointment, seller didn’t confirm the below order(s)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 10, left: 10, top: 10),
                    child: Text(
                      "Sorry for the disappointment, seller didn’t confirm the below order(s)",
                      style: TextStyle(
                        fontFamily: "LatoSemibold",
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: AppColors.appBlack,
                      ),
                    ),
                  ),
                  //Full refund process will start shortly.
                  Container(
                    padding: const EdgeInsets.only(left: 10, bottom: 10),
                    color: AppColors.appWhite,
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            "Full refund process will start shortly. ",
                            //overflow: TextOverflow.visible,
                            maxLines: 1,
                            style: TextStyle(
                              fontFamily: "LatoBold",
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: AppColors.brandBlack,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  //Tips
                  Padding(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: tip("How not confirming orders impact sellers"),
                  ),
                  //How refund amount is calculated?
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              //print("Hello");
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              decoration: BoxDecoration(
                                color: AppColors.inActiveGreen,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20)),
                              ),
                              child: Center(
                                child: Text(
                                  "How refund amount is calculated?",
                                  style: TextStyle(
                                      fontFamily: "LatoBold",
                                      fontWeight: FontWeight.w700,
                                      fontSize: 14,
                                      color: AppColors.brandBlack),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            ///Expanded
            expanded: Container(
              color: Colors.white,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  //Sorry for the disappointment, seller didn’t confirm the below order(s)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 10, left: 10, top: 10),
                    child: Text(
                      "Sorry for the disappointment, seller didn’t confirm the below order(s)",
                      style: TextStyle(
                        fontFamily: "LatoSemibold",
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: AppColors.appBlack,
                      ),
                    ),
                  ),
                  //Full refund process will start shortly.
                  Container(
                    padding: const EdgeInsets.only(left: 10, bottom: 10),
                    color: AppColors.appWhite,
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            "Full refund process will start shortly. ",
                            //overflow: TextOverflow.visible,
                            maxLines: 1,
                            style: TextStyle(
                              fontFamily: "LatoBold",
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: AppColors.brandBlack,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  //Tips
                  Padding(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: tip("How not confirming orders impact sellers"),
                  ),
                  //How refund amount is calculated?
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              //print("Hello");
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              decoration: BoxDecoration(
                                color: AppColors.inActiveGreen,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20)),
                              ),
                              child: Center(
                                child: Text(
                                  "How refund amount is calculated?",
                                  style: TextStyle(
                                      fontFamily: "LatoBold",
                                      fontWeight: FontWeight.w700,
                                      fontSize: 14,
                                      color: AppColors.brandBlack),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  //Sub-order list
                  Container(
                    color: AppColors.appWhite,
                    child: ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        shrinkWrap: true,
                        itemCount: orders.length,
                        itemBuilder: (BuildContext, index) {
                          return Column(
                            children: [
                              //Divider
                              Container(
                                color: AppColors.appWhite,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 10),
                                  child: divider(),
                                ),
                              ),
                              //Select and color change
                              InkWell(
                                // onLongPress: (){
                                //   //buyerMyOrdersBloc.selectedSubOrderNumberList.add(orders[index].suborderNumber!);
                                //   buyerMyOrdersBloc.selectedSubOrderNumberList.isEmpty?buyerMyOrdersBloc.onLongPress(orders[index].suborderNumber!):null;
                                // },
                                // onTap: (){
                                //   buyerMyOrdersBloc.onTapProduct(orders[index].suborderNumber!);
                                // },
                                child: Container(
                                  decoration: BoxDecoration(
                                      color: buyerMyOrdersBloc
                                              .selectedSubOrderNumberList
                                              .contains(
                                                  orders[index].suborderNumber!)
                                          ? AppColors.tertiaryGreen
                                          : AppColors.appWhite,
                                      //color: AppColors.green3,
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(10))),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      //Updated on 24-01-2021
                                      Container(
                                        padding:
                                            const EdgeInsets.only(bottom: 10),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                "Updated on  ${orders[index].cancelledDate == null ? '' : CommonMethods.convertStringDateTimeSlashFormat(orders[index].cancelledDate!)}",

                                                // "Updated on 24-01-2021",
                                                //overflow: TextOverflow.visible,
                                                maxLines: 1,
                                                style: TextStyle(
                                                  fontFamily: "LatoSemibold",
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w600,
                                                  color: AppColors.appBlack,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      //You will receive ₹ 450 as the refund amount
                                      Container(
                                        padding:
                                            const EdgeInsets.only(bottom: 10),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                "You will receive ₹ 450 as the refund amount",
                                                //overflow: TextOverflow.visible,
                                                maxLines: 1,
                                                style: TextStyle(
                                                  fontFamily: "LatoBold",
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w600,
                                                  color: AppColors.appBlack,
                                                ),
                                              ),
                                            ),
                                            SvgPicture.asset(
                                                AppImages.exclamation)
                                          ],
                                        ),
                                      ),

                                      buyerSubOrderCard(
                                        context: context,
                                        subOrder: orders[index],
                                      ),
                                      //Your reason: Reason mentioned
                                      Text(
                                        "Seller reason: ${orders[index].cancellationReason == null ? '' : orders[index].cancellationReason!}",
                                        textAlign: TextAlign.start,
                                        style: TextStyle(
                                            fontFamily: "LatoRegular",
                                            fontWeight: FontWeight.w400,
                                            fontSize: 14,
                                            color: AppColors.appBlack),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          );
                        }),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  //endregion

//endregion

  //region View order detail and need Help
  Widget viewNeedHelp({required Order store}) {
    return Container(
      color: AppColors.appWhite,
      child: Padding(
        padding: const EdgeInsets.only(left: 10, right: 10),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: InkWell(
                onTap: () {
                  buyerMyOrdersBloc.customerAndOrderDetailsBottomSheet(
                      orderNumber: store.orderNumber!);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    color: AppColors.textFieldFill1,
                    borderRadius: BorderRadius.all(Radius.circular(20)),
                  ),
                  child: Center(
                    child: Text(
                      AppStrings.viewOrderDetails,
                      style: TextStyle(
                          fontFamily: "LatoSemibold",
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          color: AppColors.writingColor3),
                    ),
                  ),
                ),
              ),
            ),
            horizontalSizedBox(10),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                ),
                child: Center(
                  child: Text(
                    AppStrings.needHelp,
                    style: TextStyle(
                        fontFamily: "LatoSemibold",
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        color: AppColors.writingColor3),
                  ),
                ),
              ),
            ),
            CupertinoButton(
                onPressed: () {
                  buyerMyOrdersBloc.goToOrderHistory(
                      subOrderList: store.subOrderList!);
                },
                padding: EdgeInsets.zero,
                child: SvgPicture.asset(AppImages.drawerIcon))
          ],
        ),
      ),
    );
  }
  //endregion

  //region Have a question Ask Seller
  Widget haveQuestion(
      {required String storeReference,
      required StoreContactInfo storeContactInfo}) {
    return InkWell(
      onTap: () async {
        await CommonMethods.appMinimumBottomSheets(
            bottomSheetName: AppStrings.contactDetails,
            screen: ContactInfo(
                phoneNumbers: storeContactInfo.phoneNumber ?? [],
                email: storeContactInfo.emailId ?? []),
            context: context);
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 25),
            child: Divider(
              color: AppColors.activeGreen,
              height: 1,
              thickness: 1,
            ),
          ),
          verticalSizedBox(15),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "Have a question",
                  textAlign: TextAlign.left,
                  style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      fontFamily: "LatoRegular",
                      color: AppColors.darkGray),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                      border:
                          Border.all(color: AppColors.lightStroke, width: 1.5)),
                  child: Text("Ask seller",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        fontFamily: "LatoBold",
                        color: AppColors.writingColor2,
                      )),
                )
              ],
            ),
          ),
          //verticalSizedBox(20),
          // Divider(
          //   color: AppColors.green,
          //   height: 1,
          //   thickness: 1,
          // ),
          verticalSizedBox(15)
        ],
      ),
    );
  }
//endregion

  //region Dispose
  @override
  void dispose() {
    // Detach scroll controller and disable auto-hide navigation
    detachScrollControllerFromAutoHide(buyerMyOrdersBloc.scrollCtrl);
    disableAutoHideNavigation();

    super.dispose();
  }
  //endregion
}
