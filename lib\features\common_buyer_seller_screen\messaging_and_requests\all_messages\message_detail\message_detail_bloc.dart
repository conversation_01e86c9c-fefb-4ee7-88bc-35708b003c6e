import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_get_all_message_pagination.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_send_message.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/message_chat_data_model/message_chat_data_model.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/messaging_response/messaging_detail.dart';
import 'package:swadesic/services/messaging_services/messaging_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

//region Message Detail Bloc

class MessageDetailBloc {
  BuildContext context;

  //region Variables
  // Stream controllers
  final messageDetailStateCtrl = StreamController<AppStateEnum>.broadcast();
  final paginationStateCtrl = StreamController<AppStateEnum>.broadcast();
  AppStateEnum paginationCurrentStateCtrl = AppStateEnum.Loading;
  final TextEditingController messageController = TextEditingController();
  final String toEntityReference;
  String roomId = "";

  ScrollController scrollController = ScrollController();

  List<ChatDetail> messagesList = [];
  late MessageDetailResponse messageDetailResponse;

  late MessageChatDataModel messageChatDataModel;
  WebSocketChannel? _channel;

  //endregion

  //region Constructor
  MessageDetailBloc(this.context, this.toEntityReference);

  //endregion

  //region Init
  void init() async{

    messageChatDataModel = Provider.of<MessageChatDataModel>(context, listen: false);
    //Web socket
    String entityReference = AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!;
    // final wsUrl = Uri.parse("${AppConstants.getAllMessagesWebSocket}?entity_reference=$entityReference");
    // _channel = WebSocketChannel.connect(wsUrl);

    final wsUrl = Uri.parse("${AppConstants.getAllMessagesWebSocket}${AppConstants.appData.messagingToken}");
    print('Connecting to WebSocket: $wsUrl');
    _channel = WebSocketChannel.connect(wsUrl);
    print('Connected to WebSocket.');

    //Get initial message
    await MessageDetailGetAllMessagePagination(messageDetailBloc: this,  context: context).getInitialMessageList();
    //Scroll to the end of the list
    // Scroll to the end after the first frame
    await Future.delayed(Duration(milliseconds: 100));
    scrollAllTheWayDown();
    //Get single message from web socket and save it in data model and in messagesList list
    getSingleMessage();

    //Send initial message to get unread messaeg
    requestGetUnreadMessageCount();

    //Pagination scroll listener
    scrollController.addListener(() {

      //If scroll is at top then call a method
      if (scrollController.position.pixels == scrollController.position.minScrollExtent && paginationCurrentStateCtrl != AppStateEnum.Empty) {
        //print("Touched to the top");
        //Get pagination message
        MessageDetailGetAllMessagePagination(messageDetailBloc: this,  context: context).getPaginationMessageList();
      }
    });
  }

  //endregion


  //region Request to get unread messaeg count
  void requestGetUnreadMessageCount(){
    String entityReference = AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!;

    try {
      if (_channel == null) {
        //print('WebSocket not connected');
        return;
      }

      final messageData = {
        "type": "get_unread_conversations",
        "entity_reference": entityReference,
      };

      // Send the message through WebSocket
      if (_channel != null) {
        _channel!.sink.add(jsonEncode(messageData));
        debugPrint("Unread messaeg count request sent");
      }
      else{
        //print("channel is closed");
      }

    } catch (e) {
      //print('Error sending message through WebSocket: $e');
    }
  }
  //endregion


  //region Add message
  void addMessage() async {

    String fromEntityReference = AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!;

    ChatDetail chatDetail = ChatDetail(
      text: messageController.text,
      messageId: DateTime.now().toString(),
      timestamp: DateTime.now().toString(),
      senderReference: fromEntityReference,
      isDeliveredToServer: false

    );
    //Add chatDetail in messagesList
    messagesList.add(chatDetail);
    //Send message
    MessageDetailSendMessage(chatDetail:chatDetail,context: context,toEntityReference: toEntityReference );

    //Success
    messageDetailStateCtrl.sink.add(AppStateEnum.Success);

    //Scroll all the way down
    scrollAllTheWayDown();

    //Clear text ctrl
    messageController.clear();
  }

  //endregion

  //region Add message with attachments
  void addMessageWithAttachments(String message, List<Map<String, dynamic>> attachments) {
    if (message.trim().isEmpty && attachments.isEmpty) return;

    // Create a new chat detail with a temporary messageId
    ChatDetail chatDetail = ChatDetail(
      text: message,
      messageId: DateTime.now().millisecondsSinceEpoch.toString(), 
      timestamp: DateTime.now().toUtc().toIso8601String(),
      senderReference: AppConstants.appData.isUserView! 
        ? AppConstants.appData.userReference! 
        : AppConstants.appData.storeReference!,
      isDeliveredToServer: false,
      attachments: attachments.map((attachment) => ChatAttachment.fromJson(attachment)).toList(),
    );

    // Add to messages list
    messagesList.add(chatDetail);

    // Send message through WebSocket
    MessageDetailSendMessage(
      chatDetail: chatDetail,
      context: context,
      toEntityReference: toEntityReference,
    );

    // Clear the message input
    messageController.clear();
  }

  //endregion

  //region Get single recently added message and save it in data model and in messagesList list
  void getSingleMessage() async {
    try {
      // Listen to WebSocket stream
      _channel!.stream.listen((dynamic myData) {
        // Decode the incoming JSON string into a Map
        final Map<String, dynamic> decodedResponse = jsonDecode(myData);

        /// Check the type and process the data
        if (decodedResponse['type'] != null && decodedResponse['type'] == 'new_incoming_messages') {
          // Extract the list of new incoming messages
          final List<dynamic> newMessages = decodedResponse['data']['new_incoming_messages'];

          //Update last seen
          messageDetailResponse.activeStatusString =  decodedResponse['data']['active_status_string'];
          //print("current user online status is ${decodedResponse['data']['active_status_string']}");

          // Filter messages with room_id == "1" and map them to ChatDetail
          for (var message in newMessages) {
            if (message['room_id'] == messageDetailResponse.dmMetaData!.data!.roomId) {
              // Create a ChatDetail object
              final ChatDetail chatDetail = ChatDetail(
                messageId: message['message_id'],
                text: message['text'],
                timestamp: CommonMethods.convertUtcToIst(message['timestamp']),
                senderReference: message['sender_reference'],
              );
              // Add to the messages list
              messagesList.add(chatDetail);
            }
          }

          //Add to data model
          messageChatDataModel.addChatInList(dataList: messagesList);

          //Scroll all the way down
          scrollAllTheWayDown();
          //Update app bar ui
          messageDetailStateCtrl.sink.add(AppStateEnum.Success);

          //print("Filtered messages added! Total messages: ${messagesList.length}");
        }
        ///If get_unread_conversations
        if (decodedResponse['type'] != null && decodedResponse['type'] == 'get_unread_conversations') {
          late LoggedInUserInfoDataModel loggedInUserInfoDataModel = Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

          debugPrint("Unread message updated and value is ${decodedResponse['unread_count']}");
          //Un-read count update
          loggedInUserInfoDataModel.userDetail!.unreadConversationsCount = decodedResponse['unread_count'];
          //Update ui
          loggedInUserInfoDataModel.updateUi();
        }
      });
    } catch (e) {
      //print('Error connecting to WebSocket: $e');
      // Handle error appropriately, such as updating the state to failed
      // allMessagesStateCtrl.sink.add(AllMessagesState.Failed);
    }
  }
  //endregion

  //region Scroll all the way down
  void scrollAllTheWayDown() {
    if (scrollController.hasClients && scrollController.position.maxScrollExtent > 0) {
      scrollController.animateTo(
        scrollController.position.maxScrollExtent + 200,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOut,
      );
    }
  }
  //endregion

  //region Go to store screen
  void goToStore({required String storeReference}) {
    var screen = BuyerViewStoreScreen(storeReference: storeReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Go to user profile
  void goToUserProfile({required String userReference}) {
    var screen = UserProfileScreen(
      userReference: userReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Dispose
  void dispose() {
    messageDetailStateCtrl.close();
    messageController.dispose();
    scrollController.dispose();
  }
//endregion
}
//endregion
