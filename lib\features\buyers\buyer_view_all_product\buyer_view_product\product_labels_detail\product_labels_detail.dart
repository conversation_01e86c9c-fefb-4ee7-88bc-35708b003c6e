import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_labels_detail/product_labels_detail_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

//region Product label detail
class ProductLabelsDetail extends StatefulWidget {
  final bool isMade;
  final bool isBrand;
  final bool isOwned;
  final String currentFlag;

  const ProductLabelsDetail({
    super.key,
    this.isMade = false,
    this.isBrand = false,
    this.isOwned = false,
    required this.currentFlag,
  });

  @override
  State<ProductLabelsDetail> createState() => _ProductLabelsDetailState();
}
//endregion

class _ProductLabelsDetailState extends State<ProductLabelsDetail> {
  //region Bloc
  late ProductLabelsDetailBloc productLabelsDetailBloc;
  //endregion

  //region Init
  @override
  void initState() {
    productLabelsDetailBloc = ProductLabelsDetailBloc(context, widget.isMade,
        widget.isBrand, widget.isOwned, widget.currentFlag);
    productLabelsDetailBloc.init();
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: productLabelsDetailBloc.refreshLabelCtrl.stream,
        builder: (context, snapshot) {
          return ListView(
            children: [
              ///Made
              made(),

              ///Core
              core(),

              ///Notes
              note(),
            ],
          );
        });
  }
//endregion

  //region Core
  Widget core() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Text(
        AppStrings.theCoreIntention,
        style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      ),
    );
  }
  //endregion

//region Made
  Widget made() {
    return Visibility(
      visible: true,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.appBlack,
            width: 1,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
        child: Table(
          // border: TableBorder.all(), // Add a border around the table
          border: TableBorder.symmetric(
            inside: const BorderSide(width: 1),
          ),
          children: productLabelsDetailBloc.tableRowList,
        ),
      ),
    );
  }
//endregion

//region Note
  Widget note() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
      child: Text(
        AppStrings.noteTheLabelsAreGiving,
        style: AppTextStyle.smallText(textColor: AppColors.appBlack),
      ),
    );
  }
//endregion
}
