import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:rxdart/rxdart.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_scroll_demo.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/messaging_response/all_message_response.dart';
import 'package:swadesic/services/messaging_services/messaging_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

enum MessagePaginationState { Loading, Done, Empty }

enum AllMessagesState { Loading, Success, Failed, Empty }

class AllMessagesBloc {
  final BuildContext context;
  final scrollController = ScrollController();
  final messagesPaginationStateCtrl = BehaviorSubject<MessagePaginationState>();
  final allMessagesStateCtrl = BehaviorSubject<AllMessagesState>();
  late AllMessageResponse allMessageResponse;

  // List<PreviousMessageDetail> messagesList = [];
  int offset = 0;
  final int limit = 20;
  bool isLoadingPaginationData = false;
  MessagePaginationState currentPaginationState = MessagePaginationState.Loading;

  WebSocketChannel? _channel;

  // StreamSubscription? _subscription;
  // final _allMessagesController = StreamController<List<PreviousMessageDetail>>.broadcast();
  //
  // Stream<List<PreviousMessageDetail>> get allMessagesStream => _allMessagesController.stream;

  AllMessagesBloc(this.context) {
    // scrollController.addListener(_scrollListener);
  }

  void init() async {
    //await getAllMessages();



    //Web socket
    // String entityReference = AppConstants.appData.isUserView! ? AppConstants.appData.userReference! : AppConstants.appData.storeReference!;
    final wsUrl = Uri.parse("${AppConstants.getAllMessagesWebSocket}${AppConstants.appData.messagingToken}");
    debugPrint(wsUrl.toString());
    _channel = WebSocketChannel.connect(wsUrl);
    connectToAllMessagesWebSocket();
  }

  //region Get all messages
  // Future<void> getAllMessages() async {
  //   try {
  //     //Api call
  //     allMessageResponse = await MessagingService().getAllMessages(
  //       limit: limit,
  //       offset: offset,
  //     );
  //
  //     //If no message are there then Empty
  //     if (allMessageResponse.data!.previousMessageDetailList!.isEmpty) {
  //       return allMessagesStateCtrl.sink.add(AllMessagesState.Empty);
  //     }
  //
  //     //Success
  //     allMessagesStateCtrl.sink.add(AllMessagesState.Success);
  //   } on ApiErrorResponseMessage catch (error) {
  //     //Failed
  //     allMessagesStateCtrl.sink.add(AllMessagesState.Failed);
  //     // context.mounted ? CommonMethods.toastMessage(error.message!, context) : null;
  //   } catch (error) {
  //     //Failed
  //     allMessagesStateCtrl.sink.add(AllMessagesState.Failed);
  //     // context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
  //   }
  // }

  //endregion

  //region Connect to WebSocket for All Messages
  void connectToAllMessagesWebSocket() {
    try {
      // Listen to WebSocket stream
      _channel!.stream.listen((dynamic myData) {
          // Decode the incoming JSON string into a Map
          final Map<String, dynamic> decodedResponse = jsonDecode(myData);
          // Check the type and process the data
          if (decodedResponse['type'] != null && decodedResponse['type'] == 'dm_list_update') {
            // Process the data
            allMessageResponse = AllMessageResponse.fromJson(decodedResponse);
            //Empty
             if (allMessageResponse.data!.dmList!.isEmpty && allMessageResponse.data!.recommendations!.isEmpty) {
               allMessagesStateCtrl.sink.add(AllMessagesState.Empty);
             }
             //Success
             else {
               allMessagesStateCtrl.sink.add(AllMessagesState.Success);
             }
          }
      });
    } catch (e) {
      //print('Error connecting to WebSocket: $e');
      allMessagesStateCtrl.sink.add(AllMessagesState.Failed);
    }
  }

  //endregion


  //region Format date time
  String formatDateTime(String dateTime) {
    try {
      final inputDateTime = DateTime.parse(dateTime);
      final now = DateTime.now();

      if (inputDateTime.year == now.year && inputDateTime.month == now.month && inputDateTime.day == now.day) {
        // Return hour and minute with AM/PM if the date is today
        return DateFormat('hh:mm a').format(inputDateTime);
      } else if (inputDateTime.year == now.year) {
        // Return "10th Nov" if the date is within this year
        return DateFormat('d MMM').format(inputDateTime);
      } else {
        // Return "10th Nov 2023" if the date is older than this year
        return DateFormat('d MMM yyyy').format(inputDateTime);
      }
    } catch (e) {
      return "";
    }
  }

  //endregion

  //region Go to message Navigation
  void goToMessageDetail({required String toEntityReference}) {
    var screen = MessageDetailScreen(
      toEntityReference: toEntityReference,
    );
    // var screen = MessageScrollDemo();
    // var screen = MessageScrollDemo();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }

  //endregion


  //region Dispose
  void dispose() {
    _channel?.sink.close();
    _channel = null;
    allMessagesStateCtrl.close();
    messagesPaginationStateCtrl.close();
    scrollController.dispose();
  }
//endregion
}
