import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:swadesic/features/navigation/widgets/custom_bottom_nav.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';

void main() {
  group('Navigation System Tests', () {
    setUp(() {
      // Initialize app constants for testing
      AppConstants.appData.isUserView = true;
      AppConstants.appData.isStoreView = false;
      AppConstants.appData.userReference = 'test_user_ref';
      AppConstants.appData.storeReference = null;
      AppConstants.appData.storeId = null;
    });

    testWidgets('CustomBottomNav renders correctly',
        (WidgetTester tester) async {
      const tabIcons = [
        AppImages.homeIconOutlined,
        AppImages.searchIconOutlined,
        AppImages.ordersIconOutlined,
        AppImages.notificationIconOutlined,
      ];
      const tabNames = ["Home", "Search", "Notifications", "Add"];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            bottomNavigationBar: CustomBottomNav(
              currentIndex: 0,
              onTabSelected: (index) {},
              onDoubleTap: (index) {},
              onHorizontalSwipe: (velocity) {},
              onLongPress: (index) {},
              onIconSwipe: (index) {},
              tabIcons: tabIcons,
              tabNames: tabNames,
              profileWidget: const ProfileAvatar(
                isSelected: false,
                isStore: false,
              ),
            ),
          ),
        ),
      );

      // // Verify that all tab icons are present
      // for (final icon in tabIcons) {
      //   expect(find.byIcon(icon), findsOneWidget);
      // }

      // Verify profile avatar is present
      expect(find.byType(ProfileAvatar), findsOneWidget);
    });

    testWidgets('Tab selection works correctly', (WidgetTester tester) async {
      int selectedIndex = 0;
      const tabIcons = [
        AppImages.homeIconOutlined,
        AppImages.searchIconOutlined,
        AppImages.ordersIconOutlined,
        AppImages.notificationIconOutlined,
      ];
      const tabNames = ["Home", "Search", "Notifications", "Add"];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            bottomNavigationBar: CustomBottomNav(
              currentIndex: selectedIndex,
              onTabSelected: (index) {
                selectedIndex = index;
              },
              onDoubleTap: (index) {},
              onHorizontalSwipe: (velocity) {},
              onLongPress: (index) {},
              onIconSwipe: (index) {},
              tabIcons: tabIcons,
              tabNames: tabNames,
              profileWidget: const ProfileAvatar(
                isSelected: false,
                isStore: false,
              ),
            ),
          ),
        ),
      );

      // Tap on search icon (index 1)
      await tester.tap(find.byIcon(Icons.search));
      await tester.pump();

      // Note: In a real test, we would need to rebuild the widget with the new selectedIndex
      // This is a simplified test to verify the tap gesture is recognized
      expect(find.byIcon(Icons.search), findsOneWidget);
    });

    testWidgets('ProfileAvatar displays correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProfileAvatar(
              isSelected: true,
              isStore: false,
              imageUrl: null, // Use null to avoid network requests in tests
            ),
          ),
        ),
      );

      expect(find.byType(CircleAvatar), findsOneWidget);
    });

    test('App constants role detection works correctly', () {
      // Test buyer role
      AppConstants.appData.isUserView = true;
      AppConstants.appData.isStoreView = false;

      expect(AppConstants.appData.isUserView, isTrue);
      expect(AppConstants.appData.isStoreView, isFalse);

      // Test seller role
      AppConstants.appData.isUserView = false;
      AppConstants.appData.isStoreView = true;

      expect(AppConstants.appData.isUserView, isFalse);
      expect(AppConstants.appData.isStoreView, isTrue);
    });
  });
}
