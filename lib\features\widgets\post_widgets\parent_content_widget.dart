import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/parent_card.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

/// Widget to display parent content (posts, comments, products)
class ParentContentWidget extends StatelessWidget {
  final dynamic content;
  final bool isLast;
  final VoidCallback? onTap;

  const ParentContentWidget({
    Key? key,
    required this.content,
    this.isLast = false,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (content is Product) {
      return _buildMiniProductCard(context, content as Product);
    } else if (content is PostDetail) {
      return _buildPostCommentCard(context, content as PostDetail);
    }
    return const SizedBox();
  }

  /// Builds mini product card similar to Feed
  Widget _buildMiniProductCard(BuildContext context, Product product) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.lightGray, width: 0.5),
        boxShadow: [
          BoxShadow(
            color: AppColors.appBlack.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap ?? () => _navigateToProduct(context, product),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Product image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  height: 60,
                  width: 60,
                  color: AppColors.textFieldFill1,
                  child: product.prodImages?.isNotEmpty == true
                      ? extendedImage(
                          product.prodImages!.first.productImage ?? '',
                          context,
                          60,
                          60,
                          customPlaceHolder: AppImages.productPlaceHolder,
                          fit: BoxFit.cover,
                        )
                      : SvgPicture.asset(
                          AppImages.productPlaceHolder,
                          fit: BoxFit.cover,
                        ),
                ),
              ),
              const SizedBox(width: 12),
              // Product details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product name
                    RichText(
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: product.brandName ?? '',
                            style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack,
                            ),
                          ),
                          TextSpan(
                            text: ' ${product.productName ?? ''}',
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),
                    // Store info
                    Row(
                      children: [
                        Text(
                          'by @${product.storehandle ?? ''}',
                          style: AppTextStyle.smallTextRegular(
                            textColor: AppColors.writingBlack0,
                          ),
                        ),
                        // Note: Product model doesn't have subscription/verification info
                        // Store verification would need to be fetched separately
                      ],
                    ),
                    const SizedBox(height: 4),
                    // Price
                    if (product.sellingPrice != null)
                      Text(
                        '₹${product.sellingPrice}',
                        style: AppTextStyle.contentHeading0(
                          textColor: AppColors.brandGreen,
                        ),
                      ),
                  ],
                ),
              ),
              // Arrow icon
              Icon(
                Icons.keyboard_arrow_right,
                color: AppColors.lightGray,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a post/comment card using the same UI as CommentCard
  Widget _buildPostCommentCard(BuildContext context, PostDetail postDetail) {
    // Create SinglePostViewBloc with required parameters
    // For parent posts, we're not in a product screen context, so isFromProduct is false
    final singlePostViewBloc = SinglePostViewBloc(
      context,
      postDetail.postOrCommentReference ?? '',
      false, // isFromProduct
    );
    
    return ParentCard(
      postDetail: postDetail,
      onTapEdit: () {},
      onTapDelete: () {},
      onTapReport: () {},
      onTapHeart: () {},
      onTapDrawer: () {},
      onTapProfileImage: () {
        // Handle profile image tap if needed
      },
      onTapShare: () {},
      onTapPost: () {
        if (onTap != null) {
          onTap!();
        } else {
          _navigateToPost(context, postDetail);
        }
      },
      onTapReply: () {
        // Handle reply tap if needed
      },
      singlePostViewBloc: singlePostViewBloc,
      isParentPost: true, // Mark this as a parent post to hide view more replies
    );
  }

  /// Navigate to product screen
  void _navigateToProduct(BuildContext context, Product product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BuyerViewSingleProductScreen(
          productReference: product.productReference!,
        ),
      ),
    );
  }

  /// Navigate to post screen
  void _navigateToPost(BuildContext context, PostDetail postDetail) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SinglePostViewScreen(
          postReference: postDetail.postOrCommentReference ?? '',
        ),
      ),
    );
  }

  /// Get comment type color
  Color _getCommentTypeColor(String commentType) {
    switch (commentType.toUpperCase()) {
      case 'QUESTION':
        return AppColors.brandColor2; // Blue color
      case 'REVIEW':
      case 'EXTERNAL_REVIEW':
        return AppColors.brandGreen;
      default:
        return AppColors.appBlack;
    }
  }

  /// Get comment type label
  String _getCommentTypeLabel(String commentType) {
    switch (commentType.toUpperCase()) {
      case 'QUESTION':
        return 'Question';
      case 'REVIEW':
        return 'Review';
      case 'EXTERNAL_REVIEW':
        return 'External Review';
      case 'COMMENT':
        return 'Comment';
      default:
        return commentType;
    }
  }
}
