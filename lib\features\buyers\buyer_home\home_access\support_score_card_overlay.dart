import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path_provider/path_provider.dart';

import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/level_badge/level_badge.dart';
import 'package:swadesic/model/contribution_response/contribution_response.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';

class SupportScoreCardOverlay extends StatefulWidget {
  final String? userReference;
  final dynamic userDetail;

  const SupportScoreCardOverlay({
    Key? key,
    this.userReference,
    this.userDetail,
  }) : super(key: key);

  @override
  State<SupportScoreCardOverlay> createState() =>
      _SupportScoreCardOverlayState();
}

class _SupportScoreCardOverlayState extends State<SupportScoreCardOverlay> {
  final GlobalKey _cardKey = GlobalKey();
  bool _isCapturingImage = false; // Flag to hide share button during capture

  // API related state
  List<ContributionItem> _userContributions = [];
  bool _isLoadingContributions = true;
  final UserDetailsServices _userDetailsService = UserDetailsServices();

  @override
  void initState() {
    super.initState();
    _fetchUserContributions();
  }

  Future<void> _fetchUserContributions() async {
    try {
      // Use the provided user reference or fall back to current user's reference
      final userReference =
          widget.userReference ?? AppConstants.appData.userReference;
      if (userReference != null && userReference.isNotEmpty) {
        final response = await _userDetailsService.getUserContributions(
          userReference: userReference,
        );

        if (mounted) {
          setState(() {
            _userContributions = response.data ?? [];
            _isLoadingContributions = false;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoadingContributions = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error fetching user contributions: $e');
      if (mounted) {
        setState(() {
          _isLoadingContributions = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Blurred background with tap to close
        Positioned.fill(
          child: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 8, sigmaY: 8),
              child: Container(
                color: AppColors.appBlack.withOpacity(0.3),
              ),
            ),
          ),
        ),
        // Card content
        Center(
          child: GestureDetector(
            onTap: () {
              // Prevent tap from propagating to the background
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: RepaintBoundary(
                key: _cardKey,
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.appWhite,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      // Bottom shadow
                      BoxShadow(
                        color: AppColors.appBlack.withOpacity(0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                      // Top shadow
                      BoxShadow(
                        color: AppColors.appBlack.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(0, -5),
                      ),
                      // Left shadow
                      BoxShadow(
                        color: AppColors.appBlack.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(-5, 0),
                      ),
                      // Right shadow
                      BoxShadow(
                        color: AppColors.appBlack.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(5, 0),
                      ),
                    ],
                  ),
                  child: _buildCardContent(context),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCardContent(BuildContext context) {
    // Use provided user detail or fall back to BuyerHomeBloc for current user
    final userDetail =
        widget.userDetail ?? BuyerHomeBloc.userDetailsResponse.userDetail;

    if (userDetail == null) {
      return const SizedBox();
    }

    // Get support score
    final supportScore = userDetail.userSupportScore ?? 0.0;

    // Level ranges mapping
    final Map<int, Map<String, double>> levelRanges = {
      1: {'min': 0, 'max': 10000},
      2: {'min': 10000, 'max': 50000},
      3: {'min': 50000, 'max': 100000},
      4: {'min': 100000, 'max': 200000},
      5: {'min': 200000, 'max': 500000},
      6: {'min': 500000, 'max': 700000},
      7: {'min': 700000, 'max': 1000000},
      8: {'min': 1000000, 'max': 1500000},
      9: {'min': 1500000, 'max': 1500000}, // Max level
    };

    // Determine current level based on support score
    int currentLevel = 1;
    for (int level = 1; level <= 9; level++) {
      final range = levelRanges[level]!;
      if (supportScore >= range['min']! &&
          (level == 9 || supportScore < levelRanges[level + 1]!['min']!)) {
        currentLevel = level;
        break;
      }
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Profile section
        _buildProfileSection(userDetail, currentLevel),

        verticalSizedBox(20),

        // Support score section
        _buildSupportScoreSection(supportScore, currentLevel),

        verticalSizedBox(5),

        // Contributions section
        _buildContributionsSection(),

        verticalSizedBox(20),

        // Date section
        _buildDateSection(),

        verticalSizedBox(20),

        // Bottom action row (hidden during image capture)
        if (!_isCapturingImage) _buildBottomActionRow(context, userDetail),
      ],
    );
  }

  Widget _buildProfileSection(userDetail, int currentLevel) {
    return Column(
      children: [
        // Profile image, shuffle icon, and level badge row
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Profile image with circular background
            Container(
              width: 90,
              height: 90,
              child: Center(
                child: CustomImageContainer(
                  width: 90,
                  height: 90,
                  imageUrl: userDetail.icon,
                  imageType: CustomImageContainerType.user,
                ),
              ),
            ),

            horizontalSizedBox(20),

            // Shuffle icon
            SvgPicture.asset(
              AppImages.shuffleIcon,
              width: 40,
              height: 40,
              color: AppColors.appBlack,
            ),

            horizontalSizedBox(20),

            // Level badge using the LevelBadge widget
            LevelBadge(
              level: currentLevel.toString(),
              badgeType: LevelBadgeType.user,
              width: 92,
              height: 92,
              fontSize: 64,
              borderWidth: 6.1,
            ),
          ],
        ),

        verticalSizedBox(16),

        // Name
        Text(
          userDetail.displayName ?? userDetail.firstName ?? "User",
          style: AppTextStyle.usernameHeading(textColor: AppColors.appBlack),
        ),
        // Handle
        Text(
          "@${userDetail.userName ?? "user"}",
          style: AppTextStyle.access0(textColor: AppColors.brandBlack),
        ),

        verticalSizedBox(12),

        // Level description text
        Text(
          "Level $currentLevel Community member of Swadeshi Mission with Support Score more than ${_getMinScoreForLevel(currentLevel)}",
          style: AppTextStyle.access0(textColor: AppColors.appBlack),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  Widget _buildSupportScoreSection(double supportScore, int currentLevel) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "Swadeshi Contributions:",
          style: AppTextStyle.access0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }

  Widget _buildContributionsSection() {
    // Show loading indicator while fetching data
    if (_isLoadingContributions) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: CircularProgressIndicator(
            color: AppColors.brandBlack,
          ),
        ),
      );
    }

    // Show "Soon to be available" if no data
    if (_userContributions.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Text(
            "Soon to be available",
            style: AppTextStyle.smallText(textColor: AppColors.writingBlack0),
          ),
        ),
      );
    }

    // Show API data
    return Column(
      children: [
        for (int i = 0; i < _userContributions.length; i += 2)
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Expanded(
                  child: _buildContributionItem(
                    _userContributions[i].label ?? '',
                    _userContributions[i].value ?? '',
                  ),
                ),
                horizontalSizedBox(20),
                Expanded(
                  child: i + 1 < _userContributions.length
                      ? _buildContributionItem(
                          _userContributions[i + 1].label ?? '',
                          _userContributions[i + 1].value ?? '',
                        )
                      : const SizedBox(),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildContributionItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyle.smallText(textColor: AppColors.writingBlack0),
        ),
        verticalSizedBox(4),
        Text(
          value,
          style: AppTextStyle.smallText(textColor: AppColors.appBlack),
        ),
      ],
    );
  }

  Widget _buildBottomActionRow(BuildContext context, userDetail) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // // Learn how to level up text
        // GestureDetector(
        //   onTap: () {
        //     FaqNavigation.navigateToFaqQuestion(
        //         context, 'general', 'general_cancellation');
        //   },
        //   child: Text(
        //     "Learn how to level up!",
        //     style:
        //         AppTextStyle.subTitle(textColor: AppColors.brandGreen).copyWith(
        //       decoration: TextDecoration.underline,
        //       decorationColor: AppColors.brandGreen,
        //     ),
        //   ),
        // ),
        // horizontalSizedBox(30),
        // Share button
        GestureDetector(
          onTap: () {
            _shareCard(context, userDetail);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                AppImages.chainIcon,
                width: 16,
                height: 16,
                color: AppColors.appBlack,
              ),
              horizontalSizedBox(3),
              Text(
                "Share this card",
                style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Method to capture the card as an image
  Future<File?> _captureCardAsImage() async {
    try {
      // Get the boundary before any async operations
      final context = _cardKey.currentContext;
      if (context == null) return null;

      RenderRepaintBoundary boundary =
          context.findRenderObject() as RenderRepaintBoundary;

      // Hide share button during capture
      setState(() {
        _isCapturingImage = true;
      });

      // Wait for UI to update
      await Future.delayed(const Duration(milliseconds: 100));

      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      // Get application documents directory
      final directory = await getApplicationDocumentsDirectory();
      final path =
          '${directory.path}/support_score_card_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(path);

      // Save the image file
      await file.writeAsBytes(pngBytes);

      // Show share button again
      setState(() {
        _isCapturingImage = false;
      });

      return file;
    } catch (e) {
      debugPrint('Error capturing card as image: $e');

      // Make sure to show share button again even if there's an error
      setState(() {
        _isCapturingImage = false;
      });

      return null;
    }
  }

  void _shareCard(BuildContext context, userDetail) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: CircularProgressIndicator(
            color: AppColors.brandBlack,
          ),
        );
      },
    );

    // Capture the card as an image
    File? cardImage = await _captureCardAsImage();

    // Check if widget is still mounted before using context
    if (!mounted) return;

    // Close loading indicator
    Navigator.of(context).pop();

    // Create user profile URL
    String url = "${AppConstants.domainName}${userDetail.userName ?? ""}";

    // Add invite code if available
    if (userDetail.inviteCode != null && userDetail.inviteCode!.isNotEmpty) {
      url = "$url/?ic=${userDetail.inviteCode}";
    }

    // Close the overlay
    Navigator.of(context).pop();

    // Open share screen with user profile and the captured card image
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: url,
        imageLink: userDetail.icon, // Use user profile icon for preview
        imageType: CustomImageContainerType.user,
        entityType: EntityType.USER,
        postCreatorName: userDetail.userName,
        postCreatorIcon: userDetail.icon,
        postText: userDetail.displayName ?? userDetail.firstName,
        objectReference: userDetail.userReference,
        message: "Check out my Swadesic support score card!",
        attachmentImagePath:
            cardImage?.path, // Pass the captured card image path
      ),
      context: context,
    );
  }

  // Helper method to get minimum score for a level
  int _getMinScoreForLevel(int level) {
    final Map<int, int> levelMinScores = {
      1: 0,
      2: 10000,
      3: 50000,
      4: 100000,
      5: 200000,
      6: 500000,
      7: 700000,
      8: 1000000,
      9: 1500000,
    };
    return levelMinScores[level] ?? 0;
  }

  // Build date section
  Widget _buildDateSection() {
    final now = DateTime.now();
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];

    String getOrdinalSuffix(int day) {
      if (day >= 11 && day <= 13) {
        return 'th';
      }
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    }

    final dateString =
        "Dated: ${months[now.month - 1]} ${now.day}${getOrdinalSuffix(now.day)}, ${now.year}";

    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        dateString,
        style: AppTextStyle.smallText(textColor: AppColors.writingBlack0),
      ),
    );
  }
}
