import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/no_internet/no_internet_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';

import '../../../util/app_constants.dart';

class Poem extends StatefulWidget {
  final NoInternetBloc noInternetBloc;

  const Poem({Key? key, required this.noInternetBloc}) : super(key: key);

  @override
  State<Poem> createState() => _PoemState();
}

class _PoemState extends State<Poem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15,vertical: 10),
      width: double.infinity,
      child: AnimatedTextKit(

        onFinished: (){
          //Mark option to true
          widget.noInternetBloc.isOptionVisible = true;
          //refresh
          widget.noInternetBloc.refreshNoInternetCtrl.sink.add(true);
        },

        animatedTexts: [
          TypewriterAnimatedText(
            AppStrings.poemList[widget.noInternetBloc.poemNumber],

            textStyle: TextStyle(
              fontFamily: AppConstants.rRegular,
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack,
              height: 1.56,

            ),
            speed: const Duration(milliseconds:70),

          ),

        ],


        totalRepeatCount: 1,
        // pause: const Duration(milliseconds: 1000),
        // displayFullTextOnTap: true,
        // stopPauseOnTap: true,
      ),
    );
  }
}
